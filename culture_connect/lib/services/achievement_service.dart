// Dart imports
import 'dart:convert';

// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/models/achievement/achievement.dart';
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/notification_service.dart';

/// Enum for user actions that can trigger achievements
enum UserAction {
  // Travel Booking Actions
  firstBooking,
  insurancePurchase,
  visaApplication,
  flightBooking,
  transferBooking,
  hotelBooking,

  // Service Usage Actions
  serviceComparison,
  earlyBooking,
  lastMinuteBooking,
  multiServiceBooking,

  // Engagement Actions
  reviewSubmission,
  referralMade,
  appFeatureUsed,
  profileCompleted,

  // Safety Actions
  documentUploaded,
  notificationEnabled,
  emergencyContactAdded,
}

/// Service for managing user achievements and celebrations
class AchievementService {
  static const String _achievementsKey = 'user_achievements';
  static const String _achievementStatsKey = 'achievement_stats';

  final SharedPreferences _prefs;
  final AnalyticsService _analyticsService;
  final LoggingService _loggingService;
  final NotificationService _notificationService;

  // In-memory cache
  final Map<String, UserAchievement> _userAchievements = {};
  final Map<String, Achievement> _availableAchievements = {};
  bool _isInitialized = false;

  AchievementService({
    required SharedPreferences prefs,
    required AnalyticsService analyticsService,
    required LoggingService loggingService,
    required NotificationService notificationService,
  })  : _prefs = prefs,
        _analyticsService = analyticsService,
        _loggingService = loggingService,
        _notificationService = notificationService;

  /// Initialize the achievement service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadAvailableAchievements();
      await _loadUserAchievements();
      _isInitialized = true;

      _loggingService.info(
          'AchievementService', 'Service initialized successfully');
    } catch (e, stackTrace) {
      _loggingService.error(
        'AchievementService',
        'Failed to initialize service',
        {'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Track a user action and check for achievement progress
  Future<List<UserAchievement>> trackUserAction(
    UserAction action, {
    Map<String, dynamic>? metadata,
  }) async {
    await initialize();

    try {
      final unlockedAchievements = <UserAchievement>[];
      final eligibleAchievements = _getEligibleAchievements(action);

      for (final achievement in eligibleAchievements) {
        final userAchievement = _userAchievements[achievement.id];
        if (userAchievement == null || userAchievement.isUnlocked) continue;

        final points = _calculatePointsForAction(action, achievement);
        if (points <= 0) continue;

        final updatedAchievement = userAchievement.updateProgress(
          points,
          metadata: metadata,
        );

        _userAchievements[achievement.id] = updatedAchievement;

        if (updatedAchievement.canUnlock) {
          final unlockedAchievement = updatedAchievement.unlock();
          _userAchievements[achievement.id] = unlockedAchievement;
          unlockedAchievements.add(unlockedAchievement);

          // Log achievement unlock
          await _analyticsService.logEvent(
            name: 'achievement_unlocked',
            category: AnalyticsCategory.engagement,
            parameters: {
              'achievement_id': achievement.id,
              'achievement_type': achievement.type.toString(),
              'achievement_category': achievement.category.toString(),
              'times_earned': unlockedAchievement.timesEarned,
            },
          );

          _loggingService.info(
            'AchievementService',
            'Achievement unlocked: ${achievement.title}',
            {
              'userId': unlockedAchievement.userId,
              'achievementId': achievement.id
            },
          );
        }
      }

      if (unlockedAchievements.isNotEmpty) {
        await _saveUserAchievements();
        await _updateAchievementStats(unlockedAchievements);
      }

      return unlockedAchievements;
    } catch (e, stackTrace) {
      _loggingService.error(
        'AchievementService',
        'Failed to track user action',
        {'action': action.toString(), 'error': e.toString()},
        stackTrace,
      );
      return [];
    }
  }

  /// Get all user achievements
  List<UserAchievement> getUserAchievements() {
    return _userAchievements.values.toList();
  }

  /// Get user achievements by category
  List<UserAchievement> getUserAchievementsByCategory(
      AchievementCategory category) {
    return _userAchievements.values
        .where((ua) => ua.achievement.category == category)
        .toList();
  }

  /// Get unlocked achievements
  List<UserAchievement> getUnlockedAchievements() {
    return _userAchievements.values.where((ua) => ua.isUnlocked).toList();
  }

  /// Get achievements that need celebration
  List<UserAchievement> getAchievementsNeedingCelebration() {
    return _userAchievements.values.where((ua) => ua.needsCelebration).toList();
  }

  /// Mark achievement as celebrated
  Future<void> markAchievementCelebrated(String achievementId) async {
    final userAchievement = _userAchievements[achievementId];
    if (userAchievement == null) return;

    _userAchievements[achievementId] = userAchievement.markCelebrated();
    await _saveUserAchievements();
  }

  /// Get achievement statistics
  Map<String, dynamic> getAchievementStats() {
    final stats = _prefs.getString(_achievementStatsKey);
    if (stats == null) return {};

    try {
      return Map<String, dynamic>.from(
        // ignore: avoid_dynamic_calls
        (jsonDecode(stats) as Map<String, dynamic>?) ?? {},
      );
    } catch (e) {
      return {};
    }
  }

  /// Load available achievements (mock data for now)
  Future<void> _loadAvailableAchievements() async {
    // TODO: Load from backend API when available
    _availableAchievements.clear();
    _availableAchievements.addAll(_generateMockAchievements());
  }

  /// Load user achievements from storage
  Future<void> _loadUserAchievements() async {
    final achievementsJson = _prefs.getString(_achievementsKey);
    if (achievementsJson == null) {
      await _initializeUserAchievements();
      return;
    }

    try {
      final achievementsList = jsonDecode(achievementsJson) as List<dynamic>;
      _userAchievements.clear();

      for (final achievementJson in achievementsList) {
        final userAchievement = UserAchievement.fromJson(
          achievementJson as Map<String, dynamic>,
        );
        _userAchievements[userAchievement.achievementId] = userAchievement;
      }
    } catch (e) {
      _loggingService.warning(
        'AchievementService',
        'Failed to load user achievements, reinitializing',
        {'error': e.toString()},
      );
      await _initializeUserAchievements();
    }
  }

  /// Initialize user achievements for all available achievements
  Future<void> _initializeUserAchievements() async {
    _userAchievements.clear();
    final now = DateTime.now();

    for (final achievement in _availableAchievements.values) {
      final userAchievement = UserAchievement(
        id: '${achievement.id}_user',
        userId: 'current_user', // TODO: Get from auth service
        achievementId: achievement.id,
        achievement: achievement,
        status: UserAchievementStatus.locked,
        progress: 0.0,
        currentPoints: 0,
        createdAt: now,
        updatedAt: now,
      );

      _userAchievements[achievement.id] = userAchievement;
    }

    await _saveUserAchievements();
  }

  /// Save user achievements to storage
  Future<void> _saveUserAchievements() async {
    try {
      final achievementsList =
          _userAchievements.values.map((ua) => ua.toJson()).toList();

      await _prefs.setString(_achievementsKey, jsonEncode(achievementsList));
    } catch (e) {
      _loggingService.error(
        'AchievementService',
        'Failed to save user achievements',
        {'error': e.toString()},
      );
    }
  }

  /// Update achievement statistics
  Future<void> _updateAchievementStats(
      List<UserAchievement> newAchievements) async {
    final stats = getAchievementStats();

    stats['totalUnlocked'] =
        (stats['totalUnlocked'] as int? ?? 0) + newAchievements.length;
    stats['lastUnlocked'] = DateTime.now().toIso8601String();

    for (final achievement in newAchievements) {
      final categoryKey =
          '${achievement.achievement.category.toString()}_unlocked';
      stats[categoryKey] = (stats[categoryKey] as int? ?? 0) + 1;
    }

    await _prefs.setString(_achievementStatsKey, jsonEncode(stats));
  }

  /// Get eligible achievements for a user action
  List<Achievement> _getEligibleAchievements(UserAction action) {
    return _availableAchievements.values.where((achievement) {
      switch (achievement.type) {
        case AchievementType.firstJourney:
          return action == UserAction.firstBooking;
        case AchievementType.insuranceProtector:
          return action == UserAction.insurancePurchase;
        case AchievementType.worldExplorer:
          return action == UserAction.visaApplication;
        case AchievementType.skyNavigator:
          return action == UserAction.flightBooking;
        case AchievementType.journeyPlanner:
          return action == UserAction.transferBooking;
        case AchievementType.travelMaster:
          return [
            UserAction.insurancePurchase,
            UserAction.visaApplication,
            UserAction.flightBooking,
            UserAction.transferBooking,
            UserAction.hotelBooking,
          ].contains(action);
        default:
          return false;
      }
    }).toList();
  }

  /// Calculate points for a user action
  int _calculatePointsForAction(UserAction action, Achievement achievement) {
    // Base points for different actions
    switch (action) {
      case UserAction.firstBooking:
      case UserAction.insurancePurchase:
      case UserAction.visaApplication:
      case UserAction.flightBooking:
      case UserAction.transferBooking:
      case UserAction.hotelBooking:
        return achievement.pointsRequired; // Complete achievement in one action
      case UserAction.serviceComparison:
        return 10;
      case UserAction.reviewSubmission:
        return 25;
      case UserAction.referralMade:
        return 50;
      default:
        return 5;
    }
  }

  /// Generate mock achievements for development
  Map<String, Achievement> _generateMockAchievements() {
    final now = DateTime.now();

    return {
      'first_journey': Achievement(
        id: 'first_journey',
        type: AchievementType.firstJourney,
        category: AchievementCategory.travelBooking,
        difficulty: AchievementDifficulty.bronze,
        title: 'First Journey',
        description: 'Complete your first booking with CultureConnect',
        icon: Icons.flight_takeoff,
        color: Colors.blue,
        pointsRequired: 100,
        rewardType: AchievementRewardType.points,
        rewardValue: 100,
        tags: ['beginner', 'booking'],
        createdAt: now,
        updatedAt: now,
      ),
      'insurance_protector': Achievement(
        id: 'insurance_protector',
        type: AchievementType.insuranceProtector,
        category: AchievementCategory.safetyFeatures,
        difficulty: AchievementDifficulty.silver,
        title: 'Insurance Protector',
        description: 'Purchase travel insurance for your journey',
        icon: Icons.security,
        color: Colors.green,
        pointsRequired: 150,
        rewardType: AchievementRewardType.discount,
        rewardValue: 5,
        tags: ['safety', 'insurance'],
        createdAt: now,
        updatedAt: now,
      ),
      'world_explorer': Achievement(
        id: 'world_explorer',
        type: AchievementType.worldExplorer,
        category: AchievementCategory.exploration,
        difficulty: AchievementDifficulty.gold,
        title: 'World Explorer',
        description: 'Submit your first visa application',
        icon: Icons.public,
        color: Colors.orange,
        pointsRequired: 200,
        rewardType: AchievementRewardType.points,
        rewardValue: 200,
        tags: ['visa', 'exploration'],
        createdAt: now,
        updatedAt: now,
      ),
    };
  }
}
