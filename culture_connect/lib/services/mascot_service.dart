// Dart imports
import 'dart:async';

// Flutter imports
import 'package:flutter/services.dart';

// Third-party imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/mascot/mascot_state.dart';
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for managing mascot state and context-aware expression selection
class MascotService {
  final LoggingService _loggingService;

  /// Stream controller for mascot state changes
  final StreamController<MascotState> _stateController =
      StreamController<MascotState>.broadcast();

  /// Current mascot state
  MascotState _currentState = MascotState.idle();

  /// Timer for automatic state transitions
  Timer? _stateTimer;

  /// Whether the service is initialized
  bool _isInitialized = false;

  /// Creates a new mascot service
  MascotService(this._loggingService);

  /// Stream of mascot state changes
  Stream<MascotState> get stateStream => _stateController.stream;

  /// Current mascot state
  MascotState get currentState => _currentState;

  /// Initialize the mascot service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _currentState = MascotState.idle();
      _stateController.add(_currentState);
      _isInitialized = true;

      _loggingService.info(
        'MascotService',
        'Mascot service initialized successfully',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'MascotService',
        'Failed to initialize mascot service',
        {'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Update mascot state based on context
  Future<void> updateState(MascotState newState) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Cancel any existing timer
      _stateTimer?.cancel();

      // Update current state
      _currentState = newState.copyWith(timestamp: DateTime.now());

      // Trigger haptic feedback if required
      if (newState.shouldTriggerHaptic) {
        await _triggerHapticFeedback();
      }

      // Emit the new state
      _stateController.add(_currentState);

      // Set timer for automatic state transition if duration is specified
      if (newState.displayDuration != null) {
        _stateTimer = Timer(newState.displayDuration!, () {
          _transitionToIdle();
        });
      }

      _loggingService.info(
        'MascotService',
        'Mascot state updated',
        {
          'expression': newState.expression.displayName,
          'context': newState.context.toString(),
          'hasTimer': newState.displayDuration != null,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'MascotService',
        'Failed to update mascot state',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Handle achievement unlocked event
  Future<void> onAchievementUnlocked(UserAchievement achievement) async {
    final state = MascotState.achievementCelebration(
      achievementId: achievement.achievement.id,
      achievementTitle: achievement.achievement.title,
    );
    await updateState(state);
  }

  /// Handle successful booking event
  Future<void> onBookingSuccessful({
    required String bookingType,
    String? bookingId,
  }) async {
    final state = MascotState.bookingSuccess(
      bookingType: bookingType,
      bookingId: bookingId,
    );
    await updateState(state);
  }

  /// Handle booking failure event
  Future<void> onBookingFailed({
    String? errorMessage,
    String? bookingType,
  }) async {
    final state = MascotState.error(
      errorMessage: errorMessage,
      errorType: 'booking_failed',
    );
    await updateState(state);
  }

  /// Handle loading state
  Future<void> onLoadingStarted({String? message}) async {
    final state = MascotState.loading(loadingMessage: message);
    await updateState(state);
  }

  /// Handle error state
  Future<void> onError({
    required String errorMessage,
    String? errorType,
  }) async {
    final state = MascotState.error(
      errorMessage: errorMessage,
      errorType: errorType,
    );
    await updateState(state);
  }

  /// Handle onboarding step
  Future<void> onOnboardingStep({
    required String step,
    String? message,
  }) async {
    final state = MascotState.onboarding(
      step: step,
      message: message,
    );
    await updateState(state);
  }

  /// Handle discovery event
  Future<void> onDiscovery({
    required String discoveryType,
    String? message,
  }) async {
    final state = MascotState.discovery(
      discoveryType: discoveryType,
      discoveryMessage: message,
    );
    await updateState(state);
  }

  /// Get appropriate expression for context
  MascotExpression getExpressionForContext(MascotContext context) {
    switch (context) {
      case MascotContext.achievementUnlocked:
        return MascotExpression.celebrating;
      case MascotContext.bookingSuccessful:
        return MascotExpression.happy;
      case MascotContext.bookingFailed:
      case MascotContext.error:
        return MascotExpression.sympathetic;
      case MascotContext.loading:
      case MascotContext.onboarding:
      case MascotContext.tutorial:
        return MascotExpression.helpful;
      case MascotContext.discovery:
        return MascotExpression.excited;
      case MascotContext.idle:
        return MascotExpression.helpful;
    }
  }

  /// Transition to idle state
  void _transitionToIdle() {
    if (_currentState.context != MascotContext.idle) {
      final idleState = MascotState.idle();
      _currentState = idleState;
      _stateController.add(_currentState);

      _loggingService.info(
        'MascotService',
        'Transitioned to idle state',
      );
    }
  }

  /// Trigger haptic feedback
  Future<void> _triggerHapticFeedback() async {
    try {
      await HapticFeedback.mediumImpact();
    } catch (e) {
      _loggingService.warning(
        'MascotService',
        'Failed to trigger haptic feedback',
        {'error': e.toString()},
      );
    }
  }

  /// Get mascot state for specific travel service action
  MascotState getStateForTravelAction({
    required String actionType,
    bool isSuccess = true,
    String? message,
    Map<String, dynamic>? metadata,
  }) {
    if (isSuccess) {
      switch (actionType.toLowerCase()) {
        case 'insurance':
          return MascotState.bookingSuccess(
            bookingType: 'Insurance',
            bookingId: metadata?['policyId'],
          );
        case 'flight':
          return MascotState.bookingSuccess(
            bookingType: 'Flight',
            bookingId: metadata?['bookingId'],
          );
        case 'hotel':
          return MascotState.bookingSuccess(
            bookingType: 'Hotel',
            bookingId: metadata?['reservationId'],
          );
        case 'transfer':
          return MascotState.bookingSuccess(
            bookingType: 'Transfer',
            bookingId: metadata?['transferId'],
          );
        case 'car_rental':
          return MascotState.bookingSuccess(
            bookingType: 'Car Rental',
            bookingId: metadata?['rentalId'],
          );
        default:
          return MascotState.bookingSuccess(
            bookingType: actionType,
          );
      }
    } else {
      return MascotState.error(
        errorMessage: message ?? 'Booking failed',
        errorType: '${actionType.toLowerCase()}_failed',
      );
    }
  }

  /// Reset to idle state
  Future<void> resetToIdle() async {
    _stateTimer?.cancel();
    await updateState(MascotState.idle());
  }

  /// Dispose of the service
  void dispose() {
    _stateTimer?.cancel();
    _stateController.close();
    _isInitialized = false;

    _loggingService.info(
      'MascotService',
      'Mascot service disposed',
    );
  }
}

/// Provider for the mascot service
final mascotServiceProvider = Provider<MascotService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final service = MascotService(loggingService);

  // Initialize the service
  service.initialize();

  // Dispose when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
