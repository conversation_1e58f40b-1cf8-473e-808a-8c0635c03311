// Dart imports
import 'dart:async';
import 'dart:convert';

// Flutter imports
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/models/mood/mood_model.dart';
import 'package:culture_connect/models/mascot/mascot_state.dart';
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/mascot_service.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/providers/achievement_provider.dart';

/// Provider for the mood tracking service
final moodTrackingServiceProvider = Provider<MoodTrackingService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final achievementService = ref.watch(achievementServiceProvider);
  final mascotService = ref.watch(mascotServiceProvider);

  return MoodTrackingService(
    prefs: prefs,
    achievementService: achievementService,
    mascotService: mascotService,
  );
});

/// Service for managing mood tracking functionality
class MoodTrackingService {
  final SharedPreferences _prefs;
  final AchievementService _achievementService;
  final MascotService _mascotService;

  static const String _moodEntriesKey = 'mood_entries';
  static const String _moodPreferencesKey = 'mood_preferences';
  static const String _lastMoodPromptKey = 'last_mood_prompt';
  static const String _consentGivenKey = 'mood_tracking_consent';

  /// Creates a new mood tracking service
  MoodTrackingService({
    required SharedPreferences prefs,
    required AchievementService achievementService,
    required MascotService mascotService,
  })  : _prefs = prefs,
        _achievementService = achievementService,
        _mascotService = mascotService;

  /// Request user consent for mood tracking
  Future<bool> requestTrackingConsent() async {
    try {
      // In a real app, this would show a consent dialog
      // For now, we'll assume consent is given
      await _prefs.setBool(_consentGivenKey, true);
      return true;
    } catch (e) {
      debugPrint('Error requesting mood tracking consent: $e');
      return false;
    }
  }

  /// Check if user has given consent for mood tracking
  bool get hasTrackingConsent {
    return _prefs.getBool(_consentGivenKey) ?? false;
  }

  /// Get current mood preferences
  MoodPreferences get preferences {
    final prefsJson = _prefs.getString(_moodPreferencesKey);
    if (prefsJson == null) {
      return const MoodPreferences();
    }

    try {
      final prefsMap = jsonDecode(prefsJson) as Map<String, dynamic>;
      return MoodPreferences.fromJson(prefsMap);
    } catch (e) {
      debugPrint('Error loading mood preferences: $e');
      return const MoodPreferences();
    }
  }

  /// Update mood preferences
  Future<void> updatePreferences(MoodPreferences newPreferences) async {
    try {
      final prefsJson = jsonEncode(newPreferences.toJson());
      await _prefs.setString(_moodPreferencesKey, prefsJson);

      // Update notification schedule if preferences changed
      if (newPreferences.notificationsEnabled) {
        await _scheduleNextMoodReminder();
      }
    } catch (e) {
      debugPrint('Error updating mood preferences: $e');
    }
  }

  /// Record a new mood entry
  Future<MoodEntry?> recordMood(
    MoodType mood, {
    String? experienceId,
    String? bookingId,
    String? notes,
    Map<String, dynamic>? context,
  }) async {
    if (!hasTrackingConsent || !preferences.trackingEnabled) {
      return null;
    }

    try {
      final entry = MoodEntry.create(
        mood: mood,
        experienceId: experienceId,
        bookingId: bookingId,
        notes: notes,
        context: context,
      );

      // Save the mood entry
      await _saveMoodEntry(entry);

      // Trigger haptic feedback
      await HapticFeedback.mediumImpact();

      // Update mascot based on mood
      if (preferences.shareWithMascot) {
        await _updateMascotBasedOnMood(mood);
      }

      // Check for achievements
      await _checkMoodTrackingAchievements(entry);

      return entry;
    } catch (e) {
      debugPrint('Error recording mood: $e');
      return null;
    }
  }

  /// Get mood history for a date range
  Future<List<MoodEntry>> getMoodHistory({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final entries = await _loadMoodEntries();

      if (startDate == null && endDate == null) {
        return entries;
      }

      return entries.where((entry) {
        if (startDate != null && entry.timestamp.isBefore(startDate)) {
          return false;
        }
        if (endDate != null && entry.timestamp.isAfter(endDate)) {
          return false;
        }
        return true;
      }).toList();
    } catch (e) {
      debugPrint('Error getting mood history: $e');
      return [];
    }
  }

  /// Analyze mood trend for a given period
  Future<MoodTrend> analyzeMoodTrend(Duration period) async {
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(period);

      final entries = await getMoodHistory(
        startDate: startDate,
        endDate: endDate,
      );

      // Get previous period for comparison
      final previousStartDate = startDate.subtract(period);
      final previousEntries = await getMoodHistory(
        startDate: previousStartDate,
        endDate: startDate,
      );

      final previousAverage = _calculateAverageScore(previousEntries);

      return MoodTrend.fromEntries(
        startDate: startDate,
        endDate: endDate,
        entries: entries,
        previousPeriodAverage: previousAverage,
      );
    } catch (e) {
      debugPrint('Error analyzing mood trend: $e');
      return MoodTrend(
        startDate: DateTime.now().subtract(period),
        endDate: DateTime.now(),
        moodDistribution: {},
        averageMoodScore: 0.0,
        improvementPercentage: 0.0,
        entries: [],
      );
    }
  }

  /// Check if we should prompt for mood based on experience completion
  Future<bool> shouldPromptForMood(String? experienceId) async {
    if (!hasTrackingConsent || !preferences.trackingEnabled) {
      return false;
    }

    // Check if we've already prompted today
    final lastPrompt = _prefs.getInt(_lastMoodPromptKey);
    if (lastPrompt != null) {
      final lastPromptDate = DateTime.fromMillisecondsSinceEpoch(lastPrompt);
      final today = DateTime.now();
      if (lastPromptDate.day == today.day &&
          lastPromptDate.month == today.month &&
          lastPromptDate.year == today.year) {
        return false; // Already prompted today
      }
    }

    // Check if we have a recent mood entry for this experience
    if (experienceId != null) {
      final entries = await getMoodHistory();
      final recentEntry = entries
          .where((entry) =>
              entry.experienceId == experienceId &&
              DateTime.now().difference(entry.timestamp).inHours < 24)
          .firstOrNull;

      if (recentEntry != null) {
        return false; // Already recorded mood for this experience
      }
    }

    return true;
  }

  /// Schedule next mood reminder notification
  Future<void> _scheduleNextMoodReminder() async {
    if (!preferences.notificationsEnabled) return;

    try {
      final nextReminder = DateTime.now().add(preferences.reminderFrequency);

      // This would integrate with the notification service
      // For now, we'll just store the next reminder time
      await _prefs.setInt(
          'next_mood_reminder', nextReminder.millisecondsSinceEpoch);
    } catch (e) {
      debugPrint('Error scheduling mood reminder: $e');
    }
  }

  /// Update mascot expression based on mood
  Future<void> _updateMascotBasedOnMood(MoodType mood) async {
    try {
      switch (mood) {
        case MoodType.veryHappy:
        case MoodType.happy:
          await _mascotService.updateState(
            MascotState(
              expression: MascotExpression.happy,
              context: MascotContext.idle,
              metadata: {'mood': mood.displayName, 'source': 'mood_tracking'},
            ),
          );
          break;
        case MoodType.neutral:
          await _mascotService.updateState(
            MascotState(
              expression: MascotExpression.helpful,
              context: MascotContext.idle,
              metadata: {'mood': mood.displayName, 'source': 'mood_tracking'},
            ),
          );
          break;
        case MoodType.sad:
        case MoodType.verySad:
          await _mascotService.updateState(
            MascotState(
              expression: MascotExpression.sympathetic,
              context: MascotContext.idle,
              metadata: {'mood': mood.displayName, 'source': 'mood_tracking'},
            ),
          );
          break;
      }
    } catch (e) {
      debugPrint('Error updating mascot based on mood: $e');
    }
  }

  /// Check for mood tracking achievements
  Future<void> _checkMoodTrackingAchievements(MoodEntry entry) async {
    try {
      final entries = await _loadMoodEntries();

      // First mood achievement - use appFeatureUsed as closest match
      if (entries.length == 1) {
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {
            'feature': 'mood_tracking',
            'mood': entry.mood.displayName,
            'isFirstMood': true,
          },
        );
      }

      // Weekly streak achievement - use appFeatureUsed for engagement
      if (_hasWeeklyStreak(entries)) {
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {
            'feature': 'mood_tracking',
            'streakDays': 7,
            'achievementType': 'weekly_streak',
          },
        );
      }

      // Mood improvement achievement - use profileCompleted for progress
      final trend = await analyzeMoodTrend(const Duration(days: 30));
      if (trend.isImproving && trend.improvementPercentage > 10) {
        await _achievementService.trackUserAction(
          UserAction.profileCompleted,
          metadata: {
            'feature': 'mood_improvement',
            'improvementPercentage': trend.improvementPercentage,
            'achievementType': 'mood_improvement',
          },
        );
      }
    } catch (e) {
      debugPrint('Error checking mood tracking achievements: $e');
    }
  }

  /// Save mood entry to local storage
  Future<void> _saveMoodEntry(MoodEntry entry) async {
    try {
      final entries = await _loadMoodEntries();
      entries.add(entry);

      // Clean up old entries based on retention policy
      final retentionDate = DateTime.now()
          .subtract(Duration(days: preferences.dataRetentionDays));
      entries.removeWhere((e) => e.timestamp.isBefore(retentionDate));

      final entriesJson = entries.map((e) => e.toJson()).toList();
      await _prefs.setString(_moodEntriesKey, jsonEncode(entriesJson));
    } catch (e) {
      debugPrint('Error saving mood entry: $e');
    }
  }

  /// Load mood entries from local storage
  Future<List<MoodEntry>> _loadMoodEntries() async {
    try {
      final entriesJson = _prefs.getString(_moodEntriesKey);
      if (entriesJson == null) return [];

      final entriesList = jsonDecode(entriesJson) as List;
      return entriesList
          .map((json) => MoodEntry.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error loading mood entries: $e');
      return [];
    }
  }

  /// Calculate average mood score from entries
  double _calculateAverageScore(List<MoodEntry> entries) {
    if (entries.isEmpty) return 0.0;

    final totalScore = entries.fold<double>(
      0.0,
      (sum, entry) => sum + entry.mood.score,
    );

    return totalScore / entries.length;
  }

  /// Check if user has a weekly mood tracking streak
  bool _hasWeeklyStreak(List<MoodEntry> entries) {
    if (entries.length < 7) return false;

    final sortedEntries = List<MoodEntry>.from(entries)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    final today = DateTime.now();
    for (int i = 0; i < 7; i++) {
      final targetDate = today.subtract(Duration(days: i));
      final hasEntryForDay = sortedEntries.any((entry) =>
          entry.timestamp.day == targetDate.day &&
          entry.timestamp.month == targetDate.month &&
          entry.timestamp.year == targetDate.year);

      if (!hasEntryForDay) return false;
    }

    return true;
  }
}
