// Flutter imports
import 'dart:async';
import 'dart:convert';

// Package imports
import 'package:http/http.dart' as http;

// Project imports
import 'package:culture_connect/models/travel/document/visa_requirement.dart';
import 'package:culture_connect/services/cache_service.dart';
import 'package:culture_connect/services/connectivity_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for integrating with Travel Buddy Visa Requirements API
/// Provides fast, accurate, and up-to-date visa information for 200+ passports and 210+ destinations
class TravelBuddyApiService {
  // Singleton instance
  static final TravelBuddyApiService _instance =
      TravelBuddyApiService._internal();
  factory TravelBuddyApiService() => _instance;
  TravelBuddyApiService._internal();

  // Dependencies
  final LoggingService _loggingService = LoggingService();
  final CacheService _cacheService = CacheService();
  final ConnectivityService _connectivityService = ConnectivityService();

  // HTTP client
  final http.Client _client = http.Client();

  // API configuration
  static const String _baseUrl = 'https://api.travelbuddy.com/v1';
  static const String _apiKey =
      'your_travel_buddy_api_key'; // TODO: Move to environment config
  static const Duration _requestTimeout = Duration(seconds: 10);

  // Cache keys
  static const String _cachePrefix = 'travel_buddy_visa_';

  /// Get visa requirements for a specific passport and destination combination
  Future<VisaRequirement?> getVisaRequirements({
    required String passportCountry,
    required String destinationCountry,
  }) async {
    try {
      _loggingService.debug(
        'TravelBuddyApiService',
        'Getting visa requirements: $passportCountry → $destinationCountry',
      );

      // Generate cache key
      final cacheKey = '$_cachePrefix${passportCountry}_$destinationCountry';

      // Check cache first
      final cachedData = await _getCachedRequirement(cacheKey);
      if (cachedData != null) {
        _loggingService.debug(
          'TravelBuddyApiService',
          'Returning cached visa requirement',
        );
        return cachedData;
      }

      // Check connectivity
      final isConnected = await _connectivityService.isConnected();
      if (!isConnected) {
        _loggingService.warning(
          'TravelBuddyApiService',
          'No internet connection, returning null',
        );
        return null;
      }

      // Make API request
      final requirement =
          await _fetchVisaRequirement(passportCountry, destinationCountry);

      if (requirement != null) {
        // Cache the result
        await _cacheRequirement(cacheKey, requirement);

        _loggingService.debug(
          'TravelBuddyApiService',
          'Successfully fetched and cached visa requirement',
        );
      }

      return requirement;
    } catch (e, stackTrace) {
      _loggingService.error(
        'TravelBuddyApiService',
        'Error getting visa requirements',
        e,
        stackTrace,
      );
      return null;
    }
  }

  /// Get visa requirements for multiple destinations (batch processing)
  Future<List<VisaRequirement>> getBatchVisaRequirements({
    required String passportCountry,
    required List<String> destinations,
  }) async {
    try {
      _loggingService.debug(
        'TravelBuddyApiService',
        'Getting batch visa requirements for ${destinations.length} destinations',
      );

      final results = <VisaRequirement>[];

      // Process destinations in parallel with a limit to avoid overwhelming the API
      const batchSize = 5;
      for (int i = 0; i < destinations.length; i += batchSize) {
        final batch = destinations.skip(i).take(batchSize);
        final batchFutures = batch.map((destination) => getVisaRequirements(
              passportCountry: passportCountry,
              destinationCountry: destination,
            ));

        final batchResults = await Future.wait(batchFutures);
        results.addAll(batchResults.whereType<VisaRequirement>());

        // Small delay between batches to be respectful to the API
        if (i + batchSize < destinations.length) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      return results;
    } catch (e, stackTrace) {
      _loggingService.error(
        'TravelBuddyApiService',
        'Error getting batch visa requirements',
        e,
        stackTrace,
      );
      return [];
    }
  }

  /// Get list of supported countries for passport selection
  Future<List<Country>> getSupportedCountries() async {
    try {
      const cacheKey = '${_cachePrefix}supported_countries';

      // Check cache first (longer cache for country list)
      final cachedData = await _cacheService.getData(cacheKey);
      if (cachedData != null) {
        final List<dynamic> jsonList = jsonDecode(cachedData);
        return jsonList.map((json) => Country.fromJson(json)).toList();
      }

      // Check connectivity
      final isConnected = await _connectivityService.isConnected();
      if (!isConnected) {
        return _getMockCountries(); // Fallback to mock data
      }

      // Make API request
      final response = await _client
          .get(
            Uri.parse('$_baseUrl/countries'),
            headers: _getHeaders(),
          )
          .timeout(_requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = jsonDecode(response.body);
        final countries =
            jsonData.map((json) => Country.fromJson(json)).toList();

        // Cache the result
        await _cacheService.saveData(cacheKey, response.body);

        return countries;
      } else {
        throw Exception('Failed to load countries: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'TravelBuddyApiService',
        'Error getting supported countries',
        e,
        stackTrace,
      );
      return _getMockCountries();
    }
  }

  /// Fetch visa requirement from Travel Buddy API
  Future<VisaRequirement?> _fetchVisaRequirement(
    String passportCountry,
    String destinationCountry,
  ) async {
    try {
      final uri =
          Uri.parse('$_baseUrl/visa-requirements').replace(queryParameters: {
        'passport_country': passportCountry,
        'destination_country': destinationCountry,
      });

      final response = await _client
          .get(
            uri,
            headers: _getHeaders(),
          )
          .timeout(_requestTimeout);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);
        return VisaRequirement.fromTravelBuddyApi(jsonData);
      } else if (response.statusCode == 404) {
        _loggingService.warning(
          'TravelBuddyApiService',
          'No visa requirement found for $passportCountry → $destinationCountry',
        );
        return null;
      } else {
        throw Exception('API request failed: ${response.statusCode}');
      }
    } catch (e) {
      _loggingService.error(
        'TravelBuddyApiService',
        'Error fetching visa requirement from API',
        e,
      );
      rethrow;
    }
  }

  /// Get cached visa requirement
  Future<VisaRequirement?> _getCachedRequirement(String cacheKey) async {
    try {
      final cachedData = await _cacheService.getData(cacheKey);
      if (cachedData != null) {
        final Map<String, dynamic> jsonData = jsonDecode(cachedData);
        return VisaRequirement.fromJson(jsonData);
      }
      return null;
    } catch (e) {
      _loggingService.warning(
        'TravelBuddyApiService',
        'Error reading cached visa requirement: $e',
      );
      return null;
    }
  }

  /// Cache visa requirement
  Future<void> _cacheRequirement(
      String cacheKey, VisaRequirement requirement) async {
    try {
      await _cacheService.saveData(
        cacheKey,
        jsonEncode(requirement.toJson()),
      );
    } catch (e) {
      _loggingService.warning(
        'TravelBuddyApiService',
        'Error caching visa requirement: $e',
      );
    }
  }

  /// Get HTTP headers for API requests
  Map<String, String> _getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $_apiKey',
      'User-Agent': 'CultureConnect/1.0',
    };
  }

  /// Get mock countries for fallback
  List<Country> _getMockCountries() {
    return [
      const Country(code: 'US', name: 'United States', flagUrl: '🇺🇸'),
      const Country(code: 'GB', name: 'United Kingdom', flagUrl: '🇬🇧'),
      const Country(code: 'CA', name: 'Canada', flagUrl: '🇨🇦'),
      const Country(code: 'AU', name: 'Australia', flagUrl: '🇦🇺'),
      const Country(code: 'DE', name: 'Germany', flagUrl: '🇩🇪'),
      const Country(code: 'FR', name: 'France', flagUrl: '🇫🇷'),
      const Country(code: 'JP', name: 'Japan', flagUrl: '🇯🇵'),
      const Country(code: 'CN', name: 'China', flagUrl: '🇨🇳'),
      const Country(code: 'IN', name: 'India', flagUrl: '🇮🇳'),
      const Country(code: 'BR', name: 'Brazil', flagUrl: '🇧🇷'),
    ];
  }

  /// Dispose resources
  void dispose() {
    _client.close();
  }
}

/// Country model for passport/destination selection
class Country {
  final String code;
  final String name;
  final String flagUrl;

  const Country({
    required this.code,
    required this.name,
    required this.flagUrl,
  });

  factory Country.fromJson(Map<String, dynamic> json) {
    return Country(
      code: json['code'] as String,
      name: json['name'] as String,
      flagUrl: json['flag_url'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'flag_url': flagUrl,
    };
  }
}
