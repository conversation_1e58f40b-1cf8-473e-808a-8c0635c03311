// Flutter imports
import 'dart:convert';

// Package imports
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/models/travel/document/visa_requirement.dart';
import 'package:culture_connect/services/connectivity_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for offline visa requirements capability
class VisaOfflineService {
  /// Singleton instance
  static final VisaOfflineService _instance = VisaOfflineService._internal();

  /// Factory constructor
  factory VisaOfflineService() => _instance;

  /// Internal constructor
  VisaOfflineService._internal();

  // Dependencies
  final ConnectivityService _connectivityService = ConnectivityService();
  final LoggingService _loggingService = LoggingService();

  // State
  SharedPreferences? _prefs;
  bool _isInitialized = false;
  final Map<String, VisaRequirement> _cachedRequirements = {};
  final Map<String, DateTime> _cacheTimestamps = {};

  // Cache configuration
  static const Duration _cacheExpiry = Duration(hours: 24);
  static const String _cacheKeyPrefix = 'visa_requirement_';
  static const String _timestampKeyPrefix = 'visa_timestamp_';
  static const String _popularCountriesKey = 'popular_visa_countries';
  static const String _lastSyncKey = 'visa_last_sync';

  /// Initialize the offline service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadCachedRequirements();
      _isInitialized = true;

      _loggingService.info(
          'VisaOfflineService', 'Service initialized successfully');
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaOfflineService',
        'Error initializing offline service',
        e,
        stackTrace,
      );
      _isInitialized = true; // Continue with empty cache
    }
  }

  /// Cache a visa requirement for offline access
  Future<void> cacheVisaRequirement(
    String countryFrom,
    String countryTo,
    VisaRequirement requirement,
  ) async {
    await initialize();

    try {
      final cacheKey = _getCacheKey(countryFrom, countryTo);
      final timestampKey = _getTimestampKey(countryFrom, countryTo);
      final now = DateTime.now();

      // Store in memory cache
      _cachedRequirements[cacheKey] = requirement;
      _cacheTimestamps[cacheKey] = now;

      // Store in persistent cache
      await _prefs?.setString(cacheKey, jsonEncode(requirement.toJson()));
      await _prefs?.setString(timestampKey, now.toIso8601String());

      _loggingService.info(
        'VisaOfflineService',
        'Cached visa requirement: $countryFrom -> $countryTo',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaOfflineService',
        'Error caching visa requirement',
        e,
        stackTrace,
      );
    }
  }

  /// Get cached visa requirement
  Future<VisaRequirement?> getCachedVisaRequirement(
    String countryFrom,
    String countryTo,
  ) async {
    await initialize();

    try {
      final cacheKey = _getCacheKey(countryFrom, countryTo);

      // Check memory cache first
      if (_cachedRequirements.containsKey(cacheKey)) {
        final timestamp = _cacheTimestamps[cacheKey];
        if (timestamp != null && _isCacheValid(timestamp)) {
          _loggingService.info(
            'VisaOfflineService',
            'Retrieved from memory cache: $countryFrom -> $countryTo',
          );
          return _cachedRequirements[cacheKey];
        } else {
          // Remove expired cache
          _cachedRequirements.remove(cacheKey);
          _cacheTimestamps.remove(cacheKey);
        }
      }

      // Check persistent cache
      final cachedJson = _prefs?.getString(cacheKey);
      final timestampString =
          _prefs?.getString(_getTimestampKey(countryFrom, countryTo));

      if (cachedJson != null && timestampString != null) {
        final timestamp = DateTime.parse(timestampString);
        if (_isCacheValid(timestamp)) {
          final requirement = VisaRequirement.fromJson(jsonDecode(cachedJson));

          // Update memory cache
          _cachedRequirements[cacheKey] = requirement;
          _cacheTimestamps[cacheKey] = timestamp;

          _loggingService.info(
            'VisaOfflineService',
            'Retrieved from persistent cache: $countryFrom -> $countryTo',
          );
          return requirement;
        } else {
          // Remove expired cache
          await _removeCachedRequirement(countryFrom, countryTo);
        }
      }

      return null;
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaOfflineService',
        'Error retrieving cached visa requirement',
        e,
        stackTrace,
      );
      return null;
    }
  }

  /// Check if device is offline
  Future<bool> isOffline() async {
    return !(await _connectivityService.isConnected());
  }

  /// Get all cached visa requirements
  Future<List<VisaRequirement>> getAllCachedRequirements() async {
    await initialize();

    final validRequirements = <VisaRequirement>[];

    try {
      for (final entry in _cachedRequirements.entries) {
        final timestamp = _cacheTimestamps[entry.key];
        if (timestamp != null && _isCacheValid(timestamp)) {
          validRequirements.add(entry.value);
        }
      }

      _loggingService.info(
        'VisaOfflineService',
        'Retrieved ${validRequirements.length} cached requirements',
      );

      return validRequirements;
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaOfflineService',
        'Error retrieving all cached requirements',
        e,
        stackTrace,
      );
      return [];
    }
  }

  /// Preload popular visa requirements for offline access
  Future<void> preloadPopularRequirements(List<String> popularCountries) async {
    await initialize();

    try {
      // Store popular countries list
      await _prefs?.setStringList(_popularCountriesKey, popularCountries);

      _loggingService.info(
        'VisaOfflineService',
        'Stored ${popularCountries.length} popular countries for preloading',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaOfflineService',
        'Error preloading popular requirements',
        e,
        stackTrace,
      );
    }
  }

  /// Get popular countries for preloading
  Future<List<String>> getPopularCountries() async {
    await initialize();

    try {
      final popularCountries =
          _prefs?.getStringList(_popularCountriesKey) ?? [];
      return popularCountries;
    } catch (e) {
      _loggingService.warning(
        'VisaOfflineService',
        'Error getting popular countries: $e',
      );
      return [];
    }
  }

  /// Clear expired cache entries
  Future<void> clearExpiredCache() async {
    await initialize();

    try {
      final keysToRemove = <String>[];

      // Check memory cache
      for (final entry in _cacheTimestamps.entries) {
        if (!_isCacheValid(entry.value)) {
          keysToRemove.add(entry.key);
        }
      }

      // Remove from memory cache
      for (final key in keysToRemove) {
        _cachedRequirements.remove(key);
        _cacheTimestamps.remove(key);
      }

      // Check persistent cache
      final allKeys = _prefs?.getKeys() ?? <String>{};
      for (final key in allKeys) {
        if (key.startsWith(_timestampKeyPrefix)) {
          final timestampString = _prefs?.getString(key);
          if (timestampString != null) {
            final timestamp = DateTime.parse(timestampString);
            if (!_isCacheValid(timestamp)) {
              final cacheKey =
                  key.replaceFirst(_timestampKeyPrefix, _cacheKeyPrefix);
              await _prefs?.remove(key);
              await _prefs?.remove(cacheKey);
            }
          }
        }
      }

      _loggingService.info(
        'VisaOfflineService',
        'Cleared ${keysToRemove.length} expired cache entries',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaOfflineService',
        'Error clearing expired cache',
        e,
        stackTrace,
      );
    }
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStatistics() async {
    await initialize();

    try {
      final totalCached = _cachedRequirements.length;
      final validCached = _cachedRequirements.entries.where((entry) {
        final timestamp = _cacheTimestamps[entry.key];
        return timestamp != null && _isCacheValid(timestamp);
      }).length;

      final lastSyncString = _prefs?.getString(_lastSyncKey);
      final lastSync =
          lastSyncString != null ? DateTime.parse(lastSyncString) : null;

      return {
        'totalCached': totalCached,
        'validCached': validCached,
        'expiredCached': totalCached - validCached,
        'lastSync': lastSync?.toIso8601String(),
        'cacheExpiry': _cacheExpiry.inHours,
      };
    } catch (e) {
      _loggingService.warning(
        'VisaOfflineService',
        'Error getting cache statistics: $e',
      );
      return {};
    }
  }

  /// Update last sync timestamp
  Future<void> updateLastSync() async {
    await initialize();
    await _prefs?.setString(_lastSyncKey, DateTime.now().toIso8601String());
  }

  /// Load cached requirements from persistent storage
  Future<void> _loadCachedRequirements() async {
    try {
      final allKeys = _prefs?.getKeys() ?? <String>{};

      for (final key in allKeys) {
        if (key.startsWith(_cacheKeyPrefix)) {
          final timestampKey =
              key.replaceFirst(_cacheKeyPrefix, _timestampKeyPrefix);
          final cachedJson = _prefs?.getString(key);
          final timestampString = _prefs?.getString(timestampKey);

          if (cachedJson != null && timestampString != null) {
            final timestamp = DateTime.parse(timestampString);
            if (_isCacheValid(timestamp)) {
              final requirement =
                  VisaRequirement.fromJson(jsonDecode(cachedJson));
              _cachedRequirements[key] = requirement;
              _cacheTimestamps[key] = timestamp;
            }
          }
        }
      }

      _loggingService.info(
        'VisaOfflineService',
        'Loaded ${_cachedRequirements.length} cached requirements',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaOfflineService',
        'Error loading cached requirements',
        e,
        stackTrace,
      );
    }
  }

  /// Remove a cached requirement
  Future<void> _removeCachedRequirement(
      String countryFrom, String countryTo) async {
    final cacheKey = _getCacheKey(countryFrom, countryTo);
    final timestampKey = _getTimestampKey(countryFrom, countryTo);

    _cachedRequirements.remove(cacheKey);
    _cacheTimestamps.remove(cacheKey);
    await _prefs?.remove(cacheKey);
    await _prefs?.remove(timestampKey);
  }

  /// Generate cache key for a country pair
  String _getCacheKey(String countryFrom, String countryTo) {
    return '$_cacheKeyPrefix${countryFrom}_$countryTo';
  }

  /// Generate timestamp key for a country pair
  String _getTimestampKey(String countryFrom, String countryTo) {
    return '$_timestampKeyPrefix${countryFrom}_$countryTo';
  }

  /// Check if cache entry is still valid
  bool _isCacheValid(DateTime timestamp) {
    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }
}
