// Flutter imports
import 'package:flutter/foundation.dart';

// Project imports
import 'package:culture_connect/models/travel/document/visa_requirement.dart';
import 'package:culture_connect/models/travel/document/visa_service_provider.dart';
import 'package:culture_connect/services/travel/visa/visa_provider_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Enum for visa service routing recommendations
enum VisaServiceRoute {
  /// User can handle visa application independently
  selfService,

  /// Provider assistance is recommended
  providerAssisted,

  /// Hybrid approach (self-service with optional provider consultation)
  hybrid,

  /// Provider assistance is required (complex cases)
  providerRequired,
}

/// Extension for visa service route display
extension VisaServiceRouteExtension on VisaServiceRoute {
  /// Display name for the route
  String get displayName {
    switch (this) {
      case VisaServiceRoute.selfService:
        return 'Self-Service';
      case VisaServiceRoute.providerAssisted:
        return 'Provider Assisted';
      case VisaServiceRoute.hybrid:
        return 'Hybrid Approach';
      case VisaServiceRoute.providerRequired:
        return 'Provider Required';
    }
  }

  /// Description for the route
  String get description {
    switch (this) {
      case VisaServiceRoute.selfService:
        return 'You can handle this visa application independently with our guidance.';
      case VisaServiceRoute.providerAssisted:
        return 'We recommend using a visa service provider for better success rates.';
      case VisaServiceRoute.hybrid:
        return 'Start with self-service and get provider help if needed.';
      case VisaServiceRoute.providerRequired:
        return 'This visa type requires professional assistance for best results.';
    }
  }

  /// Icon name for the route
  String get iconName {
    switch (this) {
      case VisaServiceRoute.selfService:
        return 'person';
      case VisaServiceRoute.providerAssisted:
        return 'support_agent';
      case VisaServiceRoute.hybrid:
        return 'group_work';
      case VisaServiceRoute.providerRequired:
        return 'business_center';
    }
  }
}

/// Model representing a visa routing recommendation
class VisaRoutingRecommendation {
  /// Recommended service route
  final VisaServiceRoute route;

  /// Confidence score (0-100)
  final double confidence;

  /// Reasoning for the recommendation
  final String reasoning;

  /// Recommended providers (if applicable)
  final List<VisaServiceProvider> recommendedProviders;

  /// Estimated cost range
  final String estimatedCostRange;

  /// Estimated processing time
  final String estimatedProcessingTime;

  /// Success rate estimate
  final double estimatedSuccessRate;

  /// Additional considerations
  final List<String> considerations;

  /// Creates a new visa routing recommendation
  const VisaRoutingRecommendation({
    required this.route,
    required this.confidence,
    required this.reasoning,
    required this.recommendedProviders,
    required this.estimatedCostRange,
    required this.estimatedProcessingTime,
    required this.estimatedSuccessRate,
    required this.considerations,
  });
}

/// Service for intelligent visa service routing
class VisaRoutingService {
  /// Singleton instance
  static final VisaRoutingService _instance = VisaRoutingService._internal();

  /// Factory constructor
  factory VisaRoutingService() => _instance;

  /// Internal constructor
  VisaRoutingService._internal();

  // Dependencies
  final VisaProviderService _providerService = VisaProviderService();
  final LoggingService _loggingService = LoggingService();

  /// Get routing recommendation for a visa requirement
  Future<VisaRoutingRecommendation> getRoutingRecommendation({
    required VisaRequirement visaRequirement,
    required String passportCountry,
    Map<String, dynamic>? userProfile,
  }) async {
    try {
      // Analyze visa complexity
      final complexity = _analyzeVisaComplexity(visaRequirement);

      // Get user experience level
      final userExperience = _getUserExperienceLevel(userProfile);

      // Determine base route
      final baseRoute =
          _determineBaseRoute(complexity, userExperience, visaRequirement);

      // Get recommended providers
      final providers =
          await _getRecommendedProviders(visaRequirement, baseRoute);

      // Calculate confidence and estimates
      final confidence =
          _calculateConfidence(complexity, userExperience, providers.length);
      final estimates =
          _calculateEstimates(visaRequirement, providers, baseRoute);

      // Generate reasoning and considerations
      final reasoning =
          _generateReasoning(baseRoute, complexity, userExperience);
      final considerations =
          _generateConsiderations(visaRequirement, baseRoute);

      return VisaRoutingRecommendation(
        route: baseRoute,
        confidence: confidence,
        reasoning: reasoning,
        recommendedProviders: providers,
        estimatedCostRange: estimates['costRange'] as String,
        estimatedProcessingTime: estimates['processingTime'] as String,
        estimatedSuccessRate: estimates['successRate'] as double,
        considerations: considerations,
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaRoutingService',
        'Error generating routing recommendation',
        e,
        stackTrace,
      );

      // Return fallback recommendation
      return _getFallbackRecommendation(visaRequirement);
    }
  }

  /// Analyze visa complexity level
  int _analyzeVisaComplexity(VisaRequirement visaRequirement) {
    int complexity = 0;

    // Base complexity by visa type
    switch (visaRequirement.requirementType) {
      case VisaRequirementType.noVisaRequired:
      case VisaRequirementType.visaExemption:
        complexity = 0;
        break;
      case VisaRequirementType.eVisa:
        complexity = 2;
        break;
      case VisaRequirementType.visaOnArrival:
        complexity = 3;
        break;
      case VisaRequirementType.visaRequired:
        complexity = 5;
        break;
      case VisaRequirementType.specialPermit:
        complexity = 8;
        break;
    }

    // Add complexity for document requirements
    complexity += (visaRequirement.requiredDocuments.length * 0.5).round();

    // Add complexity for processing time
    if (visaRequirement.processingTime != null) {
      if (visaRequirement.processingTime! > 30) {
        complexity += 2;
      } else if (visaRequirement.processingTime! > 14) {
        complexity += 1;
      }
    }

    // Add complexity for high fees
    if (visaRequirement.visaFee != null && visaRequirement.visaFee! > 200) {
      complexity += 1;
    }

    return complexity.clamp(0, 10);
  }

  /// Get user experience level from profile
  int _getUserExperienceLevel(Map<String, dynamic>? userProfile) {
    if (userProfile == null) return 1; // Beginner by default

    // Check travel history
    final travelHistory = userProfile['travelHistory'] as List<dynamic>? ?? [];
    final visaApplications =
        userProfile['visaApplications'] as List<dynamic>? ?? [];

    int experience = 1;

    // Experience from travel history
    if (travelHistory.length > 10) {
      experience += 2;
    } else if (travelHistory.length > 5) {
      experience += 1;
    }

    // Experience from visa applications
    if (visaApplications.length > 5) {
      experience += 2;
    } else if (visaApplications.length > 2) {
      experience += 1;
    }

    // Check for successful complex visa applications
    final complexVisas = visaApplications
        .where((app) =>
            app['type'] == 'work' ||
            app['type'] == 'student' ||
            app['type'] == 'business')
        .length;
    if (complexVisas > 0) {
      experience += 1;
    }

    return experience.clamp(1, 5);
  }

  /// Determine base routing recommendation
  VisaServiceRoute _determineBaseRoute(
    int complexity,
    int userExperience,
    VisaRequirement visaRequirement,
  ) {
    // No visa required cases
    if (visaRequirement.requirementType == VisaRequirementType.noVisaRequired ||
        visaRequirement.requirementType == VisaRequirementType.visaExemption) {
      return VisaServiceRoute.selfService;
    }

    // Simple cases (eVisa, visa on arrival) with experienced users
    if (complexity <= 3 && userExperience >= 3) {
      return VisaServiceRoute.selfService;
    }

    // Complex cases always need provider assistance
    if (complexity >= 8) {
      return VisaServiceRoute.providerRequired;
    }

    // Medium complexity cases
    if (complexity >= 5) {
      if (userExperience >= 4) {
        return VisaServiceRoute.hybrid;
      } else {
        return VisaServiceRoute.providerAssisted;
      }
    }

    // Low-medium complexity cases
    if (userExperience >= 2) {
      return VisaServiceRoute.hybrid;
    } else {
      return VisaServiceRoute.providerAssisted;
    }
  }

  /// Get recommended providers for the route
  Future<List<VisaServiceProvider>> _getRecommendedProviders(
    VisaRequirement visaRequirement,
    VisaServiceRoute route,
  ) async {
    if (route == VisaServiceRoute.selfService) {
      return []; // No providers needed for self-service
    }

    try {
      // Determine visa specialization
      VisaProviderSpecialization? specialization;
      // This would be determined based on visa requirement details
      // For now, default to tourist
      specialization = VisaProviderSpecialization.tourist;

      final providers = await _providerService.getRecommendedProviders(
        destinationCountry: visaRequirement.countryTo,
        visaType: specialization,
        limit: route == VisaServiceRoute.providerRequired ? 5 : 3,
      );

      return providers;
    } catch (e) {
      _loggingService.warning(
        'VisaRoutingService',
        'Failed to get recommended providers: $e',
      );
      return [];
    }
  }

  /// Calculate confidence score
  double _calculateConfidence(
      int complexity, int userExperience, int providerCount) {
    double confidence = 70.0; // Base confidence

    // Adjust based on complexity vs experience match
    final experienceComplexityRatio = userExperience / (complexity + 1);
    if (experienceComplexityRatio > 1.5) {
      confidence += 20;
    } else if (experienceComplexityRatio < 0.5) {
      confidence -= 15;
    }

    // Adjust based on provider availability
    if (providerCount >= 3) {
      confidence += 10;
    } else if (providerCount == 0) {
      confidence -= 20;
    }

    return confidence.clamp(30.0, 95.0);
  }

  /// Calculate cost, time, and success rate estimates
  Map<String, dynamic> _calculateEstimates(
    VisaRequirement visaRequirement,
    List<VisaServiceProvider> providers,
    VisaServiceRoute route,
  ) {
    String costRange;
    String processingTime;
    double successRate;

    // Base estimates from visa requirement
    final baseFee = visaRequirement.visaFee ?? 0;
    final baseProcessingTime = visaRequirement.processingTime ?? 14;

    switch (route) {
      case VisaServiceRoute.selfService:
        costRange = '\$${baseFee.toStringAsFixed(0)}';
        processingTime = '${baseProcessingTime} days';
        successRate = 85.0;
        break;

      case VisaServiceRoute.hybrid:
        final minProviderFee = providers.isNotEmpty
            ? providers
                .map((p) => p.minimumServiceFee)
                .reduce((a, b) => a < b ? a : b)
            : 50.0;
        costRange =
            '\$${baseFee.toStringAsFixed(0)} - \$${(baseFee + minProviderFee).toStringAsFixed(0)}';
        processingTime =
            '${(baseProcessingTime * 0.9).round()} - ${baseProcessingTime} days';
        successRate = 92.0;
        break;

      case VisaServiceRoute.providerAssisted:
      case VisaServiceRoute.providerRequired:
        if (providers.isNotEmpty) {
          final minFee = providers
              .map((p) => p.minimumServiceFee)
              .reduce((a, b) => a < b ? a : b);
          final maxFee = providers
              .map((p) => p.minimumServiceFee)
              .reduce((a, b) => a > b ? a : b);
          costRange =
              '\$${(baseFee + minFee).toStringAsFixed(0)} - \$${(baseFee + maxFee).toStringAsFixed(0)}';

          final avgProcessingTime = providers
                  .map((p) => p.averageProcessingDays)
                  .reduce((a, b) => a + b) /
              providers.length;
          processingTime = '${avgProcessingTime.round()} days';

          final avgSuccessRate = providers
                  .map((p) => p.overallSuccessRate)
                  .reduce((a, b) => a + b) /
              providers.length;
          successRate = avgSuccessRate;
        } else {
          costRange =
              '\$${(baseFee + 100).toStringAsFixed(0)} - \$${(baseFee + 300).toStringAsFixed(0)}';
          processingTime = '${(baseProcessingTime * 0.8).round()} days';
          successRate = 95.0;
        }
        break;
    }

    return {
      'costRange': costRange,
      'processingTime': processingTime,
      'successRate': successRate,
    };
  }

  /// Generate reasoning for the recommendation
  String _generateReasoning(
      VisaServiceRoute route, int complexity, int userExperience) {
    switch (route) {
      case VisaServiceRoute.selfService:
        if (complexity <= 2) {
          return 'This is a straightforward visa process that you can handle independently with our step-by-step guidance.';
        } else {
          return 'Based on your travel experience, you should be able to handle this visa application successfully on your own.';
        }

      case VisaServiceRoute.hybrid:
        return 'This visa has moderate complexity. Start with our self-service tools, but consider provider assistance if you encounter difficulties.';

      case VisaServiceRoute.providerAssisted:
        if (userExperience < 2) {
          return 'Given the complexity of this visa and your travel experience level, we recommend using a professional service for better success rates.';
        } else {
          return 'This visa type has specific requirements that are best handled by experienced professionals.';
        }

      case VisaServiceRoute.providerRequired:
        return 'This is a complex visa type with strict requirements. Professional assistance is strongly recommended to ensure success.';
    }
  }

  /// Generate considerations for the recommendation
  List<String> _generateConsiderations(
      VisaRequirement visaRequirement, VisaServiceRoute route) {
    final considerations = <String>[];

    // Add route-specific considerations
    switch (route) {
      case VisaServiceRoute.selfService:
        considerations.add('Ensure all documents meet exact specifications');
        considerations.add('Double-check application before submission');
        break;

      case VisaServiceRoute.hybrid:
        considerations
            .add('Start early to allow time for provider assistance if needed');
        considerations.add('Keep provider contact information handy');
        break;

      case VisaServiceRoute.providerAssisted:
      case VisaServiceRoute.providerRequired:
        considerations.add('Compare multiple providers for best value');
        considerations.add('Verify provider credentials and reviews');
        break;
    }

    // Add visa-specific considerations
    if (visaRequirement.processingTime != null &&
        visaRequirement.processingTime! > 21) {
      considerations.add('Apply well in advance due to long processing times');
    }

    if (visaRequirement.visaFee != null && visaRequirement.visaFee! > 200) {
      considerations.add('Budget for higher visa fees');
    }

    if (visaRequirement.requiredDocuments.length > 5) {
      considerations.add('Prepare all required documents carefully');
    }

    return considerations;
  }

  /// Get fallback recommendation for error cases
  VisaRoutingRecommendation _getFallbackRecommendation(
      VisaRequirement visaRequirement) {
    return const VisaRoutingRecommendation(
      route: VisaServiceRoute.hybrid,
      confidence: 60.0,
      reasoning:
          'Unable to analyze complexity. We recommend starting with self-service and seeking provider assistance if needed.',
      recommendedProviders: [],
      estimatedCostRange: 'Contact providers for quotes',
      estimatedProcessingTime: 'Varies by provider',
      estimatedSuccessRate: 85.0,
      considerations: [
        'Verify current visa requirements',
        'Consider professional assistance for complex cases',
        'Apply well in advance of travel dates',
      ],
    );
  }
}
