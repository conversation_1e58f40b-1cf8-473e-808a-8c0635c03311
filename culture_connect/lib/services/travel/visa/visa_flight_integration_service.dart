// Flutter imports

// Project imports
import 'package:culture_connect/models/travel/flight/booking_info.dart';
import 'package:culture_connect/models/travel/document/visa_requirement.dart';
import 'package:culture_connect/services/travel/visa/travel_buddy_api_service.dart';
import 'package:culture_connect/services/travel/visa/visa_routing_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Model representing visa requirements detected from flight booking
class FlightVisaDetection {
  /// Flight booking that triggered the detection
  final BookingInfo flightBooking;

  /// Detected visa requirements for each passenger
  final Map<String, VisaRequirement?> passengerVisaRequirements;

  /// Routing recommendations for each passenger
  final Map<String, VisaRoutingRecommendation> routingRecommendations;

  /// Whether any visa requirements were found
  final bool hasVisaRequirements;

  /// Countries that require visas
  final List<String> visaRequiredCountries;

  /// Countries that are visa-free
  final List<String> visaFreeCountries;

  /// Creates a new flight visa detection result
  const FlightVisaDetection({
    required this.flightBooking,
    required this.passengerVisaRequirements,
    required this.routingRecommendations,
    required this.hasVisaRequirements,
    required this.visaRequiredCountries,
    required this.visaFreeCountries,
  });

  /// Get visa requirement for a specific passenger
  VisaRequirement? getVisaRequirementForPassenger(String passengerId) {
    return passengerVisaRequirements[passengerId];
  }

  /// Get routing recommendation for a specific passenger
  VisaRoutingRecommendation? getRoutingRecommendationForPassenger(
      String passengerId) {
    return routingRecommendations[passengerId];
  }

  /// Get passengers who need visas
  List<String> getPassengersNeedingVisas() {
    return passengerVisaRequirements.entries
        .where((entry) =>
            entry.value != null &&
            entry.value!.requirementType !=
                VisaRequirementType.noVisaRequired &&
            entry.value!.requirementType != VisaRequirementType.visaExemption)
        .map((entry) => entry.key)
        .toList();
  }

  /// Get passengers who are visa-free
  List<String> getVisaFreePassengers() {
    return passengerVisaRequirements.entries
        .where((entry) =>
            entry.value != null &&
            (entry.value!.requirementType ==
                    VisaRequirementType.noVisaRequired ||
                entry.value!.requirementType ==
                    VisaRequirementType.visaExemption))
        .map((entry) => entry.key)
        .toList();
  }
}

/// Service for integrating visa requirements with flight booking
class VisaFlightIntegrationService {
  /// Singleton instance
  static final VisaFlightIntegrationService _instance =
      VisaFlightIntegrationService._internal();

  /// Factory constructor
  factory VisaFlightIntegrationService() => _instance;

  /// Internal constructor
  VisaFlightIntegrationService._internal();

  // Dependencies
  final TravelBuddyApiService _travelBuddyService = TravelBuddyApiService();
  final VisaRoutingService _routingService = VisaRoutingService();
  final LoggingService _loggingService = LoggingService();

  /// Auto-detect visa requirements from flight booking
  Future<FlightVisaDetection> detectVisaRequirements({
    required BookingInfo flightBooking,
    Map<String, String>? passengerNationalities,
    Map<String, Map<String, dynamic>>? passengerProfiles,
  }) async {
    try {
      _loggingService.info(
        'VisaFlightIntegrationService',
        'Detecting visa requirements for flight ${flightBooking.bookingReference}',
      );

      // Extract destination countries from flight itinerary
      final destinationCountries = _extractDestinationCountries(flightBooking);

      // Get passenger nationalities (default to booking nationality if not provided)
      final nationalities = passengerNationalities ??
          _getDefaultPassengerNationalities(flightBooking);

      final passengerVisaRequirements = <String, VisaRequirement?>{};
      final routingRecommendations = <String, VisaRoutingRecommendation>{};
      final visaRequiredCountries = <String>[];
      final visaFreeCountries = <String>[];

      // Check visa requirements for each passenger and destination
      for (final passengerId in nationalities.keys) {
        final nationality = nationalities[passengerId]!;

        for (final destination in destinationCountries) {
          // Skip if passenger is traveling to their own country
          if (nationality == destination) continue;

          try {
            final visaRequirement =
                await _travelBuddyService.getVisaRequirements(
              passportCountry: nationality,
              destinationCountry: destination,
            );

            if (visaRequirement != null) {
              passengerVisaRequirements[passengerId] = visaRequirement;

              // Generate routing recommendation
              final routingRecommendation =
                  await _routingService.getRoutingRecommendation(
                visaRequirement: visaRequirement,
                passportCountry: nationality,
                userProfile: passengerProfiles?[passengerId],
              );
              routingRecommendations[passengerId] = routingRecommendation;

              // Categorize countries
              if (_requiresVisa(visaRequirement)) {
                if (!visaRequiredCountries.contains(destination)) {
                  visaRequiredCountries.add(destination);
                }
              } else {
                if (!visaFreeCountries.contains(destination)) {
                  visaFreeCountries.add(destination);
                }
              }
            }
          } catch (e) {
            _loggingService.warning(
              'VisaFlightIntegrationService',
              'Failed to get visa requirement for $nationality -> $destination: $e',
            );
          }
        }
      }

      final detection = FlightVisaDetection(
        flightBooking: flightBooking,
        passengerVisaRequirements: passengerVisaRequirements,
        routingRecommendations: routingRecommendations,
        hasVisaRequirements: visaRequiredCountries.isNotEmpty,
        visaRequiredCountries: visaRequiredCountries,
        visaFreeCountries: visaFreeCountries,
      );

      _loggingService.info(
        'VisaFlightIntegrationService',
        'Visa detection completed: ${visaRequiredCountries.length} countries require visas, '
            '${visaFreeCountries.length} countries are visa-free',
      );

      return detection;
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaFlightIntegrationService',
        'Error detecting visa requirements',
        e,
        stackTrace,
      );

      // Return empty detection on error
      return FlightVisaDetection(
        flightBooking: flightBooking,
        passengerVisaRequirements: const {},
        routingRecommendations: const {},
        hasVisaRequirements: false,
        visaRequiredCountries: const [],
        visaFreeCountries: const [],
      );
    }
  }

  /// Generate smart visa recommendations based on flight booking
  Future<List<VisaRecommendation>> generateSmartRecommendations({
    required BookingInfo flightBooking,
    required FlightVisaDetection detection,
  }) async {
    final recommendations = <VisaRecommendation>[];

    try {
      // Recommend visa applications for passengers who need them
      final passengersNeedingVisas = detection.getPassengersNeedingVisas();

      for (final passengerId in passengersNeedingVisas) {
        final visaRequirement =
            detection.getVisaRequirementForPassenger(passengerId);
        final routingRecommendation =
            detection.getRoutingRecommendationForPassenger(passengerId);

        if (visaRequirement != null && routingRecommendation != null) {
          recommendations.add(VisaRecommendation(
            passengerId: passengerId,
            visaRequirement: visaRequirement,
            routingRecommendation: routingRecommendation,
            urgency: _calculateUrgency(
                flightBooking.flight.departureDateTime, visaRequirement),
            actionRequired: _getActionRequired(routingRecommendation.route),
          ));
        }
      }

      // Sort by urgency (most urgent first)
      recommendations.sort((a, b) => b.urgency.compareTo(a.urgency));

      _loggingService.info(
        'VisaFlightIntegrationService',
        'Generated ${recommendations.length} visa recommendations',
      );

      return recommendations;
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaFlightIntegrationService',
        'Error generating visa recommendations',
        e,
        stackTrace,
      );
      return [];
    }
  }

  /// Extract destination countries from flight booking
  List<String> _extractDestinationCountries(BookingInfo flightBooking) {
    final countries = <String>[];
    final flight = flightBooking.flight;

    // Add destination country from arrival airport
    // Extract country code from airport code (first 2 characters typically)
    final arrivalCountry = _extractCountryFromAirport(flight.arrivalAirport);
    if (arrivalCountry.isNotEmpty && !countries.contains(arrivalCountry)) {
      countries.add(arrivalCountry);
    }

    // Add any layover countries
    if (flight.layoverAirports != null) {
      for (final layoverAirport in flight.layoverAirports!) {
        final layoverCountry = _extractCountryFromAirport(layoverAirport);
        if (layoverCountry.isNotEmpty && !countries.contains(layoverCountry)) {
          countries.add(layoverCountry);
        }
      }
    }

    return countries;
  }

  /// Extract country code from airport code
  /// This is a simplified implementation - in production, you'd use a proper airport database
  String _extractCountryFromAirport(String airportCode) {
    // Common airport code to country mappings
    const airportToCountry = {
      // US airports
      'JFK': 'US', 'LAX': 'US', 'ORD': 'US', 'DFW': 'US', 'ATL': 'US',
      'SFO': 'US', 'SEA': 'US', 'DEN': 'US', 'LAS': 'US', 'PHX': 'US',

      // UK airports
      'LHR': 'GB', 'LGW': 'GB', 'STN': 'GB', 'MAN': 'GB', 'EDI': 'GB',

      // European airports
      'CDG': 'FR', 'ORY': 'FR', 'FRA': 'DE', 'MUC': 'DE', 'FCO': 'IT',
      'MAD': 'ES', 'BCN': 'ES', 'AMS': 'NL', 'VIE': 'AT', 'ZUR': 'CH',

      // Asian airports
      'NRT': 'JP', 'HND': 'JP', 'ICN': 'KR', 'PVG': 'CN', 'PEK': 'CN',
      'BOM': 'IN', 'DEL': 'IN', 'SIN': 'SG', 'BKK': 'TH', 'KUL': 'MY',

      // Other major airports
      'YYZ': 'CA', 'YVR': 'CA', 'SYD': 'AU', 'MEL': 'AU', 'DXB': 'AE',
      'DOH': 'QA', 'JNB': 'ZA', 'CAI': 'EG', 'GRU': 'BR', 'MEX': 'MX',
    };

    return airportToCountry[airportCode] ?? '';
  }

  /// Get default passenger nationalities from booking
  Map<String, String> _getDefaultPassengerNationalities(
      BookingInfo flightBooking) {
    final nationalities = <String, String>{};

    for (final passenger in flightBooking.passengers) {
      // Use passenger's nationality if available, otherwise use default
      nationalities[passenger.passportNumber] = passenger.nationality;
    }

    return nationalities;
  }

  /// Check if a visa requirement actually requires a visa
  bool _requiresVisa(VisaRequirement visaRequirement) {
    return visaRequirement.requirementType !=
            VisaRequirementType.noVisaRequired &&
        visaRequirement.requirementType != VisaRequirementType.visaExemption;
  }

  /// Calculate urgency score based on departure date and processing time
  double _calculateUrgency(
      DateTime departureDate, VisaRequirement visaRequirement) {
    final now = DateTime.now();
    final daysUntilDeparture = departureDate.difference(now).inDays;
    final processingTime = visaRequirement.processingTime ?? 14;

    // Higher score = more urgent
    if (daysUntilDeparture <= processingTime) {
      return 1.0; // Critical urgency
    } else if (daysUntilDeparture <= processingTime * 1.5) {
      return 0.8; // High urgency
    } else if (daysUntilDeparture <= processingTime * 2) {
      return 0.6; // Medium urgency
    } else {
      return 0.4; // Low urgency
    }
  }

  /// Get action required based on routing recommendation
  String _getActionRequired(VisaServiceRoute route) {
    switch (route) {
      case VisaServiceRoute.selfService:
        return 'Apply independently with our guidance';
      case VisaServiceRoute.hybrid:
        return 'Start application, get help if needed';
      case VisaServiceRoute.providerAssisted:
        return 'Use professional visa service';
      case VisaServiceRoute.providerRequired:
        return 'Professional assistance required';
    }
  }
}

/// Model representing a visa recommendation for a passenger
class VisaRecommendation {
  /// Passenger ID
  final String passengerId;

  /// Visa requirement details
  final VisaRequirement visaRequirement;

  /// Routing recommendation
  final VisaRoutingRecommendation routingRecommendation;

  /// Urgency score (0.0 to 1.0)
  final double urgency;

  /// Action required description
  final String actionRequired;

  /// Creates a new visa recommendation
  const VisaRecommendation({
    required this.passengerId,
    required this.visaRequirement,
    required this.routingRecommendation,
    required this.urgency,
    required this.actionRequired,
  });

  /// Get urgency level as string
  String get urgencyLevel {
    if (urgency >= 0.9) return 'Critical';
    if (urgency >= 0.7) return 'High';
    if (urgency >= 0.5) return 'Medium';
    return 'Low';
  }

  /// Get urgency color
  String get urgencyColor {
    if (urgency >= 0.9) return '#DC2626'; // Red
    if (urgency >= 0.7) return '#F59E0B'; // Amber
    if (urgency >= 0.5) return '#3B82F6'; // Blue
    return '#10B981'; // Green
  }
}
