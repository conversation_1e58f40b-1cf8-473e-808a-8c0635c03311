import 'dart:async';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import 'package:culture_connect/models/performance/performance_metric.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/providers/achievement_provider.dart'
    hide loggingServiceProvider, analyticsServiceProvider;
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/mascot_service.dart';

/// Provider for the PerformanceMetricsService
final performanceMetricsServiceProvider =
    Provider<PerformanceMetricsService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final analyticsService = ref.watch(analyticsServiceProvider);
  final achievementService = ref.watch(achievementServiceProvider);
  final mascotService = ref.watch(mascotServiceProvider);

  return PerformanceMetricsService(
    prefs: prefs,
    loggingService: loggingService,
    analyticsService: analyticsService,
    achievementService: achievementService,
    mascotService: mascotService,
  );
});

/// Performance metrics preferences
class PerformanceMetricsPreferences {
  /// Whether performance metrics collection is enabled
  final bool metricsEnabled;

  /// Whether to collect startup metrics
  final bool collectStartupMetrics;

  /// Whether to collect transition metrics
  final bool collectTransitionMetrics;

  /// Whether to collect API metrics
  final bool collectAPIMetrics;

  /// Whether to collect memory metrics
  final bool collectMemoryMetrics;

  /// Whether to collect battery metrics
  final bool collectBatteryMetrics;

  /// Data retention period in days
  final int dataRetentionDays;

  /// Whether user has given consent for metrics collection
  final bool hasUserConsent;

  /// Maximum metrics to store locally
  final int maxLocalMetrics;

  const PerformanceMetricsPreferences({
    this.metricsEnabled = true,
    this.collectStartupMetrics = true,
    this.collectTransitionMetrics = true,
    this.collectAPIMetrics = true,
    this.collectMemoryMetrics = true,
    this.collectBatteryMetrics = true,
    this.dataRetentionDays = 30,
    this.hasUserConsent = false,
    this.maxLocalMetrics = 1000,
  });

  PerformanceMetricsPreferences copyWith({
    bool? metricsEnabled,
    bool? collectStartupMetrics,
    bool? collectTransitionMetrics,
    bool? collectAPIMetrics,
    bool? collectMemoryMetrics,
    bool? collectBatteryMetrics,
    int? dataRetentionDays,
    bool? hasUserConsent,
    int? maxLocalMetrics,
  }) {
    return PerformanceMetricsPreferences(
      metricsEnabled: metricsEnabled ?? this.metricsEnabled,
      collectStartupMetrics:
          collectStartupMetrics ?? this.collectStartupMetrics,
      collectTransitionMetrics:
          collectTransitionMetrics ?? this.collectTransitionMetrics,
      collectAPIMetrics: collectAPIMetrics ?? this.collectAPIMetrics,
      collectMemoryMetrics: collectMemoryMetrics ?? this.collectMemoryMetrics,
      collectBatteryMetrics:
          collectBatteryMetrics ?? this.collectBatteryMetrics,
      dataRetentionDays: dataRetentionDays ?? this.dataRetentionDays,
      hasUserConsent: hasUserConsent ?? this.hasUserConsent,
      maxLocalMetrics: maxLocalMetrics ?? this.maxLocalMetrics,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'metricsEnabled': metricsEnabled,
      'collectStartupMetrics': collectStartupMetrics,
      'collectTransitionMetrics': collectTransitionMetrics,
      'collectAPIMetrics': collectAPIMetrics,
      'collectMemoryMetrics': collectMemoryMetrics,
      'collectBatteryMetrics': collectBatteryMetrics,
      'dataRetentionDays': dataRetentionDays,
      'hasUserConsent': hasUserConsent,
      'maxLocalMetrics': maxLocalMetrics,
    };
  }

  factory PerformanceMetricsPreferences.fromJson(Map<String, dynamic> json) {
    return PerformanceMetricsPreferences(
      metricsEnabled: json['metricsEnabled'] as bool? ?? true,
      collectStartupMetrics: json['collectStartupMetrics'] as bool? ?? true,
      collectTransitionMetrics:
          json['collectTransitionMetrics'] as bool? ?? true,
      collectAPIMetrics: json['collectAPIMetrics'] as bool? ?? true,
      collectMemoryMetrics: json['collectMemoryMetrics'] as bool? ?? true,
      collectBatteryMetrics: json['collectBatteryMetrics'] as bool? ?? true,
      dataRetentionDays: json['dataRetentionDays'] as int? ?? 30,
      hasUserConsent: json['hasUserConsent'] as bool? ?? false,
      maxLocalMetrics: json['maxLocalMetrics'] as int? ?? 1000,
    );
  }
}

/// Service for collecting and managing performance metrics
class PerformanceMetricsService {
  final SharedPreferences _prefs;
  final LoggingService _loggingService;
  final AnalyticsService _analyticsService;
  final AchievementService _achievementService;

  /// UUID generator for metric IDs
  static const _uuid = Uuid();

  /// Current session ID
  late final String _sessionId;

  /// Current user preferences
  PerformanceMetricsPreferences _preferences =
      const PerformanceMetricsPreferences();

  /// Cached metrics
  final List<PerformanceMetric> _cachedMetrics = [];

  /// Timer for periodic memory monitoring
  Timer? _memoryMonitoringTimer;

  /// Timer for periodic battery monitoring
  Timer? _batteryMonitoringTimer;

  /// Whether service is initialized
  bool _isInitialized = false;

  /// Storage keys
  static const String _preferencesKey = 'performance_metrics_preferences';
  static const String _metricsKey = 'performance_metrics_cache';

  PerformanceMetricsService({
    required SharedPreferences prefs,
    required LoggingService loggingService,
    required AnalyticsService analyticsService,
    required AchievementService achievementService,
    required MascotService mascotService,
  })  : _prefs = prefs,
        _loggingService = loggingService,
        _analyticsService = analyticsService,
        _achievementService = achievementService {
    _sessionId = _uuid.v4();
  }

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadPreferences();
      await _loadCachedMetrics();

      if (_preferences.hasUserConsent && _preferences.metricsEnabled) {
        await _startPeriodicMonitoring();
      }

      _isInitialized = true;

      _loggingService.info(
        'PerformanceMetricsService',
        'Service initialized',
        {
          'sessionId': _sessionId,
          'metricsEnabled': _preferences.metricsEnabled,
          'hasConsent': _preferences.hasUserConsent,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'PerformanceMetricsService',
        'Failed to initialize service',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Request user consent for metrics collection
  Future<bool> requestUserConsent() async {
    try {
      // TODO: Integrate with Performance Metrics Consent Dialog UI
      // POST /api/v1/analytics/performance/consent
      // Headers: Authorization, Content-Type
      // Body: { userId: string, consentGiven: boolean, timestamp: string }
      // Response: { success: boolean, consentId: string }

      // For now, we'll assume consent is given for development
      await updatePreferences(_preferences.copyWith(hasUserConsent: true));

      if (_preferences.metricsEnabled) {
        await _startPeriodicMonitoring();
      }

      return true;
    } catch (e) {
      _loggingService.error(
        'PerformanceMetricsService',
        'Failed to request user consent',
        {'error': e.toString()},
      );
      return false;
    }
  }

  /// Update user preferences
  Future<void> updatePreferences(
      PerformanceMetricsPreferences preferences) async {
    try {
      _preferences = preferences;
      await _prefs.setString(_preferencesKey, jsonEncode(preferences.toJson()));

      if (!preferences.metricsEnabled || !preferences.hasUserConsent) {
        await _stopPeriodicMonitoring();
      } else {
        await _startPeriodicMonitoring();
      }

      _loggingService.info(
        'PerformanceMetricsService',
        'Preferences updated',
        {'preferences': preferences.toJson()},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'PerformanceMetricsService',
        'Failed to update preferences',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Get current preferences
  PerformanceMetricsPreferences get preferences => _preferences;

  /// Check if metrics collection is enabled
  bool get isMetricsEnabled =>
      _preferences.metricsEnabled && _preferences.hasUserConsent;

  /// Record a startup metric
  Future<void> recordStartupMetric({
    required String startupType,
    required Duration timeToFirstFrame,
    required Duration timeToInteractive,
    required double initialMemoryUsage,
  }) async {
    if (!isMetricsEnabled || !_preferences.collectStartupMetrics) return;

    try {
      final metric = StartupMetric(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        sessionId: _sessionId,
        startupType: startupType,
        timeToFirstFrame: timeToFirstFrame,
        timeToInteractive: timeToInteractive,
        initialMemoryUsage: initialMemoryUsage,
      );

      await _recordMetric(metric);

      // Track achievement for fast startup
      if (timeToInteractive.inMilliseconds < 2000) {
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {
            'type': 'fast_startup',
            'duration': timeToInteractive.inMilliseconds
          },
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'PerformanceMetricsService',
        'Failed to record startup metric',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Record a screen transition metric
  Future<void> recordTransitionMetric({
    required String fromScreen,
    required String toScreen,
    required Duration transitionDuration,
    required bool isAnimated,
    required String routeType,
  }) async {
    if (!isMetricsEnabled || !_preferences.collectTransitionMetrics) return;

    try {
      final metric = TransitionMetric(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        sessionId: _sessionId,
        fromScreen: fromScreen,
        toScreen: toScreen,
        transitionDuration: transitionDuration,
        isAnimated: isAnimated,
        routeType: routeType,
      );

      await _recordMetric(metric);

      // Track achievement for smooth navigation
      if (transitionDuration.inMilliseconds < 300) {
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {
            'type': 'smooth_navigation',
            'duration': transitionDuration.inMilliseconds
          },
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'PerformanceMetricsService',
        'Failed to record transition metric',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Record an API request metric
  Future<void> recordAPIMetric({
    required String endpoint,
    required String method,
    required Duration responseTime,
    required int statusCode,
    required int requestSize,
    required int responseSize,
  }) async {
    if (!isMetricsEnabled || !_preferences.collectAPIMetrics) return;

    try {
      final metric = APIMetric(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        sessionId: _sessionId,
        endpoint: endpoint,
        method: method,
        responseTime: responseTime,
        statusCode: statusCode,
        requestSize: requestSize,
        responseSize: responseSize,
        isSuccessful: statusCode >= 200 && statusCode < 300,
      );

      await _recordMetric(metric);

      // Track achievement for efficient API usage
      if (responseTime.inMilliseconds < 500 && metric.isSuccessful) {
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {
            'type': 'fast_api',
            'duration': responseTime.inMilliseconds
          },
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'PerformanceMetricsService',
        'Failed to record API metric',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Record a memory usage metric
  Future<void> recordMemoryMetric({
    required double heapUsage,
    required double nativeUsage,
    required double totalUsage,
    required double availableMemory,
    required String pressureLevel,
  }) async {
    if (!isMetricsEnabled || !_preferences.collectMemoryMetrics) return;

    try {
      final metric = MemoryMetric(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        sessionId: _sessionId,
        heapUsage: heapUsage,
        nativeUsage: nativeUsage,
        totalUsage: totalUsage,
        availableMemory: availableMemory,
        pressureLevel: pressureLevel,
      );

      await _recordMetric(metric);

      // Track achievement for memory efficiency
      if (pressureLevel == 'low' && totalUsage < 100 * 1024 * 1024) {
        // Less than 100MB
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {'type': 'memory_efficient', 'usage': totalUsage},
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'PerformanceMetricsService',
        'Failed to record memory metric',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Record a battery consumption metric
  Future<void> recordBatteryMetric({
    required double batteryLevel,
    required double consumptionRate,
    required bool isCharging,
    required String powerState,
    int? estimatedTimeRemaining,
  }) async {
    if (!isMetricsEnabled || !_preferences.collectBatteryMetrics) return;

    try {
      final metric = BatteryMetric(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        sessionId: _sessionId,
        batteryLevel: batteryLevel,
        consumptionRate: consumptionRate,
        isCharging: isCharging,
        powerState: powerState,
        estimatedTimeRemaining: estimatedTimeRemaining,
      );

      await _recordMetric(metric);

      // Track achievement for battery efficiency
      if (consumptionRate < 5.0 && !isCharging) {
        // Less than 5% per hour
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {'type': 'battery_efficient', 'rate': consumptionRate},
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'PerformanceMetricsService',
        'Failed to record battery metric',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Get all cached metrics
  List<PerformanceMetric> getCachedMetrics() {
    return List.unmodifiable(_cachedMetrics);
  }

  /// Get metrics by type
  List<PerformanceMetric> getMetricsByType(MetricType type) {
    return _cachedMetrics.where((metric) => metric.type == type).toList();
  }

  /// Get metrics for a specific time period
  List<PerformanceMetric> getMetricsForPeriod({
    required DateTime startDate,
    required DateTime endDate,
  }) {
    return _cachedMetrics
        .where((metric) =>
            metric.timestamp.isAfter(startDate) &&
            metric.timestamp.isBefore(endDate))
        .toList();
  }

  /// Export user data for privacy compliance
  Future<Map<String, dynamic>> exportUserData() async {
    try {
      final data = {
        'sessionId': _sessionId,
        'preferences': _preferences.toJson(),
        'metrics': _cachedMetrics.map((m) => m.toJson()).toList(),
        'exportTimestamp': DateTime.now().toIso8601String(),
      };

      _loggingService.info(
        'PerformanceMetricsService',
        'User data exported',
        {'metricsCount': _cachedMetrics.length},
      );

      return data;
    } catch (e, stackTrace) {
      _loggingService.error(
        'PerformanceMetricsService',
        'Failed to export user data',
        {'error': e.toString()},
        stackTrace,
      );
      return {};
    }
  }

  /// Clear all cached metrics
  Future<void> clearAllMetrics() async {
    try {
      _cachedMetrics.clear();
      await _prefs.remove(_metricsKey);

      _loggingService.info(
        'PerformanceMetricsService',
        'All metrics cleared',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'PerformanceMetricsService',
        'Failed to clear metrics',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Dispose of the service
  Future<void> dispose() async {
    await _stopPeriodicMonitoring();
    _loggingService.info('PerformanceMetricsService', 'Service disposed');
  }

  /// Record a metric internally
  Future<void> _recordMetric(PerformanceMetric metric) async {
    try {
      _cachedMetrics.add(metric);

      // Enforce cache size limit
      if (_cachedMetrics.length > _preferences.maxLocalMetrics) {
        _cachedMetrics.removeAt(0);
      }

      await _saveCachedMetrics();

      // Log to analytics service
      await _analyticsService.logPerformance(
        name: metric.type.value,
        durationMillis: metric.value.round(),
        parameters: metric.metadata,
      );

      // TODO: Integrate with Performance Metrics Collection API
      // POST /api/v1/analytics/performance/metrics
      // Headers: Authorization, Content-Type, X-Session-ID
      // Body: {
      //   metrics: [PerformanceMetric],
      //   userId: string,
      //   sessionId: string,
      //   deviceInfo: { platform: string, version: string, model: string },
      //   appVersion: string,
      //   timestamp: string
      // }
      // Response: { success: boolean, metricsProcessed: number, errors: string[] }

      _loggingService.debug(
        'PerformanceMetricsService',
        'Metric recorded',
        {
          'type': metric.type.displayName,
          'value': metric.value,
          'unit': metric.unit,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'PerformanceMetricsService',
        'Failed to record metric',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Load preferences from storage
  Future<void> _loadPreferences() async {
    try {
      final prefsJson = _prefs.getString(_preferencesKey);
      if (prefsJson != null) {
        final prefsMap = jsonDecode(prefsJson) as Map<String, dynamic>;
        _preferences = PerformanceMetricsPreferences.fromJson(prefsMap);
      }
    } catch (e) {
      _loggingService.warning(
        'PerformanceMetricsService',
        'Failed to load preferences, using defaults',
        {'error': e.toString()},
      );
    }
  }

  /// Load cached metrics from storage
  Future<void> _loadCachedMetrics() async {
    try {
      final metricsJson = _prefs.getString(_metricsKey);
      if (metricsJson != null) {
        final metricsList = jsonDecode(metricsJson) as List<dynamic>;
        _cachedMetrics.clear();

        for (final metricMap in metricsList) {
          try {
            final metric =
                PerformanceMetric.fromJson(metricMap as Map<String, dynamic>);

            // Check if metric is within retention period
            final retentionDate = DateTime.now()
                .subtract(Duration(days: _preferences.dataRetentionDays));

            if (metric.timestamp.isAfter(retentionDate)) {
              _cachedMetrics.add(metric);
            }
          } catch (e) {
            _loggingService.warning(
              'PerformanceMetricsService',
              'Failed to parse cached metric',
              {'error': e.toString()},
            );
          }
        }
      }
    } catch (e) {
      _loggingService.warning(
        'PerformanceMetricsService',
        'Failed to load cached metrics',
        {'error': e.toString()},
      );
    }
  }

  /// Save cached metrics to storage
  Future<void> _saveCachedMetrics() async {
    try {
      final metricsJson = _cachedMetrics.map((m) => m.toJson()).toList();
      await _prefs.setString(_metricsKey, jsonEncode(metricsJson));
    } catch (e) {
      _loggingService.warning(
        'PerformanceMetricsService',
        'Failed to save cached metrics',
        {'error': e.toString()},
      );
    }
  }

  /// Start periodic monitoring
  Future<void> _startPeriodicMonitoring() async {
    await _stopPeriodicMonitoring();

    if (_preferences.collectMemoryMetrics) {
      _memoryMonitoringTimer = Timer.periodic(
        const Duration(minutes: 5),
        (_) => _collectMemoryMetrics(),
      );
    }

    if (_preferences.collectBatteryMetrics) {
      _batteryMonitoringTimer = Timer.periodic(
        const Duration(minutes: 10),
        (_) => _collectBatteryMetrics(),
      );
    }
  }

  /// Stop periodic monitoring
  Future<void> _stopPeriodicMonitoring() async {
    _memoryMonitoringTimer?.cancel();
    _batteryMonitoringTimer?.cancel();
    _memoryMonitoringTimer = null;
    _batteryMonitoringTimer = null;
  }

  /// Collect memory metrics
  Future<void> _collectMemoryMetrics() async {
    try {
      // TODO: Integrate with Platform-Specific Memory Monitoring
      // iOS: Use ProcessInfo.processInfo.physicalMemory and vm_stat
      // Android: Use ActivityManager.MemoryInfo and /proc/meminfo
      // Platform Channel: MethodChannel('performance_metrics/memory')
      // Methods: getHeapUsage(), getNativeUsage(), getAvailableMemory(), getPressureLevel()
      // Response: { heapUsage: number, nativeUsage: number, totalUsage: number, availableMemory: number, pressureLevel: string }

      // For now, use placeholder values
      await recordMemoryMetric(
        heapUsage: 50 * 1024 * 1024, // 50MB
        nativeUsage: 30 * 1024 * 1024, // 30MB
        totalUsage: 80 * 1024 * 1024, // 80MB
        availableMemory: 200 * 1024 * 1024, // 200MB
        pressureLevel: 'low',
      );
    } catch (e) {
      _loggingService.warning(
        'PerformanceMetricsService',
        'Failed to collect memory metrics',
        {'error': e.toString()},
      );
    }
  }

  /// Collect battery metrics
  Future<void> _collectBatteryMetrics() async {
    try {
      // TODO: Integrate with Platform-Specific Battery Monitoring
      // iOS: Use UIDevice.current.batteryLevel and UIDevice.BatteryState
      // Android: Use BatteryManager and Intent.ACTION_BATTERY_CHANGED
      // Platform Channel: MethodChannel('performance_metrics/battery')
      // Methods: getBatteryLevel(), getChargingState(), getConsumptionRate(), getEstimatedTime()
      // Response: { batteryLevel: number, isCharging: boolean, powerState: string, consumptionRate: number, estimatedTime: number }

      // For now, use placeholder values
      await recordBatteryMetric(
        batteryLevel: 0.8, // 80%
        consumptionRate: 3.5, // 3.5% per hour
        isCharging: false,
        powerState: 'unplugged',
        estimatedTimeRemaining: 480, // 8 hours
      );
    } catch (e) {
      _loggingService.warning(
        'PerformanceMetricsService',
        'Failed to collect battery metrics',
        {'error': e.toString()},
      );
    }
  }
}
