import 'dart:async';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/providers/achievement_provider.dart'
    hide loggingServiceProvider, analyticsServiceProvider;
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/mascot_service.dart';

/// Provider for the AlertService
final alertServiceProvider = Provider<AlertService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final analyticsService = ref.watch(analyticsServiceProvider);
  final achievementService = ref.watch(achievementServiceProvider);
  final mascotService = ref.watch(mascotServiceProvider);

  return AlertService(
    prefs: prefs,
    loggingService: loggingService,
    analyticsService: analyticsService,
    achievementService: achievementService,
    mascotService: mascotService,
  );
});

/// Alert priority levels
enum AlertPriority {
  low,
  medium,
  high,
  critical,
}

extension AlertPriorityExtension on AlertPriority {
  String get displayName {
    switch (this) {
      case AlertPriority.low:
        return 'Low';
      case AlertPriority.medium:
        return 'Medium';
      case AlertPriority.high:
        return 'High';
      case AlertPriority.critical:
        return 'Critical';
    }
  }

  String get value {
    switch (this) {
      case AlertPriority.low:
        return 'low';
      case AlertPriority.medium:
        return 'medium';
      case AlertPriority.high:
        return 'high';
      case AlertPriority.critical:
        return 'critical';
    }
  }

  int get numericValue {
    switch (this) {
      case AlertPriority.low:
        return 1;
      case AlertPriority.medium:
        return 2;
      case AlertPriority.high:
        return 3;
      case AlertPriority.critical:
        return 4;
    }
  }
}

/// Alert types for categorization
enum AlertType {
  systemHealth,
  performance,
  connectivity,
  storage,
  cache,
  service,
  security,
  userExperience,
}

extension AlertTypeExtension on AlertType {
  String get displayName {
    switch (this) {
      case AlertType.systemHealth:
        return 'System Health';
      case AlertType.performance:
        return 'Performance';
      case AlertType.connectivity:
        return 'Connectivity';
      case AlertType.storage:
        return 'Storage';
      case AlertType.cache:
        return 'Cache';
      case AlertType.service:
        return 'Service';
      case AlertType.security:
        return 'Security';
      case AlertType.userExperience:
        return 'User Experience';
    }
  }

  String get value {
    switch (this) {
      case AlertType.systemHealth:
        return 'system_health';
      case AlertType.performance:
        return 'performance';
      case AlertType.connectivity:
        return 'connectivity';
      case AlertType.storage:
        return 'storage';
      case AlertType.cache:
        return 'cache';
      case AlertType.service:
        return 'service';
      case AlertType.security:
        return 'security';
      case AlertType.userExperience:
        return 'user_experience';
    }
  }
}

/// Alert status for lifecycle management
enum AlertStatus {
  active,
  acknowledged,
  resolved,
  dismissed,
}

extension AlertStatusExtension on AlertStatus {
  String get displayName {
    switch (this) {
      case AlertStatus.active:
        return 'Active';
      case AlertStatus.acknowledged:
        return 'Acknowledged';
      case AlertStatus.resolved:
        return 'Resolved';
      case AlertStatus.dismissed:
        return 'Dismissed';
    }
  }

  String get value {
    switch (this) {
      case AlertStatus.active:
        return 'active';
      case AlertStatus.acknowledged:
        return 'acknowledged';
      case AlertStatus.resolved:
        return 'resolved';
      case AlertStatus.dismissed:
        return 'dismissed';
    }
  }
}

/// Model representing a monitoring alert
class MonitoringAlert {
  /// Unique identifier for the alert
  final String id;

  /// Alert title
  final String title;

  /// Alert description
  final String description;

  /// Alert type
  final AlertType type;

  /// Alert priority
  final AlertPriority priority;

  /// Alert status
  final AlertStatus status;

  /// Timestamp when alert was created
  final DateTime createdAt;

  /// Timestamp when alert was last updated
  final DateTime? updatedAt;

  /// Timestamp when alert was resolved
  final DateTime? resolvedAt;

  /// Source of the alert (health check, performance metric, etc.)
  final String source;

  /// Additional context data
  final Map<String, dynamic> context;

  /// Recommended actions
  final List<String> recommendedActions;

  /// Whether the alert should be shown to the user
  final bool isUserVisible;

  /// Session ID for correlation
  final String sessionId;

  const MonitoringAlert({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.priority,
    this.status = AlertStatus.active,
    required this.createdAt,
    this.updatedAt,
    this.resolvedAt,
    required this.source,
    this.context = const {},
    this.recommendedActions = const [],
    this.isUserVisible = true,
    required this.sessionId,
  });

  /// Create a copy with updated values
  MonitoringAlert copyWith({
    String? id,
    String? title,
    String? description,
    AlertType? type,
    AlertPriority? priority,
    AlertStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? resolvedAt,
    String? source,
    Map<String, dynamic>? context,
    List<String>? recommendedActions,
    bool? isUserVisible,
    String? sessionId,
  }) {
    return MonitoringAlert(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      source: source ?? this.source,
      context: context ?? this.context,
      recommendedActions: recommendedActions ?? this.recommendedActions,
      isUserVisible: isUserVisible ?? this.isUserVisible,
      sessionId: sessionId ?? this.sessionId,
    );
  }

  /// Check if alert is active
  bool get isActive => status == AlertStatus.active;

  /// Check if alert is resolved
  bool get isResolved => status == AlertStatus.resolved;

  /// Get alert age
  Duration get age => DateTime.now().difference(createdAt);

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.value,
      'priority': priority.value,
      'status': status.value,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'resolvedAt': resolvedAt?.toIso8601String(),
      'source': source,
      'context': context,
      'recommendedActions': recommendedActions,
      'isUserVisible': isUserVisible,
      'sessionId': sessionId,
    };
  }

  /// Create from JSON
  factory MonitoringAlert.fromJson(Map<String, dynamic> json) {
    return MonitoringAlert(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: AlertType.values.firstWhere(
        (t) => t.value == json['type'],
        orElse: () => AlertType.systemHealth,
      ),
      priority: AlertPriority.values.firstWhere(
        (p) => p.value == json['priority'],
        orElse: () => AlertPriority.medium,
      ),
      status: AlertStatus.values.firstWhere(
        (s) => s.value == json['status'],
        orElse: () => AlertStatus.active,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      resolvedAt: json['resolvedAt'] != null
          ? DateTime.parse(json['resolvedAt'] as String)
          : null,
      source: json['source'] as String,
      context: Map<String, dynamic>.from(json['context'] as Map? ?? {}),
      recommendedActions:
          List<String>.from(json['recommendedActions'] as List? ?? []),
      isUserVisible: json['isUserVisible'] as bool? ?? true,
      sessionId: json['sessionId'] as String,
    );
  }

  @override
  String toString() {
    return 'MonitoringAlert(id: $id, title: $title, priority: ${priority.displayName}, '
        'status: ${status.displayName})';
  }
}

/// Alert preferences for user control
class AlertPreferences {
  /// Whether alerts are enabled
  final bool alertsEnabled;

  /// Whether to show low priority alerts
  final bool showLowPriorityAlerts;

  /// Whether to show medium priority alerts
  final bool showMediumPriorityAlerts;

  /// Whether to show high priority alerts
  final bool showHighPriorityAlerts;

  /// Whether to show critical priority alerts
  final bool showCriticalPriorityAlerts;

  /// Alert retention period in days
  final int alertRetentionDays;

  /// Maximum alerts to store locally
  final int maxLocalAlerts;

  /// Whether user has given consent for alerts
  final bool hasUserConsent;

  /// Whether to auto-resolve alerts
  final bool autoResolveAlerts;

  /// Auto-resolve timeout in hours
  final int autoResolveTimeoutHours;

  const AlertPreferences({
    this.alertsEnabled = true,
    this.showLowPriorityAlerts = false,
    this.showMediumPriorityAlerts = true,
    this.showHighPriorityAlerts = true,
    this.showCriticalPriorityAlerts = true,
    this.alertRetentionDays = 7,
    this.maxLocalAlerts = 100,
    this.hasUserConsent = false,
    this.autoResolveAlerts = true,
    this.autoResolveTimeoutHours = 24,
  });

  AlertPreferences copyWith({
    bool? alertsEnabled,
    bool? showLowPriorityAlerts,
    bool? showMediumPriorityAlerts,
    bool? showHighPriorityAlerts,
    bool? showCriticalPriorityAlerts,
    int? alertRetentionDays,
    int? maxLocalAlerts,
    bool? hasUserConsent,
    bool? autoResolveAlerts,
    int? autoResolveTimeoutHours,
  }) {
    return AlertPreferences(
      alertsEnabled: alertsEnabled ?? this.alertsEnabled,
      showLowPriorityAlerts:
          showLowPriorityAlerts ?? this.showLowPriorityAlerts,
      showMediumPriorityAlerts:
          showMediumPriorityAlerts ?? this.showMediumPriorityAlerts,
      showHighPriorityAlerts:
          showHighPriorityAlerts ?? this.showHighPriorityAlerts,
      showCriticalPriorityAlerts:
          showCriticalPriorityAlerts ?? this.showCriticalPriorityAlerts,
      alertRetentionDays: alertRetentionDays ?? this.alertRetentionDays,
      maxLocalAlerts: maxLocalAlerts ?? this.maxLocalAlerts,
      hasUserConsent: hasUserConsent ?? this.hasUserConsent,
      autoResolveAlerts: autoResolveAlerts ?? this.autoResolveAlerts,
      autoResolveTimeoutHours:
          autoResolveTimeoutHours ?? this.autoResolveTimeoutHours,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'alertsEnabled': alertsEnabled,
      'showLowPriorityAlerts': showLowPriorityAlerts,
      'showMediumPriorityAlerts': showMediumPriorityAlerts,
      'showHighPriorityAlerts': showHighPriorityAlerts,
      'showCriticalPriorityAlerts': showCriticalPriorityAlerts,
      'alertRetentionDays': alertRetentionDays,
      'maxLocalAlerts': maxLocalAlerts,
      'hasUserConsent': hasUserConsent,
      'autoResolveAlerts': autoResolveAlerts,
      'autoResolveTimeoutHours': autoResolveTimeoutHours,
    };
  }

  factory AlertPreferences.fromJson(Map<String, dynamic> json) {
    return AlertPreferences(
      alertsEnabled: json['alertsEnabled'] as bool? ?? true,
      showLowPriorityAlerts: json['showLowPriorityAlerts'] as bool? ?? false,
      showMediumPriorityAlerts:
          json['showMediumPriorityAlerts'] as bool? ?? true,
      showHighPriorityAlerts: json['showHighPriorityAlerts'] as bool? ?? true,
      showCriticalPriorityAlerts:
          json['showCriticalPriorityAlerts'] as bool? ?? true,
      alertRetentionDays: json['alertRetentionDays'] as int? ?? 7,
      maxLocalAlerts: json['maxLocalAlerts'] as int? ?? 100,
      hasUserConsent: json['hasUserConsent'] as bool? ?? false,
      autoResolveAlerts: json['autoResolveAlerts'] as bool? ?? true,
      autoResolveTimeoutHours: json['autoResolveTimeoutHours'] as int? ?? 24,
    );
  }
}

/// Service for managing monitoring alerts
class AlertService {
  final SharedPreferences _prefs;
  final LoggingService _loggingService;
  final AnalyticsService _analyticsService;
  final AchievementService _achievementService;
  final MascotService _mascotService;

  /// UUID generator for alert IDs
  static const _uuid = Uuid();

  /// Current session ID
  late final String _sessionId;

  /// Current user preferences
  AlertPreferences _preferences = const AlertPreferences();

  /// Active alerts
  final List<MonitoringAlert> _activeAlerts = [];

  /// Alert history
  final List<MonitoringAlert> _alertHistory = [];

  /// Whether service is initialized
  bool _isInitialized = false;

  /// Auto-resolve timer
  Timer? _autoResolveTimer;

  /// Storage keys
  static const String _preferencesKey = 'alert_preferences';
  static const String _activeAlertsKey = 'active_alerts';
  static const String _alertHistoryKey = 'alert_history';

  AlertService({
    required SharedPreferences prefs,
    required LoggingService loggingService,
    required AnalyticsService analyticsService,
    required AchievementService achievementService,
    required MascotService mascotService,
  })  : _prefs = prefs,
        _loggingService = loggingService,
        _analyticsService = analyticsService,
        _achievementService = achievementService,
        _mascotService = mascotService {
    _sessionId = _uuid.v4();
  }

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadPreferences();
      await _loadActiveAlerts();
      await _loadAlertHistory();

      if (isAlertsEnabled) {
        await _startAutoResolveTimer();
      }

      _isInitialized = true;

      _loggingService.info(
        'AlertService',
        'Service initialized',
        {
          'sessionId': _sessionId,
          'alertsEnabled': _preferences.alertsEnabled,
          'hasConsent': _preferences.hasUserConsent,
          'activeAlerts': _activeAlerts.length,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'AlertService',
        'Failed to initialize service',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Request user consent for alerts
  Future<bool> requestUserConsent() async {
    try {
      // TODO: Integrate with Alert Consent Dialog UI
      // POST /api/v1/monitoring/alerts/consent
      // Headers: Authorization, Content-Type
      // Body: { userId: string, consentGiven: boolean, timestamp: string }
      // Response: { success: boolean, consentId: string }

      // For now, we'll assume consent is given for development
      await updatePreferences(_preferences.copyWith(hasUserConsent: true));

      return true;
    } catch (e) {
      _loggingService.error(
        'AlertService',
        'Failed to request user consent',
        {'error': e.toString()},
      );
      return false;
    }
  }

  /// Update user preferences
  Future<void> updatePreferences(AlertPreferences preferences) async {
    try {
      _preferences = preferences;
      await _prefs.setString(_preferencesKey, jsonEncode(preferences.toJson()));

      // Restart auto-resolve timer if settings changed
      if (_preferences.autoResolveAlerts != preferences.autoResolveAlerts ||
          _preferences.autoResolveTimeoutHours !=
              preferences.autoResolveTimeoutHours) {
        await _restartAutoResolveTimer();
      }

      _loggingService.info(
        'AlertService',
        'Preferences updated',
        {'preferences': preferences.toJson()},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'AlertService',
        'Failed to update preferences',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Get current preferences
  AlertPreferences get preferences => _preferences;

  /// Check if alerts are enabled
  bool get isAlertsEnabled =>
      _preferences.alertsEnabled && _preferences.hasUserConsent;

  /// Create a new alert
  Future<MonitoringAlert?> createAlert({
    required String title,
    required String description,
    required AlertType type,
    required AlertPriority priority,
    required String source,
    Map<String, dynamic>? context,
    List<String>? recommendedActions,
    bool isUserVisible = true,
  }) async {
    if (!isAlertsEnabled) return null;

    // Check if we should show this priority level
    if (!_shouldShowPriority(priority)) return null;

    try {
      final alert = MonitoringAlert(
        id: _uuid.v4(),
        title: title,
        description: description,
        type: type,
        priority: priority,
        createdAt: DateTime.now(),
        source: source,
        context: context ?? {},
        recommendedActions: recommendedActions ?? [],
        isUserVisible: isUserVisible,
        sessionId: _sessionId,
      );

      _activeAlerts.add(alert);
      await _saveActiveAlerts();

      // Log alert creation
      await _analyticsService.logEvent(
        name: 'alert_created',
        category: AnalyticsCategory.userAction,
        parameters: {
          'alert_type': type.value,
          'priority': priority.value,
          'source': source,
          'session_id': _sessionId,
        },
      );

      // Show appropriate mascot expression for critical alerts
      if (priority == AlertPriority.critical) {
        await _mascotService.onError(
          errorMessage: title,
          errorType: 'critical_alert',
        );
      }

      // TODO: Integrate with Alert Creation API
      // POST /api/v1/monitoring/alerts/create
      // Headers: Authorization, Content-Type, X-Alert-Priority
      // Body: {
      //   alert: Alert,
      //   systemId: string,
      //   alertType: string,
      //   severity: string,
      //   context: {
      //     healthStatus: object,
      //     performanceMetrics: object,
      //     uxMetrics: object
      //   },
      //   timestamp: string,
      //   autoResolution: boolean
      // }
      // Response: {
      //   success: boolean,
      //   alertId: string,
      //   escalationLevel: string,
      //   estimatedResolutionTime: number,
      //   recommendedActions: [string]
      // }

      _loggingService.info(
        'AlertService',
        'Alert created',
        {
          'alertId': alert.id,
          'type': type.value,
          'priority': priority.value,
          'title': title,
        },
      );

      return alert;
    } catch (e, stackTrace) {
      _loggingService.error(
        'AlertService',
        'Failed to create alert',
        {'error': e.toString()},
        stackTrace,
      );
      return null;
    }
  }

  /// Acknowledge an alert
  Future<bool> acknowledgeAlert(String alertId) async {
    try {
      final alertIndex = _activeAlerts.indexWhere((a) => a.id == alertId);
      if (alertIndex == -1) return false;

      final alert = _activeAlerts[alertIndex];
      final acknowledgedAlert = alert.copyWith(
        status: AlertStatus.acknowledged,
        updatedAt: DateTime.now(),
      );

      _activeAlerts[alertIndex] = acknowledgedAlert;
      await _saveActiveAlerts();

      // Track achievement for alert management
      await _achievementService.trackUserAction(
        UserAction.appFeatureUsed,
        metadata: {
          'type': 'alert_acknowledged',
          'alertType': alert.type.value,
          'priority': alert.priority.value,
        },
      );

      _loggingService.info(
        'AlertService',
        'Alert acknowledged',
        {'alertId': alertId, 'title': alert.title},
      );

      return true;
    } catch (e, stackTrace) {
      _loggingService.error(
        'AlertService',
        'Failed to acknowledge alert',
        {'error': e.toString()},
        stackTrace,
      );
      return false;
    }
  }

  /// Resolve an alert
  Future<bool> resolveAlert(String alertId) async {
    try {
      final alertIndex = _activeAlerts.indexWhere((a) => a.id == alertId);
      if (alertIndex == -1) return false;

      final alert = _activeAlerts[alertIndex];
      final resolvedAlert = alert.copyWith(
        status: AlertStatus.resolved,
        updatedAt: DateTime.now(),
        resolvedAt: DateTime.now(),
      );

      _activeAlerts.removeAt(alertIndex);
      _alertHistory.insert(0, resolvedAlert);

      // Enforce history size limit
      if (_alertHistory.length > _preferences.maxLocalAlerts) {
        _alertHistory.removeRange(
            _preferences.maxLocalAlerts, _alertHistory.length);
      }

      await _saveActiveAlerts();
      await _saveAlertHistory();

      // Track achievement for alert resolution
      await _achievementService.trackUserAction(
        UserAction.appFeatureUsed,
        metadata: {
          'type': 'alert_resolved',
          'alertType': alert.type.value,
          'priority': alert.priority.value,
          'resolutionTime':
              DateTime.now().difference(alert.createdAt).inMinutes,
        },
      );

      _loggingService.info(
        'AlertService',
        'Alert resolved',
        {'alertId': alertId, 'title': alert.title},
      );

      return true;
    } catch (e, stackTrace) {
      _loggingService.error(
        'AlertService',
        'Failed to resolve alert',
        {'error': e.toString()},
        stackTrace,
      );
      return false;
    }
  }

  /// Get active alerts
  List<MonitoringAlert> getActiveAlerts({AlertPriority? priority}) {
    if (priority != null) {
      return _activeAlerts
          .where((alert) => alert.priority == priority)
          .toList();
    }
    return List.unmodifiable(_activeAlerts);
  }

  /// Get alert history
  List<MonitoringAlert> getAlertHistory({int limit = 50}) {
    return _alertHistory.take(limit).toList();
  }

  /// Get alert statistics
  Map<String, dynamic> getAlertStatistics() {
    final activeByPriority = <String, int>{};
    for (final priority in AlertPriority.values) {
      activeByPriority[priority.value] =
          _activeAlerts.where((alert) => alert.priority == priority).length;
    }

    final resolvedToday = _alertHistory
        .where((alert) =>
            alert.resolvedAt != null &&
            alert.resolvedAt!
                .isAfter(DateTime.now().subtract(const Duration(days: 1))))
        .length;

    return {
      'totalActive': _activeAlerts.length,
      'activeByPriority': activeByPriority,
      'totalResolved': _alertHistory.length,
      'resolvedToday': resolvedToday,
      'oldestActiveAlert': _activeAlerts.isNotEmpty
          ? _activeAlerts
              .map((a) => a.createdAt)
              .reduce((a, b) => a.isBefore(b) ? a : b)
              .toIso8601String()
          : null,
    };
  }

  /// Clear all alerts
  Future<void> clearAllAlerts() async {
    try {
      _activeAlerts.clear();
      _alertHistory.clear();
      await _prefs.remove(_activeAlertsKey);
      await _prefs.remove(_alertHistoryKey);

      _loggingService.info('AlertService', 'All alerts cleared');
    } catch (e, stackTrace) {
      _loggingService.error(
        'AlertService',
        'Failed to clear alerts',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Dispose of the service
  Future<void> dispose() async {
    _autoResolveTimer?.cancel();
    _loggingService.info('AlertService', 'Service disposed');
  }

  /// Load preferences from storage
  Future<void> _loadPreferences() async {
    try {
      final prefsJson = _prefs.getString(_preferencesKey);
      if (prefsJson != null) {
        final prefsMap = jsonDecode(prefsJson) as Map<String, dynamic>;
        _preferences = AlertPreferences.fromJson(prefsMap);
      }
    } catch (e) {
      _loggingService.warning(
        'AlertService',
        'Failed to load preferences, using defaults',
        {'error': e.toString()},
      );
    }
  }

  /// Load active alerts from storage
  Future<void> _loadActiveAlerts() async {
    try {
      final alertsJson = _prefs.getString(_activeAlertsKey);
      if (alertsJson != null) {
        final alertsList = jsonDecode(alertsJson) as List<dynamic>;
        _activeAlerts.clear();

        for (final alertMap in alertsList) {
          try {
            final alert =
                MonitoringAlert.fromJson(alertMap as Map<String, dynamic>);

            // Check if alert is within retention period
            final retentionDate = DateTime.now()
                .subtract(Duration(days: _preferences.alertRetentionDays));

            if (alert.createdAt.isAfter(retentionDate)) {
              _activeAlerts.add(alert);
            }
          } catch (e) {
            _loggingService.warning(
              'AlertService',
              'Failed to parse cached alert',
              {'error': e.toString()},
            );
          }
        }
      }
    } catch (e) {
      _loggingService.warning(
        'AlertService',
        'Failed to load active alerts',
        {'error': e.toString()},
      );
    }
  }

  /// Load alert history from storage
  Future<void> _loadAlertHistory() async {
    try {
      final historyJson = _prefs.getString(_alertHistoryKey);
      if (historyJson != null) {
        final historyList = jsonDecode(historyJson) as List<dynamic>;
        _alertHistory.clear();

        for (final alertMap in historyList) {
          try {
            final alert =
                MonitoringAlert.fromJson(alertMap as Map<String, dynamic>);

            // Check if alert is within retention period
            final retentionDate = DateTime.now()
                .subtract(Duration(days: _preferences.alertRetentionDays));

            if (alert.createdAt.isAfter(retentionDate)) {
              _alertHistory.add(alert);
            }
          } catch (e) {
            _loggingService.warning(
              'AlertService',
              'Failed to parse alert history',
              {'error': e.toString()},
            );
          }
        }
      }
    } catch (e) {
      _loggingService.warning(
        'AlertService',
        'Failed to load alert history',
        {'error': e.toString()},
      );
    }
  }

  /// Save active alerts to storage
  Future<void> _saveActiveAlerts() async {
    try {
      final alertsData = _activeAlerts.map((a) => a.toJson()).toList();
      await _prefs.setString(_activeAlertsKey, jsonEncode(alertsData));
    } catch (e) {
      _loggingService.warning(
        'AlertService',
        'Failed to save active alerts',
        {'error': e.toString()},
      );
    }
  }

  /// Save alert history to storage
  Future<void> _saveAlertHistory() async {
    try {
      final historyData = _alertHistory.map((a) => a.toJson()).toList();
      await _prefs.setString(_alertHistoryKey, jsonEncode(historyData));
    } catch (e) {
      _loggingService.warning(
        'AlertService',
        'Failed to save alert history',
        {'error': e.toString()},
      );
    }
  }

  /// Start auto-resolve timer
  Future<void> _startAutoResolveTimer() async {
    _autoResolveTimer?.cancel();

    if (!_preferences.autoResolveAlerts) return;

    _autoResolveTimer = Timer.periodic(const Duration(hours: 1), (timer) async {
      await _processAutoResolve();
    });

    _loggingService.info(
      'AlertService',
      'Auto-resolve timer started',
      {'timeoutHours': _preferences.autoResolveTimeoutHours},
    );
  }

  /// Restart auto-resolve timer
  Future<void> _restartAutoResolveTimer() async {
    _autoResolveTimer?.cancel();
    if (_preferences.autoResolveAlerts) {
      await _startAutoResolveTimer();
    }
  }

  /// Process auto-resolve for old alerts
  Future<void> _processAutoResolve() async {
    if (!_preferences.autoResolveAlerts) return;

    try {
      final cutoffTime = DateTime.now()
          .subtract(Duration(hours: _preferences.autoResolveTimeoutHours));

      final alertsToResolve = _activeAlerts
          .where((alert) =>
              alert.createdAt.isBefore(cutoffTime) &&
              alert.priority !=
                  AlertPriority.critical) // Don't auto-resolve critical alerts
          .toList();

      for (final alert in alertsToResolve) {
        await resolveAlert(alert.id);
      }

      if (alertsToResolve.isNotEmpty) {
        _loggingService.info(
          'AlertService',
          'Auto-resolved alerts',
          {'count': alertsToResolve.length},
        );
      }
    } catch (e) {
      _loggingService.warning(
        'AlertService',
        'Failed to process auto-resolve',
        {'error': e.toString()},
      );
    }
  }

  /// Check if we should show alerts of this priority
  bool _shouldShowPriority(AlertPriority priority) {
    switch (priority) {
      case AlertPriority.low:
        return _preferences.showLowPriorityAlerts;
      case AlertPriority.medium:
        return _preferences.showMediumPriorityAlerts;
      case AlertPriority.high:
        return _preferences.showHighPriorityAlerts;
      case AlertPriority.critical:
        return _preferences.showCriticalPriorityAlerts;
    }
  }
}
