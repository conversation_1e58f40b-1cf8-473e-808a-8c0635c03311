import 'dart:async';
import 'dart:convert';

import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import 'package:culture_connect/models/monitoring/health_check.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/providers/achievement_provider.dart'
    hide loggingServiceProvider, analyticsServiceProvider;
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/mascot_service.dart';

/// Provider for the SystemHealthService
final systemHealthServiceProvider = Provider<SystemHealthService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final analyticsService = ref.watch(analyticsServiceProvider);
  final achievementService = ref.watch(achievementServiceProvider);
  final mascotService = ref.watch(mascotServiceProvider);

  return SystemHealthService(
    prefs: prefs,
    loggingService: loggingService,
    analyticsService: analyticsService,
    achievementService: achievementService,
    mascotService: mascotService,
  );
});

/// System health monitoring preferences
class SystemHealthPreferences {
  /// Whether system health monitoring is enabled
  final bool healthMonitoringEnabled;

  /// Whether to monitor connectivity
  final bool monitorConnectivity;

  /// Whether to monitor storage
  final bool monitorStorage;

  /// Whether to monitor cache performance
  final bool monitorCache;

  /// Whether to monitor service availability
  final bool monitorServices;

  /// Health check interval in minutes
  final int healthCheckIntervalMinutes;

  /// Data retention period in days
  final int dataRetentionDays;

  /// Whether user has given consent for health monitoring
  final bool hasUserConsent;

  /// Maximum health checks to store locally
  final int maxLocalHealthChecks;

  /// Whether to send health reports to backend
  final bool sendHealthReports;

  const SystemHealthPreferences({
    this.healthMonitoringEnabled = true,
    this.monitorConnectivity = true,
    this.monitorStorage = true,
    this.monitorCache = true,
    this.monitorServices = true,
    this.healthCheckIntervalMinutes = 15,
    this.dataRetentionDays = 7,
    this.hasUserConsent = false,
    this.maxLocalHealthChecks = 100,
    this.sendHealthReports = false,
  });

  SystemHealthPreferences copyWith({
    bool? healthMonitoringEnabled,
    bool? monitorConnectivity,
    bool? monitorStorage,
    bool? monitorCache,
    bool? monitorServices,
    int? healthCheckIntervalMinutes,
    int? dataRetentionDays,
    bool? hasUserConsent,
    int? maxLocalHealthChecks,
    bool? sendHealthReports,
  }) {
    return SystemHealthPreferences(
      healthMonitoringEnabled:
          healthMonitoringEnabled ?? this.healthMonitoringEnabled,
      monitorConnectivity: monitorConnectivity ?? this.monitorConnectivity,
      monitorStorage: monitorStorage ?? this.monitorStorage,
      monitorCache: monitorCache ?? this.monitorCache,
      monitorServices: monitorServices ?? this.monitorServices,
      healthCheckIntervalMinutes:
          healthCheckIntervalMinutes ?? this.healthCheckIntervalMinutes,
      dataRetentionDays: dataRetentionDays ?? this.dataRetentionDays,
      hasUserConsent: hasUserConsent ?? this.hasUserConsent,
      maxLocalHealthChecks: maxLocalHealthChecks ?? this.maxLocalHealthChecks,
      sendHealthReports: sendHealthReports ?? this.sendHealthReports,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'healthMonitoringEnabled': healthMonitoringEnabled,
      'monitorConnectivity': monitorConnectivity,
      'monitorStorage': monitorStorage,
      'monitorCache': monitorCache,
      'monitorServices': monitorServices,
      'healthCheckIntervalMinutes': healthCheckIntervalMinutes,
      'dataRetentionDays': dataRetentionDays,
      'hasUserConsent': hasUserConsent,
      'maxLocalHealthChecks': maxLocalHealthChecks,
      'sendHealthReports': sendHealthReports,
    };
  }

  factory SystemHealthPreferences.fromJson(Map<String, dynamic> json) {
    return SystemHealthPreferences(
      healthMonitoringEnabled: json['healthMonitoringEnabled'] as bool? ?? true,
      monitorConnectivity: json['monitorConnectivity'] as bool? ?? true,
      monitorStorage: json['monitorStorage'] as bool? ?? true,
      monitorCache: json['monitorCache'] as bool? ?? true,
      monitorServices: json['monitorServices'] as bool? ?? true,
      healthCheckIntervalMinutes:
          json['healthCheckIntervalMinutes'] as int? ?? 15,
      dataRetentionDays: json['dataRetentionDays'] as int? ?? 7,
      hasUserConsent: json['hasUserConsent'] as bool? ?? false,
      maxLocalHealthChecks: json['maxLocalHealthChecks'] as int? ?? 100,
      sendHealthReports: json['sendHealthReports'] as bool? ?? false,
    );
  }
}

/// Service for monitoring system health and performance
class SystemHealthService {
  final SharedPreferences _prefs;
  final LoggingService _loggingService;
  final AnalyticsService _analyticsService;
  final AchievementService _achievementService;
  final MascotService _mascotService;

  /// UUID generator for health check IDs
  static const _uuid = Uuid();

  /// Current session ID
  late final String _sessionId;

  /// Current user preferences
  SystemHealthPreferences _preferences = const SystemHealthPreferences();

  /// Cached health checks
  final List<HealthCheck> _cachedHealthChecks = [];

  /// Whether service is initialized
  bool _isInitialized = false;

  /// Health check timer
  Timer? _healthCheckTimer;

  /// Connectivity subscription
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// Current connectivity status
  ConnectivityResult _currentConnectivity = ConnectivityResult.none;

  /// Storage keys
  static const String _preferencesKey = 'system_health_preferences';
  static const String _healthChecksKey = 'system_health_checks';

  SystemHealthService({
    required SharedPreferences prefs,
    required LoggingService loggingService,
    required AnalyticsService analyticsService,
    required AchievementService achievementService,
    required MascotService mascotService,
  })  : _prefs = prefs,
        _loggingService = loggingService,
        _analyticsService = analyticsService,
        _achievementService = achievementService,
        _mascotService = mascotService {
    _sessionId = _uuid.v4();
  }

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadPreferences();
      await _loadCachedHealthChecks();
      await _initializeConnectivityMonitoring();

      if (isHealthMonitoringEnabled) {
        await _startHealthCheckTimer();
      }

      _isInitialized = true;

      _loggingService.info(
        'SystemHealthService',
        'Service initialized',
        {
          'sessionId': _sessionId,
          'healthMonitoringEnabled': _preferences.healthMonitoringEnabled,
          'hasConsent': _preferences.hasUserConsent,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'SystemHealthService',
        'Failed to initialize service',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Request user consent for health monitoring
  Future<bool> requestUserConsent() async {
    try {
      // TODO: Integrate with Health Monitoring Consent Dialog UI
      // POST /api/v1/monitoring/health/consent
      // Headers: Authorization, Content-Type
      // Body: { userId: string, consentGiven: boolean, timestamp: string }
      // Response: { success: boolean, consentId: string }

      // For now, we'll assume consent is given for development
      await updatePreferences(_preferences.copyWith(hasUserConsent: true));

      return true;
    } catch (e) {
      _loggingService.error(
        'SystemHealthService',
        'Failed to request user consent',
        {'error': e.toString()},
      );
      return false;
    }
  }

  /// Update user preferences
  Future<void> updatePreferences(SystemHealthPreferences preferences) async {
    try {
      final oldPreferences = _preferences;
      _preferences = preferences;
      await _prefs.setString(_preferencesKey, jsonEncode(preferences.toJson()));

      // Restart health monitoring if settings changed
      if (oldPreferences.healthMonitoringEnabled !=
              preferences.healthMonitoringEnabled ||
          oldPreferences.healthCheckIntervalMinutes !=
              preferences.healthCheckIntervalMinutes) {
        await _restartHealthMonitoring();
      }

      _loggingService.info(
        'SystemHealthService',
        'Preferences updated',
        {'preferences': preferences.toJson()},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'SystemHealthService',
        'Failed to update preferences',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Get current preferences
  SystemHealthPreferences get preferences => _preferences;

  /// Check if health monitoring is enabled
  bool get isHealthMonitoringEnabled =>
      _preferences.healthMonitoringEnabled && _preferences.hasUserConsent;

  /// Perform comprehensive health check
  Future<HealthCheck> performHealthCheck() async {
    try {
      final healthCheck = HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.comprehensive,
        sessionId: _sessionId,
      );

      // Perform individual checks
      final connectivityCheck = await _performConnectivityCheck();
      final storageCheck = await _performStorageCheck();
      final cacheCheck = await _performCacheCheck();
      final serviceCheck = await _performServiceCheck();

      // Aggregate results
      final checks = [
        connectivityCheck,
        storageCheck,
        cacheCheck,
        serviceCheck
      ];
      final overallStatus = _determineOverallStatus(checks);

      final completedHealthCheck = healthCheck.copyWith(
        status: overallStatus,
        details: {
          'connectivity': connectivityCheck.toJson(),
          'storage': storageCheck.toJson(),
          'cache': cacheCheck.toJson(),
          'service': serviceCheck.toJson(),
          'checksPerformed': checks.length,
          'healthyChecks':
              checks.where((c) => c.status == HealthStatus.healthy).length,
        },
        duration: DateTime.now().difference(healthCheck.timestamp),
      );

      await _recordHealthCheck(completedHealthCheck);

      // Show appropriate mascot expression based on health status
      await _showHealthStatusExpression(overallStatus);

      // Track achievement for health monitoring
      await _trackHealthAchievements(completedHealthCheck);

      _loggingService.info(
        'SystemHealthService',
        'Health check completed',
        {
          'healthCheckId': completedHealthCheck.id,
          'status': overallStatus.value,
          'duration': completedHealthCheck.duration?.inMilliseconds,
        },
      );

      return completedHealthCheck;
    } catch (e, stackTrace) {
      _loggingService.error(
        'SystemHealthService',
        'Failed to perform health check',
        {'error': e.toString()},
        stackTrace,
      );

      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.comprehensive,
        status: HealthStatus.critical,
        sessionId: _sessionId,
        errorMessage: e.toString(),
      );
    }
  }

  /// Get recent health checks
  List<HealthCheck> getRecentHealthChecks({int limit = 10}) {
    return _cachedHealthChecks.take(limit).toList();
  }

  /// Get health status summary
  Map<String, dynamic> getHealthStatusSummary() {
    if (_cachedHealthChecks.isEmpty) {
      return {
        'overallStatus': 'unknown',
        'lastCheckTime': null,
        'healthyChecks': 0,
        'totalChecks': 0,
        'connectivity': 'unknown',
      };
    }

    final recentChecks = _cachedHealthChecks.take(10).toList();
    final healthyChecks =
        recentChecks.where((c) => c.status == HealthStatus.healthy).length;
    final lastCheck = _cachedHealthChecks.first;

    return {
      'overallStatus': lastCheck.status.value,
      'lastCheckTime': lastCheck.timestamp.toIso8601String(),
      'healthyChecks': healthyChecks,
      'totalChecks': recentChecks.length,
      'connectivity': _currentConnectivity.name,
      'healthScore': (healthyChecks / recentChecks.length * 100).round(),
    };
  }

  /// Export health data for privacy compliance
  Future<Map<String, dynamic>> exportHealthData() async {
    try {
      final data = {
        'sessionId': _sessionId,
        'preferences': _preferences.toJson(),
        'healthChecks': _cachedHealthChecks.map((h) => h.toJson()).toList(),
        'exportTimestamp': DateTime.now().toIso8601String(),
      };

      _loggingService.info(
        'SystemHealthService',
        'Health data exported',
        {'healthChecksCount': _cachedHealthChecks.length},
      );

      return data;
    } catch (e, stackTrace) {
      _loggingService.error(
        'SystemHealthService',
        'Failed to export health data',
        {'error': e.toString()},
        stackTrace,
      );
      return {};
    }
  }

  /// Clear all health data
  Future<void> clearAllHealthData() async {
    try {
      _cachedHealthChecks.clear();
      await _prefs.remove(_healthChecksKey);

      _loggingService.info('SystemHealthService', 'All health data cleared');
    } catch (e, stackTrace) {
      _loggingService.error(
        'SystemHealthService',
        'Failed to clear health data',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Dispose of the service
  Future<void> dispose() async {
    _healthCheckTimer?.cancel();
    await _connectivitySubscription?.cancel();
    _loggingService.info('SystemHealthService', 'Service disposed');
  }

  /// Load preferences from storage
  Future<void> _loadPreferences() async {
    try {
      final prefsJson = _prefs.getString(_preferencesKey);
      if (prefsJson != null) {
        final prefsMap = jsonDecode(prefsJson) as Map<String, dynamic>;
        _preferences = SystemHealthPreferences.fromJson(prefsMap);
      }
    } catch (e) {
      _loggingService.warning(
        'SystemHealthService',
        'Failed to load preferences, using defaults',
        {'error': e.toString()},
      );
    }
  }

  /// Load cached health checks from storage
  Future<void> _loadCachedHealthChecks() async {
    try {
      final healthChecksJson = _prefs.getString(_healthChecksKey);
      if (healthChecksJson != null) {
        final healthChecksList = jsonDecode(healthChecksJson) as List<dynamic>;
        _cachedHealthChecks.clear();

        for (final healthCheckMap in healthChecksList) {
          try {
            final healthCheck =
                HealthCheck.fromJson(healthCheckMap as Map<String, dynamic>);

            // Check if health check is within retention period
            final retentionDate = DateTime.now()
                .subtract(Duration(days: _preferences.dataRetentionDays));

            if (healthCheck.timestamp.isAfter(retentionDate)) {
              _cachedHealthChecks.add(healthCheck);
            }
          } catch (e) {
            _loggingService.warning(
              'SystemHealthService',
              'Failed to parse cached health check',
              {'error': e.toString()},
            );
          }
        }
      }
    } catch (e) {
      _loggingService.warning(
        'SystemHealthService',
        'Failed to load cached health checks',
        {'error': e.toString()},
      );
    }
  }

  /// Initialize connectivity monitoring
  Future<void> _initializeConnectivityMonitoring() async {
    try {
      final connectivity = Connectivity();
      _currentConnectivity = await connectivity.checkConnectivity();

      _connectivitySubscription = connectivity.onConnectivityChanged.listen(
        (ConnectivityResult result) {
          _currentConnectivity = result;
          _loggingService.debug(
            'SystemHealthService',
            'Connectivity changed',
            {'connectivity': result.name},
          );
        },
      );
    } catch (e) {
      _loggingService.warning(
        'SystemHealthService',
        'Failed to initialize connectivity monitoring',
        {'error': e.toString()},
      );
    }
  }

  /// Start health check timer
  Future<void> _startHealthCheckTimer() async {
    _healthCheckTimer?.cancel();

    if (!isHealthMonitoringEnabled) return;

    final interval = Duration(minutes: _preferences.healthCheckIntervalMinutes);
    _healthCheckTimer = Timer.periodic(interval, (timer) async {
      if (isHealthMonitoringEnabled) {
        await performHealthCheck();
      } else {
        timer.cancel();
      }
    });

    _loggingService.info(
      'SystemHealthService',
      'Health check timer started',
      {'intervalMinutes': _preferences.healthCheckIntervalMinutes},
    );
  }

  /// Restart health monitoring
  Future<void> _restartHealthMonitoring() async {
    _healthCheckTimer?.cancel();
    if (isHealthMonitoringEnabled) {
      await _startHealthCheckTimer();
    }
  }

  /// Perform connectivity check
  Future<HealthCheck> _performConnectivityCheck() async {
    if (!_preferences.monitorConnectivity) {
      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.connectivity,
        status: HealthStatus.healthy,
        sessionId: _sessionId,
        details: const {'skipped': true},
      );
    }

    try {
      final connectivity = Connectivity();
      final result = await connectivity.checkConnectivity();

      final status = result == ConnectivityResult.none
          ? HealthStatus.critical
          : HealthStatus.healthy;

      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.connectivity,
        status: status,
        sessionId: _sessionId,
        details: {
          'connectivity': result.name,
          'hasConnection': result != ConnectivityResult.none,
        },
      );
    } catch (e) {
      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.connectivity,
        status: HealthStatus.critical,
        sessionId: _sessionId,
        errorMessage: e.toString(),
      );
    }
  }

  /// Perform storage check
  Future<HealthCheck> _performStorageCheck() async {
    if (!_preferences.monitorStorage) {
      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.storage,
        status: HealthStatus.healthy,
        sessionId: _sessionId,
        details: const {'skipped': true},
      );
    }

    try {
      // Simulate storage check (actual implementation would use platform-specific APIs)
      const totalSpace = 64 * 1024 * 1024 * 1024; // 64GB
      const usedSpace = 32 * 1024 * 1024 * 1024; // 32GB
      const availableSpace = totalSpace - usedSpace;
      const usagePercentage = (usedSpace / totalSpace) * 100;

      HealthStatus status;
      if (usagePercentage > 90) {
        status = HealthStatus.critical;
      } else if (usagePercentage > 80) {
        status = HealthStatus.warning;
      } else {
        status = HealthStatus.healthy;
      }

      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.storage,
        status: status,
        sessionId: _sessionId,
        details: {
          'totalSpace': totalSpace,
          'usedSpace': usedSpace,
          'availableSpace': availableSpace,
          'usagePercentage': usagePercentage,
        },
        measurements: {
          'usagePercentage': usagePercentage,
          'availableGB': availableSpace / (1024 * 1024 * 1024),
        },
        thresholds: {
          'warningThreshold': 80.0,
          'criticalThreshold': 90.0,
        },
      );
    } catch (e) {
      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.storage,
        status: HealthStatus.critical,
        sessionId: _sessionId,
        errorMessage: e.toString(),
      );
    }
  }

  /// Perform cache check
  Future<HealthCheck> _performCacheCheck() async {
    if (!_preferences.monitorCache) {
      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.cache,
        status: HealthStatus.healthy,
        sessionId: _sessionId,
        details: const {'skipped': true},
      );
    }

    try {
      // Simulate cache performance check
      const cacheHitRate = 0.85; // 85% hit rate
      const cacheSize = 50 * 1024 * 1024; // 50MB
      const maxCacheSize = 100 * 1024 * 1024; // 100MB

      HealthStatus status;
      if (cacheHitRate < 0.5) {
        status = HealthStatus.critical;
      } else if (cacheHitRate < 0.7) {
        status = HealthStatus.warning;
      } else {
        status = HealthStatus.healthy;
      }

      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.cache,
        status: status,
        sessionId: _sessionId,
        details: {
          'cacheHitRate': cacheHitRate,
          'cacheSize': cacheSize,
          'maxCacheSize': maxCacheSize,
          'cacheUsagePercentage': (cacheSize / maxCacheSize) * 100,
        },
        measurements: {
          'hitRate': cacheHitRate * 100,
          'sizeMB': cacheSize / (1024 * 1024),
        },
        thresholds: {
          'minHitRate': 70.0,
          'maxSizePercentage': 80.0,
        },
      );
    } catch (e) {
      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.cache,
        status: HealthStatus.critical,
        sessionId: _sessionId,
        errorMessage: e.toString(),
      );
    }
  }

  /// Perform service availability check
  Future<HealthCheck> _performServiceCheck() async {
    if (!_preferences.monitorServices) {
      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.service,
        status: HealthStatus.healthy,
        sessionId: _sessionId,
        details: const {'skipped': true},
      );
    }

    try {
      // Simulate service availability check
      const servicesChecked = 3;
      const servicesAvailable = 3;
      const averageResponseTime = 150; // ms

      HealthStatus status;
      if (servicesAvailable < servicesChecked) {
        status = HealthStatus.critical;
      } else if (averageResponseTime > 1000) {
        status = HealthStatus.warning;
      } else {
        status = HealthStatus.healthy;
      }

      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.service,
        status: status,
        sessionId: _sessionId,
        details: const {
          'servicesChecked': servicesChecked,
          'servicesAvailable': servicesAvailable,
          'averageResponseTime': averageResponseTime,
          'availabilityPercentage': (servicesAvailable / servicesChecked) * 100,
        },
        measurements: {
          'responseTimeMs': averageResponseTime.toDouble(),
          'availabilityPercent': (servicesAvailable / servicesChecked) * 100,
        },
        thresholds: const {
          'maxResponseTime': 1000.0,
          'minAvailability': 95.0,
        },
      );
    } catch (e) {
      return HealthCheck(
        id: _uuid.v4(),
        timestamp: DateTime.now(),
        type: HealthCheckType.service,
        status: HealthStatus.critical,
        sessionId: _sessionId,
        errorMessage: e.toString(),
      );
    }
  }

  /// Determine overall health status from individual checks
  HealthStatus _determineOverallStatus(List<HealthCheck> checks) {
    if (checks.any((check) => check.status == HealthStatus.critical)) {
      return HealthStatus.critical;
    }
    if (checks.any((check) => check.status == HealthStatus.warning)) {
      return HealthStatus.warning;
    }
    if (checks.every((check) => check.status == HealthStatus.healthy)) {
      return HealthStatus.healthy;
    }
    return HealthStatus.unknown;
  }

  /// Record a health check
  Future<void> _recordHealthCheck(HealthCheck healthCheck) async {
    try {
      _cachedHealthChecks.insert(
          0, healthCheck); // Add to beginning for latest first

      // Enforce cache size limit
      if (_cachedHealthChecks.length > _preferences.maxLocalHealthChecks) {
        _cachedHealthChecks.removeRange(
            _preferences.maxLocalHealthChecks, _cachedHealthChecks.length);
      }

      await _saveHealthChecks();

      // Log to analytics
      await _analyticsService.logEvent(
        name: 'health_check_completed',
        category: AnalyticsCategory.userAction,
        parameters: {
          'check_type': healthCheck.type.value,
          'status': healthCheck.status.value,
          'duration_ms': healthCheck.duration?.inMilliseconds ?? 0,
          'session_id': _sessionId,
        },
      );

      // TODO: Integrate with Health Check Reporting API
      // POST /api/v1/monitoring/health/report
      // Headers: Authorization, Content-Type, X-Session-ID, X-Health-Level
      // Body: {
      //   healthCheck: HealthCheck,
      //   systemId: string (anonymized),
      //   sessionId: string,
      //   deviceInfo: {
      //     platform: string,
      //     version: string,
      //     model: string,
      //     osVersion: string
      //   },
      //   appVersion: string,
      //   timestamp: string,
      //   overallHealth: string
      // }
      // Response: {
      //   success: boolean,
      //   checksProcessed: number,
      //   recommendations: [HealthRecommendation],
      //   alerts: [SystemAlert],
      //   nextCheckInterval: number
      // }
    } catch (e, stackTrace) {
      _loggingService.error(
        'SystemHealthService',
        'Failed to record health check',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Save health checks to storage
  Future<void> _saveHealthChecks() async {
    try {
      final healthChecksData =
          _cachedHealthChecks.map((h) => h.toJson()).toList();
      await _prefs.setString(_healthChecksKey, jsonEncode(healthChecksData));
    } catch (e) {
      _loggingService.warning(
        'SystemHealthService',
        'Failed to save health checks',
        {'error': e.toString()},
      );
    }
  }

  /// Show health status expression on mascot
  Future<void> _showHealthStatusExpression(HealthStatus status) async {
    try {
      switch (status) {
        case HealthStatus.healthy:
          await _mascotService.onBookingSuccessful(
            bookingType: 'Health Check',
            bookingId: 'system_health',
          );
          break;
        case HealthStatus.warning:
          await _mascotService.onError(
            errorMessage: 'System health needs attention',
            errorType: 'health_warning',
          );
          break;
        case HealthStatus.critical:
          await _mascotService.onError(
            errorMessage: 'Critical system health issues detected',
            errorType: 'health_critical',
          );
          break;
        case HealthStatus.unknown:
          // No specific expression for unknown status
          break;
      }
    } catch (e) {
      _loggingService.warning(
        'SystemHealthService',
        'Failed to show health status expression',
        {'error': e.toString()},
      );
    }
  }

  /// Track health-related achievements
  Future<void> _trackHealthAchievements(HealthCheck healthCheck) async {
    try {
      // Track health check completion achievement
      await _achievementService.trackUserAction(
        UserAction.appFeatureUsed,
        metadata: {
          'type': 'health_check_completed',
          'checkType': healthCheck.type.value,
          'status': healthCheck.status.value,
          'duration': healthCheck.duration?.inSeconds ?? 0,
        },
      );

      // Track healthy system achievement
      if (healthCheck.status == HealthStatus.healthy) {
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {
            'type': 'healthy_system_check',
            'checkType': healthCheck.type.value,
          },
        );
      }

      // Track system guardian achievement (multiple healthy checks)
      final recentHealthyChecks = _cachedHealthChecks
          .take(10)
          .where((c) => c.status == HealthStatus.healthy)
          .length;

      if (recentHealthyChecks >= 8) {
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {
            'type': 'system_guardian',
            'healthyChecks': recentHealthyChecks,
          },
        );
      }
    } catch (e) {
      _loggingService.warning(
        'SystemHealthService',
        'Failed to track health achievements',
        {'error': e.toString()},
      );
    }
  }
}
