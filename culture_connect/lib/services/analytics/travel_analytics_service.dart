// Flutter imports
import 'dart:convert';
import 'package:flutter/foundation.dart';

// Package imports
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/models/analytics/travel_analytics_model.dart';
import 'package:culture_connect/services/analytics_service.dart';

/// Service for collecting and aggregating travel analytics data
class TravelAnalyticsService {
  final SharedPreferences _prefs;
  final AnalyticsService _analyticsService;

  /// Storage keys
  static const String _spendingDataKey = 'travel_spending_data';
  static const String _patternDataKey = 'travel_pattern_data';
  static const String _preferencesKey = 'analytics_preferences';
  static const String _lastSyncKey = 'analytics_last_sync';

  /// Creates a new travel analytics service
  TravelAnalyticsService({
    required SharedPreferences prefs,
    required AnalyticsService analyticsService,
  })  : _prefs = prefs,
        _analyticsService = analyticsService;

  /// Get analytics preferences
  Future<AnalyticsPreferences> getPreferences() async {
    try {
      final prefsJson = _prefs.getString(_preferencesKey);
      if (prefsJson != null) {
        final prefsMap = jsonDecode(prefsJson) as Map<String, dynamic>;
        return AnalyticsPreferences.fromJson(prefsMap);
      }
    } catch (e) {
      debugPrint('Error loading analytics preferences: $e');
    }
    return AnalyticsPreferences.defaultPreferences();
  }

  /// Update analytics preferences
  Future<void> updatePreferences(AnalyticsPreferences preferences) async {
    try {
      final prefsJson = jsonEncode(preferences.toJson());
      await _prefs.setString(_preferencesKey, prefsJson);

      // Log preference change
      await _analyticsService.logEvent(
        name: 'analytics_preferences_updated',
        category: AnalyticsCategory.userAction,
        parameters: {
          'analytics_enabled': preferences.analyticsEnabled,
          'spending_tracking': preferences.spendingTrackingEnabled,
          'pattern_analysis': preferences.patternAnalysisEnabled,
        },
      );
    } catch (e) {
      debugPrint('Error updating analytics preferences: $e');
    }
  }

  /// Calculate travel spending from booking data
  Future<TravelSpending> calculateSpending({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final preferences = await getPreferences();
    if (!preferences.spendingTrackingEnabled) {
      return TravelSpending.empty(userId);
    }

    try {
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year, 1, 1);
      final end = endDate ?? DateTime(now.year, 12, 31);

      // Aggregate spending from all travel services
      final categoryBreakdown = <TravelServiceCategory, double>{};
      final monthlySpending = <String, double>{};
      var totalAmount = 0.0;
      var totalBookings = 0;
      var totalSavings = 0.0;

      // Get flight bookings
      final flightSpending = await _calculateFlightSpending(start, end);
      if (flightSpending.isNotEmpty) {
        categoryBreakdown[TravelServiceCategory.flights] =
            flightSpending.values.fold(0.0, (sum, amount) => sum + amount);
        totalAmount += categoryBreakdown[TravelServiceCategory.flights]!;
        totalBookings += flightSpending.length;
        totalSavings += _calculateFlightSavings(flightSpending);
      }

      // Get insurance spending
      final insuranceSpending = await _calculateInsuranceSpending(start, end);
      if (insuranceSpending.isNotEmpty) {
        categoryBreakdown[TravelServiceCategory.insurance] =
            insuranceSpending.values.fold(0.0, (sum, amount) => sum + amount);
        totalAmount += categoryBreakdown[TravelServiceCategory.insurance]!;
        totalBookings += insuranceSpending.length;
      }

      // Get visa spending
      final visaSpending = await _calculateVisaSpending(start, end);
      if (visaSpending.isNotEmpty) {
        categoryBreakdown[TravelServiceCategory.visa] =
            visaSpending.values.fold(0.0, (sum, amount) => sum + amount);
        totalAmount += categoryBreakdown[TravelServiceCategory.visa]!;
        totalBookings += visaSpending.length;
      }

      // Get transfer spending
      final transferSpending = await _calculateTransferSpending(start, end);
      if (transferSpending.isNotEmpty) {
        categoryBreakdown[TravelServiceCategory.transfers] =
            transferSpending.values.fold(0.0, (sum, amount) => sum + amount);
        totalAmount += categoryBreakdown[TravelServiceCategory.transfers]!;
        totalBookings += transferSpending.length;
      }

      // Calculate monthly breakdown
      await _calculateMonthlySpending(monthlySpending, start, end);

      final spending = TravelSpending(
        id: 'spending_${userId}_${start.year}',
        userId: userId,
        totalAmount: totalAmount,
        currency: 'USD', // TODO: Get user's preferred currency
        categoryBreakdown: categoryBreakdown,
        monthlySpending: monthlySpending,
        totalSavings: totalSavings,
        totalBookings: totalBookings,
        startDate: start,
        endDate: end,
        lastUpdated: DateTime.now(),
      );

      // Save spending data
      await _saveSpendingData(spending);

      // Check for spending-related achievements
      await _checkSpendingAchievements(spending);

      return spending;
    } catch (e) {
      debugPrint('Error calculating travel spending: $e');
      return TravelSpending.empty(userId);
    }
  }

  /// Analyze travel patterns
  Future<TravelPattern> analyzePatterns({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final preferences = await getPreferences();
    if (!preferences.patternAnalysisEnabled) {
      return TravelPattern.empty(userId);
    }

    try {
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year - 1, 1, 1);
      final end = endDate ?? DateTime(now.year, 12, 31);

      // Analyze booking patterns
      final topDestinations = await _analyzeDestinations(start, end);
      final averageAdvanceBooking =
          await _calculateAverageAdvanceBooking(start, end);
      final preferredBookingDay = await _findPreferredBookingDay(start, end);
      final mostActiveMonth = await _findMostActiveMonth(start, end);
      final travelFrequency = await _calculateTravelFrequency(start, end);
      final travelPreferences = await _analyzeTravelPreferences(start, end);
      final seasonalPatterns = await _analyzeSeasonalPatterns(start, end);

      final pattern = TravelPattern(
        id: 'pattern_${userId}_${start.year}',
        userId: userId,
        topDestinations: topDestinations,
        averageAdvanceBookingDays: averageAdvanceBooking,
        preferredBookingDay: preferredBookingDay,
        mostActiveMonth: mostActiveMonth,
        travelFrequency: travelFrequency,
        travelPreferences: travelPreferences,
        seasonalPatterns: seasonalPatterns,
        lastUpdated: DateTime.now(),
      );

      // Save pattern data
      await _savePatternData(pattern);

      // Check for pattern-related achievements
      await _checkPatternAchievements(pattern);

      return pattern;
    } catch (e) {
      debugPrint('Error analyzing travel patterns: $e');
      return TravelPattern.empty(userId);
    }
  }

  /// Get destination insights
  Future<List<DestinationInsight>> getDestinationInsights({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year - 1, 1, 1);
      final end = endDate ?? DateTime(now.year, 12, 31);

      final insights = <DestinationInsight>[];
      final destinations = await _analyzeDestinations(start, end);

      for (final entry in destinations.entries) {
        final destination = entry.key;
        final visitCount = entry.value;

        final spending = await _getDestinationSpending(destination, start, end);
        final serviceUsage =
            await _getDestinationServiceUsage(destination, start, end);
        final lastVisit = await _getLastVisitDate(destination, start, end);

        insights.add(DestinationInsight(
          destination: destination,
          visitCount: visitCount,
          totalSpending: spending,
          averageSpending: visitCount > 0 ? spending / visitCount : 0.0,
          currency: 'USD', // TODO: Get destination currency
          lastVisit: lastVisit,
          serviceUsage: serviceUsage,
        ));
      }

      // Sort by visit count descending
      insights.sort((a, b) => b.visitCount.compareTo(a.visitCount));

      return insights;
    } catch (e) {
      debugPrint('Error getting destination insights: $e');
      return [];
    }
  }

  /// Export user analytics data
  Future<Map<String, dynamic>> exportUserData({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final spending = await calculateSpending(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );

      final patterns = await analyzePatterns(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );

      final insights = await getDestinationInsights(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );

      final preferences = await getPreferences();

      final exportData = {
        'userId': userId,
        'exportDate': DateTime.now().toIso8601String(),
        'dateRange': {
          'start': startDate?.toIso8601String(),
          'end': endDate?.toIso8601String(),
        },
        'travelSpending': spending.toJson(),
        'travelPatterns': patterns.toJson(),
        'destinationInsights': insights.map((i) => i.toJson()).toList(),
        'preferences': preferences.toJson(),
      };

      // Log export event
      await _analyticsService.logEvent(
        name: 'analytics_data_exported',
        category: AnalyticsCategory.userAction,
        parameters: {
          'export_size_kb': (jsonEncode(exportData).length / 1024).round(),
          'date_range_days': startDate != null && endDate != null
              ? endDate.difference(startDate).inDays
              : null,
        },
      );

      return exportData;
    } catch (e) {
      debugPrint('Error exporting user data: $e');
      return {};
    }
  }

  /// Clear analytics data
  Future<void> clearAnalyticsData() async {
    try {
      await _prefs.remove(_spendingDataKey);
      await _prefs.remove(_patternDataKey);
      await _prefs.remove(_lastSyncKey);

      await _analyticsService.logEvent(
        name: 'analytics_data_cleared',
        category: AnalyticsCategory.userAction,
      );
    } catch (e) {
      debugPrint('Error clearing analytics data: $e');
    }
  }

  /// Private helper methods
  Future<Map<String, double>> _calculateFlightSpending(
      DateTime start, DateTime end) async {
    // TODO: Integrate with flight booking service to get actual spending data
    return {};
  }

  Future<Map<String, double>> _calculateInsuranceSpending(
      DateTime start, DateTime end) async {
    // TODO: Integrate with insurance service to get actual spending data
    return {};
  }

  Future<Map<String, double>> _calculateVisaSpending(
      DateTime start, DateTime end) async {
    // TODO: Integrate with visa service to get actual spending data
    return {};
  }

  Future<Map<String, double>> _calculateTransferSpending(
      DateTime start, DateTime end) async {
    // TODO: Integrate with transfer service to get actual spending data
    return {};
  }

  double _calculateFlightSavings(Map<String, double> flightSpending) {
    // TODO: Calculate savings compared to competitor prices
    return flightSpending.values
        .fold(0.0, (sum, amount) => sum + (amount * 0.15));
  }

  Future<void> _calculateMonthlySpending(
      Map<String, double> monthlySpending, DateTime start, DateTime end) async {
    // TODO: Aggregate monthly spending from all services
  }

  Future<Map<String, int>> _analyzeDestinations(
      DateTime start, DateTime end) async {
    // TODO: Analyze destinations from booking data
    return {};
  }

  Future<double> _calculateAverageAdvanceBooking(
      DateTime start, DateTime end) async {
    // TODO: Calculate average advance booking days
    return 0.0;
  }

  Future<String> _findPreferredBookingDay(DateTime start, DateTime end) async {
    // TODO: Find preferred booking day of week
    return 'Unknown';
  }

  Future<String> _findMostActiveMonth(DateTime start, DateTime end) async {
    // TODO: Find most active travel month
    return 'Unknown';
  }

  Future<double> _calculateTravelFrequency(DateTime start, DateTime end) async {
    // TODO: Calculate travel frequency
    return 0.0;
  }

  Future<Map<String, int>> _analyzeTravelPreferences(
      DateTime start, DateTime end) async {
    // TODO: Analyze travel preferences
    return {};
  }

  Future<Map<String, int>> _analyzeSeasonalPatterns(
      DateTime start, DateTime end) async {
    // TODO: Analyze seasonal travel patterns
    return {};
  }

  Future<double> _getDestinationSpending(
      String destination, DateTime start, DateTime end) async {
    // TODO: Get spending for specific destination
    return 0.0;
  }

  Future<Map<TravelServiceCategory, int>> _getDestinationServiceUsage(
      String destination, DateTime start, DateTime end) async {
    // TODO: Get service usage for destination
    return {};
  }

  Future<DateTime?> _getLastVisitDate(
      String destination, DateTime start, DateTime end) async {
    // TODO: Get last visit date for destination
    return null;
  }

  Future<void> _saveSpendingData(TravelSpending spending) async {
    try {
      final spendingJson = jsonEncode(spending.toJson());
      await _prefs.setString(_spendingDataKey, spendingJson);
    } catch (e) {
      debugPrint('Error saving spending data: $e');
    }
  }

  Future<void> _savePatternData(TravelPattern pattern) async {
    try {
      final patternJson = jsonEncode(pattern.toJson());
      await _prefs.setString(_patternDataKey, patternJson);
    } catch (e) {
      debugPrint('Error saving pattern data: $e');
    }
  }

  Future<void> _checkSpendingAchievements(TravelSpending spending) async {
    // TODO: Check for spending-related achievements
  }

  Future<void> _checkPatternAchievements(TravelPattern pattern) async {
    // TODO: Check for pattern-related achievements
  }
}

// TODO: Backend Integration - Travel Analytics Service API
// POST /api/v1/analytics/spending/calculate
// Headers: Authorization: Bearer {token}, Content-Type: application/json
// Body: {
//   "userId": "string",
//   "startDate": "ISO8601",
//   "endDate": "ISO8601",
//   "includeCategories": ["flights", "insurance", "visa", "transfers"]
// }
// Response: {
//   "spending": TravelSpending.toJson(),
//   "calculationTime": "ISO8601",
//   "dataSourcesUsed": ["flights", "insurance", "visa", "transfers"]
// }

// TODO: Travel Pattern Analysis API
// POST /api/v1/analytics/patterns/analyze
// Headers: Authorization: Bearer {token}, Content-Type: application/json
// Body: {
//   "userId": "string",
//   "analysisType": "comprehensive",
//   "dateRange": { "start": "ISO8601", "end": "ISO8601" }
// }
// Response: {
//   "patterns": TravelPattern.toJson(),
//   "insights": PersonalInsight[],
//   "recommendations": TravelRecommendation[]
// }

// TODO: Analytics Data Sync API
// POST /api/v1/analytics/sync
// Headers: Authorization: Bearer {token}, Content-Type: application/json
// Body: {
//   "userId": "string",
//   "localData": {
//     "spending": TravelSpending.toJson(),
//     "patterns": TravelPattern.toJson(),
//     "preferences": AnalyticsPreferences.toJson()
//   },
//   "lastSyncTime": "ISO8601"
// }
// Response: {
//   "syncStatus": "success" | "conflict" | "error",
//   "serverData": AnalyticsData,
//   "conflictResolution": ConflictResolution[],
//   "nextSyncTime": "ISO8601"
// }
