// Flutter imports
import 'dart:convert';
import 'package:flutter/foundation.dart';

// Package imports
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/models/analytics/travel_analytics_model.dart';
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/analytics/travel_analytics_service.dart';

/// Model for travel recommendations
class TravelRecommendation {
  /// Recommendation title
  final String title;
  
  /// Recommendation description
  final String description;
  
  /// Recommendation type
  final String type;
  
  /// Potential savings amount
  final double? potentialSavings;
  
  /// Confidence score (0.0 to 1.0)
  final double confidence;
  
  /// Action URL or deep link
  final String? actionUrl;

  /// Creates a travel recommendation
  const TravelRecommendation({
    required this.title,
    required this.description,
    required this.type,
    this.potentialSavings,
    required this.confidence,
    this.actionUrl,
  });

  /// Creates from JSON
  factory TravelRecommendation.fromJson(Map<String, dynamic> json) {
    return TravelRecommendation(
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      potentialSavings: json['potentialSavings'] as double?,
      confidence: (json['confidence'] as num).toDouble(),
      actionUrl: json['actionUrl'] as String?,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'type': type,
      'potentialSavings': potentialSavings,
      'confidence': confidence,
      'actionUrl': actionUrl,
    };
  }
}

/// Model for personal insights
class PersonalInsight {
  /// Insight title
  final String title;
  
  /// Insight description
  final String description;
  
  /// Insight category
  final String category;
  
  /// Supporting data
  final Map<String, dynamic> data;
  
  /// Insight importance (1-5)
  final int importance;
  
  /// Generated timestamp
  final DateTime generatedAt;

  /// Creates a personal insight
  const PersonalInsight({
    required this.title,
    required this.description,
    required this.category,
    required this.data,
    required this.importance,
    required this.generatedAt,
  });

  /// Creates from JSON
  factory PersonalInsight.fromJson(Map<String, dynamic> json) {
    return PersonalInsight(
      title: json['title'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      data: Map<String, dynamic>.from(json['data'] as Map),
      importance: json['importance'] as int,
      generatedAt: DateTime.parse(json['generatedAt'] as String),
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'category': category,
      'data': data,
      'importance': importance,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }
}

/// Model for achievement analytics
class AchievementAnalytics {
  /// Total achievements unlocked
  final int totalUnlocked;
  
  /// Total achievements available
  final int totalAvailable;
  
  /// Completion percentage
  final double completionPercentage;
  
  /// Recent achievements
  final List<UserAchievement> recentAchievements;
  
  /// Next achievements to unlock
  final List<UserAchievement> nextAchievements;
  
  /// Achievement categories progress
  final Map<String, double> categoryProgress;
  
  /// Last updated timestamp
  final DateTime lastUpdated;

  /// Creates achievement analytics
  const AchievementAnalytics({
    required this.totalUnlocked,
    required this.totalAvailable,
    required this.completionPercentage,
    required this.recentAchievements,
    required this.nextAchievements,
    required this.categoryProgress,
    required this.lastUpdated,
  });

  /// Creates from JSON
  factory AchievementAnalytics.fromJson(Map<String, dynamic> json) {
    return AchievementAnalytics(
      totalUnlocked: json['totalUnlocked'] as int,
      totalAvailable: json['totalAvailable'] as int,
      completionPercentage: (json['completionPercentage'] as num).toDouble(),
      recentAchievements: (json['recentAchievements'] as List<dynamic>)
          .map((e) => UserAchievement.fromJson(e as Map<String, dynamic>))
          .toList(),
      nextAchievements: (json['nextAchievements'] as List<dynamic>)
          .map((e) => UserAchievement.fromJson(e as Map<String, dynamic>))
          .toList(),
      categoryProgress: Map<String, double>.from(
        (json['categoryProgress'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
        ),
      ),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'totalUnlocked': totalUnlocked,
      'totalAvailable': totalAvailable,
      'completionPercentage': completionPercentage,
      'recentAchievements': recentAchievements.map((e) => e.toJson()).toList(),
      'nextAchievements': nextAchievements.map((e) => e.toJson()).toList(),
      'categoryProgress': categoryProgress,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Get achievements close to completion
  List<UserAchievement> get achievementsCloseToCompletion {
    return nextAchievements
        .where((achievement) => achievement.progress > 0.7)
        .toList();
  }
}

/// Service for generating personalized travel insights and recommendations
class UserInsightsService {
  final SharedPreferences _prefs;
  final AnalyticsService _analyticsService;
  final TravelAnalyticsService _travelAnalyticsService;

  /// Storage keys
  static const String _insightsKey = 'user_insights_cache';
  static const String _recommendationsKey = 'travel_recommendations_cache';
  static const String _achievementAnalyticsKey = 'achievement_analytics_cache';

  /// Creates a new user insights service
  UserInsightsService({
    required SharedPreferences prefs,
    required AnalyticsService analyticsService,
    required TravelAnalyticsService travelAnalyticsService,
  })  : _prefs = prefs,
        _analyticsService = analyticsService,
        _travelAnalyticsService = travelAnalyticsService;

  /// Generate personalized travel insights
  Future<List<PersonalInsight>> generateInsights({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final insights = <PersonalInsight>[];
      
      // Get travel data
      final spending = await _travelAnalyticsService.calculateSpending(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );
      
      final patterns = await _travelAnalyticsService.analyzePatterns(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );

      // Generate spending insights
      insights.addAll(await _generateSpendingInsights(spending));
      
      // Generate pattern insights
      insights.addAll(await _generatePatternInsights(patterns));
      
      // Generate destination insights
      insights.addAll(await _generateDestinationInsights(userId, startDate, endDate));

      // Sort by importance
      insights.sort((a, b) => b.importance.compareTo(a.importance));

      // Cache insights
      await _cacheInsights(insights);

      // Log insights generation
      await _analyticsService.logEvent(
        name: 'insights_generated',
        category: AnalyticsCategory.userAction,
        parameters: {
          'insights_count': insights.length,
          'user_id': userId,
        },
      );

      return insights;
    } catch (e) {
      debugPrint('Error generating insights: $e');
      return await _getCachedInsights();
    }
  }

  /// Get optimization tips and recommendations
  Future<List<TravelRecommendation>> getOptimizationTips({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final recommendations = <TravelRecommendation>[];
      
      // Get travel data
      final spending = await _travelAnalyticsService.calculateSpending(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );
      
      final patterns = await _travelAnalyticsService.analyzePatterns(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );

      // Generate booking timing recommendations
      recommendations.addAll(await _generateTimingRecommendations(patterns));
      
      // Generate spending optimization recommendations
      recommendations.addAll(await _generateSpendingRecommendations(spending));
      
      // Generate destination recommendations
      recommendations.addAll(await _generateDestinationRecommendations(patterns));

      // Sort by confidence and potential savings
      recommendations.sort((a, b) {
        final aScore = a.confidence * (a.potentialSavings ?? 0);
        final bScore = b.confidence * (b.potentialSavings ?? 0);
        return bScore.compareTo(aScore);
      });

      // Cache recommendations
      await _cacheRecommendations(recommendations);

      return recommendations;
    } catch (e) {
      debugPrint('Error getting optimization tips: $e');
      return await _getCachedRecommendations();
    }
  }

  /// Get achievement progress analytics
  Future<AchievementAnalytics> getAchievementProgress({
    required String userId,
  }) async {
    try {
      // TODO: Integrate with achievement service to get actual achievement data
      final analytics = AchievementAnalytics(
        totalUnlocked: 8,
        totalAvailable: 35,
        completionPercentage: 22.9,
        recentAchievements: [],
        nextAchievements: [],
        categoryProgress: {
          'Travel': 0.4,
          'Booking': 0.6,
          'Explorer': 0.2,
          'Social': 0.1,
        },
        lastUpdated: DateTime.now(),
      );

      // Cache analytics
      await _cacheAchievementAnalytics(analytics);

      return analytics;
    } catch (e) {
      debugPrint('Error getting achievement progress: $e');
      return await _getCachedAchievementAnalytics();
    }
  }

  /// Private helper methods for generating insights
  Future<List<PersonalInsight>> _generateSpendingInsights(TravelSpending spending) async {
    final insights = <PersonalInsight>[];
    
    if (spending.totalSavings > 0) {
      insights.add(PersonalInsight(
        title: 'Smart Savings',
        description: 'You saved ${spending.formattedTotalSavings} this year by using CultureConnect!',
        category: 'savings',
        data: {
          'savings_amount': spending.totalSavings,
          'savings_percentage': spending.savingsPercentage,
        },
        importance: 5,
        generatedAt: DateTime.now(),
      ));
    }

    if (spending.topSpendingCategory != null) {
      final category = spending.topSpendingCategory!;
      final percentage = spending.getCategoryPercentage(category);
      
      insights.add(PersonalInsight(
        title: 'Top Spending Category',
        description: '${category.displayName} accounts for ${percentage.toStringAsFixed(1)}% of your travel spending.',
        category: 'spending_pattern',
        data: {
          'top_category': category.name,
          'percentage': percentage,
        },
        importance: 3,
        generatedAt: DateTime.now(),
      ));
    }

    return insights;
  }

  Future<List<PersonalInsight>> _generatePatternInsights(TravelPattern patterns) async {
    final insights = <PersonalInsight>[];
    
    if (patterns.topDestination != null) {
      insights.add(PersonalInsight(
        title: 'Favorite Destination',
        description: 'Your most visited destination is ${patterns.topDestination}.',
        category: 'travel_pattern',
        data: {
          'destination': patterns.topDestination,
          'visit_count': patterns.topDestinations[patterns.topDestination!],
        },
        importance: 4,
        generatedAt: DateTime.now(),
      ));
    }

    insights.add(PersonalInsight(
      title: 'Travel Style',
      description: 'You are a ${patterns.travelStyle} - you book ${patterns.averageAdvanceBookingDays.toStringAsFixed(0)} days in advance on average.',
      category: 'booking_pattern',
      data: {
        'travel_style': patterns.travelStyle,
        'advance_days': patterns.averageAdvanceBookingDays,
      },
      importance: 3,
      generatedAt: DateTime.now(),
    ));

    return insights;
  }

  Future<List<PersonalInsight>> _generateDestinationInsights(String userId, DateTime? startDate, DateTime? endDate) async {
    // TODO: Generate destination-specific insights
    return [];
  }

  Future<List<TravelRecommendation>> _generateTimingRecommendations(TravelPattern patterns) async {
    final recommendations = <TravelRecommendation>[];
    
    if (patterns.averageAdvanceBookingDays < 14) {
      recommendations.add(const TravelRecommendation(
        title: 'Book Earlier for Better Deals',
        description: 'Booking flights 2+ weeks in advance can save you up to 25% on average.',
        type: 'timing',
        potentialSavings: 150.0,
        confidence: 0.8,
      ));
    }

    return recommendations;
  }

  Future<List<TravelRecommendation>> _generateSpendingRecommendations(TravelSpending spending) async {
    final recommendations = <TravelRecommendation>[];
    
    if (spending.getCategoryPercentage(TravelServiceCategory.insurance) < 10) {
      recommendations.add(const TravelRecommendation(
        title: 'Consider Travel Insurance',
        description: 'Protect your trips with comprehensive travel insurance starting from \$25.',
        type: 'insurance',
        potentialSavings: null,
        confidence: 0.6,
      ));
    }

    return recommendations;
  }

  Future<List<TravelRecommendation>> _generateDestinationRecommendations(TravelPattern patterns) async {
    // TODO: Generate destination-based recommendations
    return [];
  }

  /// Cache management methods
  Future<void> _cacheInsights(List<PersonalInsight> insights) async {
    try {
      final insightsJson = jsonEncode(insights.map((i) => i.toJson()).toList());
      await _prefs.setString(_insightsKey, insightsJson);
    } catch (e) {
      debugPrint('Error caching insights: $e');
    }
  }

  Future<List<PersonalInsight>> _getCachedInsights() async {
    try {
      final insightsJson = _prefs.getString(_insightsKey);
      if (insightsJson != null) {
        final insightsList = jsonDecode(insightsJson) as List<dynamic>;
        return insightsList
            .map((i) => PersonalInsight.fromJson(i as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      debugPrint('Error getting cached insights: $e');
    }
    return [];
  }

  Future<void> _cacheRecommendations(List<TravelRecommendation> recommendations) async {
    try {
      final recommendationsJson = jsonEncode(recommendations.map((r) => r.toJson()).toList());
      await _prefs.setString(_recommendationsKey, recommendationsJson);
    } catch (e) {
      debugPrint('Error caching recommendations: $e');
    }
  }

  Future<List<TravelRecommendation>> _getCachedRecommendations() async {
    try {
      final recommendationsJson = _prefs.getString(_recommendationsKey);
      if (recommendationsJson != null) {
        final recommendationsList = jsonDecode(recommendationsJson) as List<dynamic>;
        return recommendationsList
            .map((r) => TravelRecommendation.fromJson(r as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      debugPrint('Error getting cached recommendations: $e');
    }
    return [];
  }

  Future<void> _cacheAchievementAnalytics(AchievementAnalytics analytics) async {
    try {
      final analyticsJson = jsonEncode(analytics.toJson());
      await _prefs.setString(_achievementAnalyticsKey, analyticsJson);
    } catch (e) {
      debugPrint('Error caching achievement analytics: $e');
    }
  }

  Future<AchievementAnalytics> _getCachedAchievementAnalytics() async {
    try {
      final analyticsJson = _prefs.getString(_achievementAnalyticsKey);
      if (analyticsJson != null) {
        final analyticsMap = jsonDecode(analyticsJson) as Map<String, dynamic>;
        return AchievementAnalytics.fromJson(analyticsMap);
      }
    } catch (e) {
      debugPrint('Error getting cached achievement analytics: $e');
    }
    
    // Return empty analytics if no cache
    return AchievementAnalytics(
      totalUnlocked: 0,
      totalAvailable: 35,
      completionPercentage: 0.0,
      recentAchievements: [],
      nextAchievements: [],
      categoryProgress: {},
      lastUpdated: DateTime.now(),
    );
  }
}

// TODO: Backend Integration - User Insights Service API
// POST /api/v1/insights/generate
// Headers: Authorization: Bearer {token}, Content-Type: application/json
// Body: {
//   "userId": "string",
//   "analysisType": "comprehensive",
//   "dateRange": { "start": "ISO8601", "end": "ISO8601" },
//   "includeCategories": ["spending", "patterns", "destinations", "achievements"]
// }
// Response: {
//   "insights": PersonalInsight[],
//   "recommendations": TravelRecommendation[],
//   "achievementAnalytics": AchievementAnalytics,
//   "generatedAt": "ISO8601"
// }

// TODO: Personalization API
// GET /api/v1/insights/recommendations?userId={id}&type=optimization
// Headers: Authorization: Bearer {token}
// Response: {
//   "recommendations": TravelRecommendation[],
//   "personalizedTips": PersonalizedTip[],
//   "savingsOpportunities": SavingsOpportunity[]
// }

// TODO: Achievement Analytics API
// GET /api/v1/achievements/analytics?userId={id}&includeProjections=true
// Headers: Authorization: Bearer {token}
// Response: {
//   "analytics": AchievementAnalytics,
//   "projections": AchievementProjection[],
//   "milestones": Milestone[]
// }
