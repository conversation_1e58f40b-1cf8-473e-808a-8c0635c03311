import 'dart:async';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import 'package:culture_connect/models/analytics/user_journey.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/providers/achievement_provider.dart'
    hide loggingServiceProvider, analyticsServiceProvider;
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/mascot_service.dart';
import 'package:culture_connect/services/performance/performance_metrics_service.dart';

/// Provider for the UserJourneyService
final userJourneyServiceProvider = Provider<UserJourneyService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final analyticsService = ref.watch(analyticsServiceProvider);
  final achievementService = ref.watch(achievementServiceProvider);
  final mascotService = ref.watch(mascotServiceProvider);
  final performanceService = ref.watch(performanceMetricsServiceProvider);

  return UserJourneyService(
    prefs: prefs,
    loggingService: loggingService,
    analyticsService: analyticsService,
    achievementService: achievementService,
    mascotService: mascotService,
    performanceService: performanceService,
  );
});

/// Journey tracking preferences
class JourneyTrackingPreferences {
  /// Whether journey tracking is enabled
  final bool journeyTrackingEnabled;

  /// Whether to track screen views
  final bool trackScreenViews;

  /// Whether to track button interactions
  final bool trackButtonInteractions;

  /// Whether to track form submissions
  final bool trackFormSubmissions;

  /// Whether to track search actions
  final bool trackSearchActions;

  /// Whether to track errors
  final bool trackErrors;

  /// Data retention period in days
  final int dataRetentionDays;

  /// Whether user has given consent for journey tracking
  final bool hasUserConsent;

  /// Maximum journeys to store locally
  final int maxLocalJourneys;

  const JourneyTrackingPreferences({
    this.journeyTrackingEnabled = true,
    this.trackScreenViews = true,
    this.trackButtonInteractions = true,
    this.trackFormSubmissions = true,
    this.trackSearchActions = true,
    this.trackErrors = true,
    this.dataRetentionDays = 30,
    this.hasUserConsent = false,
    this.maxLocalJourneys = 500,
  });

  JourneyTrackingPreferences copyWith({
    bool? journeyTrackingEnabled,
    bool? trackScreenViews,
    bool? trackButtonInteractions,
    bool? trackFormSubmissions,
    bool? trackSearchActions,
    bool? trackErrors,
    int? dataRetentionDays,
    bool? hasUserConsent,
    int? maxLocalJourneys,
  }) {
    return JourneyTrackingPreferences(
      journeyTrackingEnabled:
          journeyTrackingEnabled ?? this.journeyTrackingEnabled,
      trackScreenViews: trackScreenViews ?? this.trackScreenViews,
      trackButtonInteractions:
          trackButtonInteractions ?? this.trackButtonInteractions,
      trackFormSubmissions: trackFormSubmissions ?? this.trackFormSubmissions,
      trackSearchActions: trackSearchActions ?? this.trackSearchActions,
      trackErrors: trackErrors ?? this.trackErrors,
      dataRetentionDays: dataRetentionDays ?? this.dataRetentionDays,
      hasUserConsent: hasUserConsent ?? this.hasUserConsent,
      maxLocalJourneys: maxLocalJourneys ?? this.maxLocalJourneys,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'journeyTrackingEnabled': journeyTrackingEnabled,
      'trackScreenViews': trackScreenViews,
      'trackButtonInteractions': trackButtonInteractions,
      'trackFormSubmissions': trackFormSubmissions,
      'trackSearchActions': trackSearchActions,
      'trackErrors': trackErrors,
      'dataRetentionDays': dataRetentionDays,
      'hasUserConsent': hasUserConsent,
      'maxLocalJourneys': maxLocalJourneys,
    };
  }

  factory JourneyTrackingPreferences.fromJson(Map<String, dynamic> json) {
    return JourneyTrackingPreferences(
      journeyTrackingEnabled: json['journeyTrackingEnabled'] as bool? ?? true,
      trackScreenViews: json['trackScreenViews'] as bool? ?? true,
      trackButtonInteractions: json['trackButtonInteractions'] as bool? ?? true,
      trackFormSubmissions: json['trackFormSubmissions'] as bool? ?? true,
      trackSearchActions: json['trackSearchActions'] as bool? ?? true,
      trackErrors: json['trackErrors'] as bool? ?? true,
      dataRetentionDays: json['dataRetentionDays'] as int? ?? 30,
      hasUserConsent: json['hasUserConsent'] as bool? ?? false,
      maxLocalJourneys: json['maxLocalJourneys'] as int? ?? 500,
    );
  }
}

/// Service for tracking and managing user journeys
class UserJourneyService {
  final SharedPreferences _prefs;
  final LoggingService _loggingService;
  final AnalyticsService _analyticsService;
  final AchievementService _achievementService;
  final MascotService _mascotService;

  /// UUID generator for journey IDs
  static const _uuid = Uuid();

  /// Current session ID
  late final String _sessionId;

  /// Current user preferences
  JourneyTrackingPreferences _preferences = const JourneyTrackingPreferences();

  /// Active journeys
  final Map<String, UserJourney> _activeJourneys = {};

  /// Completed journeys cache
  final List<UserJourney> _completedJourneys = [];

  /// Current journey (if any)
  UserJourney? _currentJourney;

  /// Whether service is initialized
  bool _isInitialized = false;

  /// Storage keys
  static const String _preferencesKey = 'journey_tracking_preferences';
  static const String _journeysKey = 'user_journeys_cache';

  UserJourneyService({
    required SharedPreferences prefs,
    required LoggingService loggingService,
    required AnalyticsService analyticsService,
    required AchievementService achievementService,
    required MascotService mascotService,
    required PerformanceMetricsService performanceService,
  })  : _prefs = prefs,
        _loggingService = loggingService,
        _analyticsService = analyticsService,
        _achievementService = achievementService,
        _mascotService = mascotService {
    _sessionId = _uuid.v4();
  }

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadPreferences();
      await _loadCachedJourneys();

      _isInitialized = true;

      _loggingService.info(
        'UserJourneyService',
        'Service initialized',
        {
          'sessionId': _sessionId,
          'trackingEnabled': _preferences.journeyTrackingEnabled,
          'hasConsent': _preferences.hasUserConsent,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'UserJourneyService',
        'Failed to initialize service',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Request user consent for journey tracking
  Future<bool> requestUserConsent() async {
    try {
      // TODO: Integrate with Journey Tracking Consent Dialog UI
      // POST /api/v1/analytics/journey/consent
      // Headers: Authorization, Content-Type
      // Body: { userId: string, consentGiven: boolean, timestamp: string }
      // Response: { success: boolean, consentId: string }

      // For now, we'll assume consent is given for development
      await updatePreferences(_preferences.copyWith(hasUserConsent: true));

      return true;
    } catch (e) {
      _loggingService.error(
        'UserJourneyService',
        'Failed to request user consent',
        {'error': e.toString()},
      );
      return false;
    }
  }

  /// Update user preferences
  Future<void> updatePreferences(JourneyTrackingPreferences preferences) async {
    try {
      _preferences = preferences;
      await _prefs.setString(_preferencesKey, jsonEncode(preferences.toJson()));

      _loggingService.info(
        'UserJourneyService',
        'Preferences updated',
        {'preferences': preferences.toJson()},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'UserJourneyService',
        'Failed to update preferences',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Get current preferences
  JourneyTrackingPreferences get preferences => _preferences;

  /// Check if journey tracking is enabled
  bool get isTrackingEnabled =>
      _preferences.journeyTrackingEnabled && _preferences.hasUserConsent;

  /// Start a new journey
  Future<UserJourney?> startJourney({
    required String category,
    Map<String, bool>? goals,
    Map<String, dynamic>? metadata,
  }) async {
    if (!isTrackingEnabled) return null;

    try {
      final journey = JourneyFactory.createJourney(
        sessionId: _sessionId,
        goals: goals,
        metadata: metadata,
        category: category,
      );

      _activeJourneys[journey.id] = journey;
      _currentJourney = journey;

      await _saveJourneys();

      // Log journey start
      await _analyticsService.logEvent(
        name: 'journey_started',
        category: AnalyticsCategory.userAction,
        parameters: {
          'journey_id': journey.id,
          'category': category,
          'goals_count': goals?.length ?? 0,
        },
      );

      // Show excited mascot expression for journey start
      await _mascotService.onDiscovery(
        discoveryType: 'journey_start',
        message: 'Starting your $category journey!',
      );

      _loggingService.info(
        'UserJourneyService',
        'Journey started',
        {
          'journeyId': journey.id,
          'category': category,
          'sessionId': _sessionId,
        },
      );

      return journey;
    } catch (e, stackTrace) {
      _loggingService.error(
        'UserJourneyService',
        'Failed to start journey',
        {'error': e.toString()},
        stackTrace,
      );
      return null;
    }
  }

  /// Track a journey step
  Future<void> trackStep({
    required JourneyStepType type,
    required String screen,
    required String action,
    Map<String, dynamic>? context,
    Duration? duration,
    bool isSuccessful = true,
    String? errorMessage,
    String? journeyId,
  }) async {
    if (!isTrackingEnabled) return;

    // Check if we should track this type of step
    if (!_shouldTrackStepType(type)) return;

    try {
      final step = JourneyFactory.createStep(
        type: type,
        screen: screen,
        action: action,
        context: context,
        duration: duration,
        isSuccessful: isSuccessful,
        errorMessage: errorMessage,
      );

      // Add to current journey or specified journey
      final targetJourneyId = journeyId ?? _currentJourney?.id;
      if (targetJourneyId != null &&
          _activeJourneys.containsKey(targetJourneyId)) {
        final journey = _activeJourneys[targetJourneyId]!;
        final updatedJourney = journey.addStep(step);
        _activeJourneys[targetJourneyId] = updatedJourney;

        if (_currentJourney?.id == targetJourneyId) {
          _currentJourney = updatedJourney;
        }

        await _saveJourneys();

        // Log step to analytics
        await _analyticsService.logEvent(
          name: 'journey_step',
          category: AnalyticsCategory.userAction,
          parameters: {
            'journey_id': targetJourneyId,
            'step_type': type.value,
            'screen': screen,
            'action': action,
            'successful': isSuccessful,
          },
        );

        // Handle error steps with mascot empathy
        if (!isSuccessful && errorMessage != null) {
          await _mascotService.onError(
            errorType: 'journey_error',
            errorMessage: errorMessage,
          );
        }

        _loggingService.debug(
          'UserJourneyService',
          'Journey step tracked',
          {
            'journeyId': targetJourneyId,
            'stepType': type.displayName,
            'screen': screen,
            'action': action,
          },
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'UserJourneyService',
        'Failed to track journey step',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Complete a journey
  Future<void> completeJourney({
    required String journeyId,
    required JourneyOutcome outcome,
    Map<String, bool>? goalUpdates,
  }) async {
    if (!isTrackingEnabled) return;

    try {
      final journey = _activeJourneys[journeyId];
      if (journey == null) return;

      // Update goals if provided
      var updatedJourney = journey;
      if (goalUpdates != null) {
        for (final entry in goalUpdates.entries) {
          updatedJourney = updatedJourney.updateGoal(entry.key, entry.value);
        }
      }

      // Complete the journey
      final completedJourney = updatedJourney.complete(outcome);

      // Move from active to completed
      _activeJourneys.remove(journeyId);
      _completedJourneys.add(completedJourney);

      if (_currentJourney?.id == journeyId) {
        _currentJourney = null;
      }

      // Enforce cache size limit
      if (_completedJourneys.length > _preferences.maxLocalJourneys) {
        _completedJourneys.removeAt(0);
      }

      await _saveJourneys();

      // Log journey completion
      await _analyticsService.logEvent(
        name: 'journey_completed',
        category: AnalyticsCategory.userAction,
        parameters: {
          'journey_id': journeyId,
          'outcome': outcome.value,
          'duration_seconds': completedJourney.duration?.inSeconds ?? 0,
          'steps_count': completedJourney.stepCount,
          'success_rate': completedJourney.successRate,
          'goal_completion_rate': completedJourney.goalCompletionRate,
        },
      );

      // Track achievements for journey completion
      await _trackJourneyAchievements(completedJourney);

      // Show appropriate mascot expression
      if (outcome == JourneyOutcome.completed) {
        await _mascotService.onBookingSuccessful(
          bookingType: 'Journey',
          bookingId: completedJourney.id,
        );
      }

      // TODO: Integrate with Journey Completion API
      // POST /api/v1/analytics/journey/complete
      // Headers: Authorization, Content-Type, X-Session-ID
      // Body: {
      //   journey: UserJourney,
      //   userId: string,
      //   sessionId: string,
      //   performanceMetrics: {
      //     duration: number,
      //     stepCount: number,
      //     successRate: number,
      //     goalCompletionRate: number
      //   },
      //   timestamp: string
      // }
      // Response: {
      //   success: boolean,
      //   insights: [JourneyInsight],
      //   recommendations: [string],
      //   patterns: [JourneyPattern]
      // }

      _loggingService.info(
        'UserJourneyService',
        'Journey completed',
        {
          'journeyId': journeyId,
          'outcome': outcome.displayName,
          'duration': completedJourney.duration?.inSeconds,
          'stepCount': completedJourney.stepCount,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'UserJourneyService',
        'Failed to complete journey',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Get all completed journeys
  List<UserJourney> getCompletedJourneys() {
    return List.unmodifiable(_completedJourneys);
  }

  /// Get active journeys
  List<UserJourney> getActiveJourneys() {
    return _activeJourneys.values.toList();
  }

  /// Get current journey
  UserJourney? getCurrentJourney() {
    return _currentJourney;
  }

  /// Get journeys by category
  List<UserJourney> getJourneysByCategory(String category) {
    return _completedJourneys
        .where((journey) => journey.category == category)
        .toList();
  }

  /// Analyze journey patterns
  Future<List<JourneyPattern>> analyzePatterns({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final journeys = _getJourneysInPeriod(startDate, endDate);
      final patterns = <JourneyPattern>[];

      // Group journeys by category
      final journeysByCategory = <String, List<UserJourney>>{};
      for (final journey in journeys) {
        journeysByCategory.putIfAbsent(journey.category, () => []).add(journey);
      }

      // Analyze patterns for each category
      for (final entry in journeysByCategory.entries) {
        final categoryJourneys = entry.value;
        if (categoryJourneys.length < 3) continue; // Need minimum data

        final pattern = _analyzeJourneyPattern(entry.key, categoryJourneys);
        if (pattern != null) {
          patterns.add(pattern);
        }
      }

      return patterns;
    } catch (e, stackTrace) {
      _loggingService.error(
        'UserJourneyService',
        'Failed to analyze journey patterns',
        {'error': e.toString()},
        stackTrace,
      );
      return [];
    }
  }

  /// Export user journey data for privacy compliance
  Future<Map<String, dynamic>> exportJourneyData() async {
    try {
      final data = {
        'sessionId': _sessionId,
        'preferences': _preferences.toJson(),
        'activeJourneys':
            _activeJourneys.values.map((j) => j.toJson()).toList(),
        'completedJourneys': _completedJourneys.map((j) => j.toJson()).toList(),
        'exportTimestamp': DateTime.now().toIso8601String(),
      };

      _loggingService.info(
        'UserJourneyService',
        'Journey data exported',
        {'journeysCount': _completedJourneys.length + _activeJourneys.length},
      );

      return data;
    } catch (e, stackTrace) {
      _loggingService.error(
        'UserJourneyService',
        'Failed to export journey data',
        {'error': e.toString()},
        stackTrace,
      );
      return {};
    }
  }

  /// Clear all journey data
  Future<void> clearAllJourneys() async {
    try {
      _activeJourneys.clear();
      _completedJourneys.clear();
      _currentJourney = null;
      await _prefs.remove(_journeysKey);

      _loggingService.info('UserJourneyService', 'All journey data cleared');
    } catch (e, stackTrace) {
      _loggingService.error(
        'UserJourneyService',
        'Failed to clear journey data',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Dispose of the service
  Future<void> dispose() async {
    _loggingService.info('UserJourneyService', 'Service disposed');
  }

  /// Check if we should track this step type based on preferences
  bool _shouldTrackStepType(JourneyStepType type) {
    switch (type) {
      case JourneyStepType.screenView:
        return _preferences.trackScreenViews;
      case JourneyStepType.buttonTap:
        return _preferences.trackButtonInteractions;
      case JourneyStepType.formSubmission:
        return _preferences.trackFormSubmissions;
      case JourneyStepType.search:
        return _preferences.trackSearchActions;
      case JourneyStepType.error:
        return _preferences.trackErrors;
      default:
        return true; // Track other types by default
    }
  }

  /// Load preferences from storage
  Future<void> _loadPreferences() async {
    try {
      final prefsJson = _prefs.getString(_preferencesKey);
      if (prefsJson != null) {
        final prefsMap = jsonDecode(prefsJson) as Map<String, dynamic>;
        _preferences = JourneyTrackingPreferences.fromJson(prefsMap);
      }
    } catch (e) {
      _loggingService.warning(
        'UserJourneyService',
        'Failed to load preferences, using defaults',
        {'error': e.toString()},
      );
    }
  }

  /// Load cached journeys from storage
  Future<void> _loadCachedJourneys() async {
    try {
      final journeysJson = _prefs.getString(_journeysKey);
      if (journeysJson != null) {
        final journeysData = jsonDecode(journeysJson) as Map<String, dynamic>;

        // Load completed journeys
        final completedList = journeysData['completed'] as List<dynamic>? ?? [];
        _completedJourneys.clear();

        for (final journeyMap in completedList) {
          try {
            final journey =
                UserJourney.fromJson(journeyMap as Map<String, dynamic>);

            // Check if journey is within retention period
            final retentionDate = DateTime.now()
                .subtract(Duration(days: _preferences.dataRetentionDays));

            if (journey.startTime.isAfter(retentionDate)) {
              _completedJourneys.add(journey);
            }
          } catch (e) {
            _loggingService.warning(
              'UserJourneyService',
              'Failed to parse cached journey',
              {'error': e.toString()},
            );
          }
        }

        // Load active journeys
        final activeList = journeysData['active'] as List<dynamic>? ?? [];
        _activeJourneys.clear();

        for (final journeyMap in activeList) {
          try {
            final journey =
                UserJourney.fromJson(journeyMap as Map<String, dynamic>);
            _activeJourneys[journey.id] = journey;
          } catch (e) {
            _loggingService.warning(
              'UserJourneyService',
              'Failed to parse active journey',
              {'error': e.toString()},
            );
          }
        }
      }
    } catch (e) {
      _loggingService.warning(
        'UserJourneyService',
        'Failed to load cached journeys',
        {'error': e.toString()},
      );
    }
  }

  /// Save journeys to storage
  Future<void> _saveJourneys() async {
    try {
      final journeysData = {
        'completed': _completedJourneys.map((j) => j.toJson()).toList(),
        'active': _activeJourneys.values.map((j) => j.toJson()).toList(),
      };
      await _prefs.setString(_journeysKey, jsonEncode(journeysData));
    } catch (e) {
      _loggingService.warning(
        'UserJourneyService',
        'Failed to save journeys',
        {'error': e.toString()},
      );
    }
  }

  /// Track journey-related achievements
  Future<void> _trackJourneyAchievements(UserJourney journey) async {
    try {
      // Track journey completion achievement
      await _achievementService.trackUserAction(
        UserAction.appFeatureUsed,
        metadata: {
          'type': 'journey_completed',
          'category': journey.category,
          'duration': journey.duration?.inSeconds ?? 0,
          'stepCount': journey.stepCount,
          'successRate': journey.successRate,
        },
      );

      // Track goal completion achievements
      if (journey.goalCompletionRate >= 1.0) {
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {
            'type': 'all_goals_completed',
            'category': journey.category,
          },
        );
      }

      // Track efficiency achievements
      if (journey.successRate >= 0.9 && journey.stepCount <= 10) {
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {
            'type': 'efficient_journey',
            'category': journey.category,
          },
        );
      }
    } catch (e) {
      _loggingService.warning(
        'UserJourneyService',
        'Failed to track journey achievements',
        {'error': e.toString()},
      );
    }
  }

  /// Get journeys within a specific time period
  List<UserJourney> _getJourneysInPeriod(
      DateTime? startDate, DateTime? endDate) {
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    return _completedJourneys
        .where((journey) =>
            journey.startTime.isAfter(start) && journey.startTime.isBefore(end))
        .toList();
  }

  /// Analyze journey pattern for a category
  JourneyPattern? _analyzeJourneyPattern(
      String category, List<UserJourney> journeys) {
    if (journeys.isEmpty) return null;

    try {
      final totalDuration = journeys
          .where((j) => j.duration != null)
          .map((j) => j.duration!)
          .fold(Duration.zero, (a, b) => a + b);

      final averageDuration = journeys.isNotEmpty
          ? Duration(
              milliseconds: totalDuration.inMilliseconds ~/ journeys.length)
          : Duration.zero;

      final successfulJourneys =
          journeys.where((j) => j.outcome == JourneyOutcome.completed).length;
      final successRate =
          journeys.isNotEmpty ? successfulJourneys / journeys.length : 0.0;

      // Analyze common steps
      final stepCounts = <String, int>{};
      for (final journey in journeys) {
        for (final step in journey.steps) {
          final stepKey = '${step.screen}:${step.action}';
          stepCounts[stepKey] = (stepCounts[stepKey] ?? 0) + 1;
        }
      }

      final commonSteps = stepCounts.entries
          .where(
              (entry) => entry.value >= journeys.length * 0.5) // 50% threshold
          .map((entry) => entry.key)
          .toList();

      return JourneyPattern(
        id: _uuid.v4(),
        name: '$category Pattern',
        category: category,
        frequency: journeys.length,
        averageDuration: averageDuration,
        successRate: successRate,
        commonSteps: commonSteps,
        dropOffPoints: const [], // TODO: Implement drop-off analysis
        insights: _generatePatternInsights(category, journeys, successRate),
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      _loggingService.warning(
        'UserJourneyService',
        'Failed to analyze pattern for category: $category',
        {'error': e.toString()},
      );
      return null;
    }
  }

  /// Generate insights for journey patterns
  List<String> _generatePatternInsights(
      String category, List<UserJourney> journeys, double successRate) {
    final insights = <String>[];

    if (successRate >= 0.8) {
      insights.add(
          'High success rate for $category journeys (${(successRate * 100).toStringAsFixed(1)}%)');
    } else if (successRate < 0.5) {
      insights.add(
          'Low success rate for $category journeys - consider UX improvements');
    }

    final avgSteps = journeys.map((j) => j.stepCount).reduce((a, b) => a + b) /
        journeys.length;
    if (avgSteps > 15) {
      insights
          .add('$category journeys have many steps - consider simplification');
    }

    return insights;
  }
}
