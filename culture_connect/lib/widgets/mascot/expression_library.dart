// Flutter imports
import 'package:flutter/material.dart';

// Third-party imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/mascot/mascot_state.dart';
import 'package:culture_connect/widgets/common/mascot_widget.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Widget for displaying and managing mascot expressions
class ExpressionLibrary extends ConsumerWidget {
  /// Whether to show expression labels
  final bool showLabels;
  
  /// The size of each expression preview
  final double expressionSize;
  
  /// Callback when an expression is selected
  final Function(MascotExpression)? onExpressionSelected;
  
  /// Whether expressions are selectable
  final bool isSelectable;

  /// Creates a new expression library widget
  const ExpressionLibrary({
    super.key,
    this.showLabels = true,
    this.expressionSize = 80.0,
    this.onExpressionSelected,
    this.isSelectable = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Mascot Expressions',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 16,
          runSpacing: 16,
          children: MascotExpression.values.map((expression) {
            return _ExpressionPreview(
              expression: expression,
              size: expressionSize,
              showLabel: showLabels,
              isSelectable: isSelectable,
              onTap: () => onExpressionSelected?.call(expression),
            );
          }).toList(),
        ),
      ],
    );
  }
}

/// Widget for previewing a single mascot expression
class _ExpressionPreview extends StatefulWidget {
  /// The expression to preview
  final MascotExpression expression;
  
  /// The size of the preview
  final double size;
  
  /// Whether to show the expression label
  final bool showLabel;
  
  /// Whether this preview is selectable
  final bool isSelectable;
  
  /// Callback when tapped
  final VoidCallback? onTap;

  /// Creates a new expression preview
  const _ExpressionPreview({
    required this.expression,
    required this.size,
    this.showLabel = true,
    this.isSelectable = false,
    this.onTap,
  });

  @override
  State<_ExpressionPreview> createState() => _ExpressionPreviewState();
}

class _ExpressionPreviewState extends State<_ExpressionPreview>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _hoverAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: AppTheme.shortAnimation,
      vsync: this,
    );
    _hoverAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  void _handleHover(bool isHovered) {
    if (!widget.isSelectable) return;
    
    setState(() {
      _isHovered = isHovered;
    });

    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  MascotState _getPreviewState() {
    switch (widget.expression) {
      case MascotExpression.happy:
        return MascotState.bookingSuccess(bookingType: 'Preview');
      case MascotExpression.excited:
        return MascotState.discovery(discoveryType: 'Preview');
      case MascotExpression.helpful:
        return MascotState.onboarding(step: 'Preview');
      case MascotExpression.sympathetic:
        return MascotState.error(errorMessage: 'Preview');
      case MascotExpression.celebrating:
        return MascotState.achievementCelebration(achievementTitle: 'Preview');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final previewState = _getPreviewState();

    Widget content = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ScaleTransition(
          scale: _hoverAnimation,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _isHovered
                    ? widget.expression.primaryColor
                    : theme.colorScheme.outline.withAlpha(77),
                width: _isHovered ? 2 : 1,
              ),
              color: widget.expression.primaryColor.withAlpha(13),
            ),
            child: Center(
              child: MascotWidget(
                size: widget.size * 0.7,
                customState: previewState,
                showLabel: false,
              ),
            ),
          ),
        ),
        if (widget.showLabel) ...[
          const SizedBox(height: 8),
          Text(
            widget.expression.displayName,
            style: theme.textTheme.bodySmall?.copyWith(
              color: widget.expression.primaryColor,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );

    if (widget.isSelectable) {
      content = MouseRegion(
        onEnter: (_) => _handleHover(true),
        onExit: (_) => _handleHover(false),
        child: GestureDetector(
          onTap: widget.onTap,
          child: content,
        ),
      );
    }

    return content;
  }
}

/// Widget for demonstrating expression transitions
class ExpressionTransitionDemo extends StatefulWidget {
  /// The size of the demo mascot
  final double size;

  /// Creates a new expression transition demo
  const ExpressionTransitionDemo({
    super.key,
    this.size = 120.0,
  });

  @override
  State<ExpressionTransitionDemo> createState() => _ExpressionTransitionDemoState();
}

class _ExpressionTransitionDemoState extends State<ExpressionTransitionDemo> {
  int _currentExpressionIndex = 0;
  final List<MascotExpression> _expressions = MascotExpression.values;

  void _nextExpression() {
    setState(() {
      _currentExpressionIndex = 
          (_currentExpressionIndex + 1) % _expressions.length;
    });
  }

  MascotState _getCurrentState() {
    final expression = _expressions[_currentExpressionIndex];
    switch (expression) {
      case MascotExpression.happy:
        return MascotState.bookingSuccess(bookingType: 'Demo');
      case MascotExpression.excited:
        return MascotState.discovery(discoveryType: 'Demo');
      case MascotExpression.helpful:
        return MascotState.onboarding(step: 'Demo');
      case MascotExpression.sympathetic:
        return MascotState.error(errorMessage: 'Demo');
      case MascotExpression.celebrating:
        return MascotState.achievementCelebration(achievementTitle: 'Demo');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentState = _getCurrentState();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Expression Transition Demo',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: theme.colorScheme.outline.withAlpha(77),
            ),
          ),
          child: Column(
            children: [
              MascotWidget(
                size: widget.size,
                customState: currentState,
                showLabel: true,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _nextExpression,
                child: const Text('Next Expression'),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Widget for testing mascot expressions in different contexts
class ExpressionTester extends StatefulWidget {
  /// Creates a new expression tester
  const ExpressionTester({super.key});

  @override
  State<ExpressionTester> createState() => _ExpressionTesterState();
}

class _ExpressionTesterState extends State<ExpressionTester> {
  MascotExpression _selectedExpression = MascotExpression.helpful;
  MascotContext _selectedContext = MascotContext.idle;

  MascotState _getTestState() {
    return MascotState(
      expression: _selectedExpression,
      context: _selectedContext,
      isAnimating: _selectedExpression == MascotExpression.excited ||
                   _selectedExpression == MascotExpression.helpful,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final testState = _getTestState();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Expression Tester',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Controls
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Expression',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  DropdownButton<MascotExpression>(
                    value: _selectedExpression,
                    isExpanded: true,
                    items: MascotExpression.values.map((expression) {
                      return DropdownMenuItem(
                        value: expression,
                        child: Text(expression.displayName),
                      );
                    }).toList(),
                    onChanged: (expression) {
                      if (expression != null) {
                        setState(() {
                          _selectedExpression = expression;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Context',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  DropdownButton<MascotContext>(
                    value: _selectedContext,
                    isExpanded: true,
                    items: MascotContext.values.map((context) {
                      return DropdownMenuItem(
                        value: context,
                        child: Text(context.toString().split('.').last),
                      );
                    }).toList(),
                    onChanged: (context) {
                      if (context != null) {
                        setState(() {
                          _selectedContext = context;
                        });
                      }
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(width: 24),
            // Preview
            Expanded(
              flex: 1,
              child: Center(
                child: MascotWidget(
                  size: 120,
                  customState: testState,
                  showLabel: true,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
