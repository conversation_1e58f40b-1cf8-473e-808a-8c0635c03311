// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Third-party imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/providers/mascot_provider.dart';
import 'package:culture_connect/providers/achievement_provider.dart';
import 'package:culture_connect/services/achievement_service.dart';

/// Enum for different celebration gesture types
enum CelebrationGestureType {
  /// Pull-to-refresh success celebration
  pullToRefresh,

  /// Swipe completion animation
  swipeCompletion,

  /// Long-press feedback effect
  longPress,

  /// Multi-touch interaction reward
  multiTouch,

  /// Double-tap celebration
  doubleTap,

  /// Pinch gesture celebration
  pinch,
}

/// Widget that detects celebration gestures and provides delightful feedback
class CelebrationGestureDetector extends ConsumerStatefulWidget {
  /// The child widget to wrap with gesture detection
  final Widget child;

  /// Whether to enable celebration gestures
  final bool enabled;

  /// Whether to enable haptic feedback
  final bool enableHapticFeedback;

  /// Whether to enable mascot integration
  final bool enableMascotIntegration;

  /// Whether to enable achievement integration
  final bool enableAchievementIntegration;

  /// Callback when a celebration gesture is detected
  final void Function(CelebrationGestureType type)? onCelebrationGesture;

  /// Custom celebration message
  final String? celebrationMessage;

  /// Creates a new celebration gesture detector
  const CelebrationGestureDetector({
    super.key,
    required this.child,
    this.enabled = true,
    this.enableHapticFeedback = true,
    this.enableMascotIntegration = true,
    this.enableAchievementIntegration = true,
    this.onCelebrationGesture,
    this.celebrationMessage,
  });

  @override
  ConsumerState<CelebrationGestureDetector> createState() =>
      _CelebrationGestureDetectorState();
}

class _CelebrationGestureDetectorState
    extends ConsumerState<CelebrationGestureDetector>
    with TickerProviderStateMixin {
  late AnimationController _celebrationController;
  late AnimationController _pulseController;
  late AnimationController _scaleController;

  late Animation<double> _celebrationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  bool _isMultiTouching = false;
  int _touchCount = 0;
  DateTime? _lastTapTime;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _celebrationController = AnimationController(
      duration: AppTheme.successAnimation,
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: AppTheme.buttonPressAnimation,
      vsync: this,
    );

    _celebrationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _celebrationController,
      curve: Curves.elasticOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _celebrationController.dispose();
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  Future<void> _triggerHapticFeedback(CelebrationGestureType type) async {
    if (!widget.enableHapticFeedback || !mounted) return;

    try {
      switch (type) {
        case CelebrationGestureType.pullToRefresh:
        case CelebrationGestureType.swipeCompletion:
          await HapticFeedback.mediumImpact();
          break;
        case CelebrationGestureType.longPress:
        case CelebrationGestureType.doubleTap:
          await HapticFeedback.lightImpact();
          break;
        case CelebrationGestureType.multiTouch:
        case CelebrationGestureType.pinch:
          await HapticFeedback.heavyImpact();
          break;
      }
    } catch (e) {
      // Graceful degradation for platforms without haptic support
    }
  }

  void _triggerMascotCelebration(CelebrationGestureType type) {
    if (!widget.enableMascotIntegration || !mounted) return;

    final mascotActions = ref.read(mascotActionsProvider);
    final message = widget.celebrationMessage ?? _getDefaultMessage(type);

    mascotActions.showDiscovery(
      discoveryType: 'Gesture Celebration',
      message: message,
    );
  }

  void _triggerAchievementProgress(CelebrationGestureType type) {
    if (!widget.enableAchievementIntegration || !mounted) return;

    final achievementNotifier = ref.read(achievementNotifierProvider.notifier);

    // Track gesture usage for achievements
    achievementNotifier.trackUserAction(
      UserAction.appFeatureUsed,
      metadata: {
        'type': 'gesture_celebration',
        'gesture_type': type.name,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  String _getDefaultMessage(CelebrationGestureType type) {
    switch (type) {
      case CelebrationGestureType.pullToRefresh:
        return 'Great refresh! 🎉';
      case CelebrationGestureType.swipeCompletion:
        return 'Smooth swipe! ✨';
      case CelebrationGestureType.longPress:
        return 'Nice long press! 👍';
      case CelebrationGestureType.multiTouch:
        return 'Multi-touch master! 🙌';
      case CelebrationGestureType.doubleTap:
        return 'Double tap delight! 💫';
      case CelebrationGestureType.pinch:
        return 'Perfect pinch! 🤏';
    }
  }

  void _handleCelebration(CelebrationGestureType type) {
    if (!widget.enabled || !mounted) return;

    // Trigger animations
    _celebrationController.forward().then((_) {
      if (mounted) {
        _celebrationController.reverse();
      }
    });

    // Trigger feedback systems
    _triggerHapticFeedback(type);
    _triggerMascotCelebration(type);
    _triggerAchievementProgress(type);

    // Notify parent
    widget.onCelebrationGesture?.call(type);
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.enabled) return;

    _touchCount++;
    _scaleController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    if (!widget.enabled) return;

    _touchCount = 0;
    _scaleController.reverse();

    // Check for double tap
    final now = DateTime.now();
    if (_lastTapTime != null &&
        now.difference(_lastTapTime!).inMilliseconds < 300) {
      _handleCelebration(CelebrationGestureType.doubleTap);
    }
    _lastTapTime = now;
  }

  void _handleTapCancel() {
    if (!widget.enabled) return;

    _touchCount = 0;
    _scaleController.reverse();
  }

  void _handleLongPressStart(LongPressStartDetails details) {
    if (!widget.enabled) return;

    _pulseController.repeat(reverse: true);
  }

  void _handleLongPressEnd(LongPressEndDetails details) {
    if (!widget.enabled) return;

    _pulseController.stop();
    _pulseController.reset();

    _handleCelebration(CelebrationGestureType.longPress);
  }

  void _handlePanStart(DragStartDetails details) {
    if (!widget.enabled) return;

    // Could be start of swipe gesture
    _scaleController.forward();
  }

  void _handlePanEnd(DragEndDetails details) {
    if (!widget.enabled) return;

    _scaleController.reverse();

    // Check if it's a significant swipe
    final velocity = details.velocity.pixelsPerSecond;
    if (velocity.distance > 500) {
      _handleCelebration(CelebrationGestureType.swipeCompletion);
    }
  }

  void _handleScaleStart(ScaleStartDetails details) {
    if (!widget.enabled) return;

    if (details.pointerCount > 1) {
      _isMultiTouching = true;
      _touchCount = details.pointerCount;
    }
  }

  void _handleScaleEnd(ScaleEndDetails details) {
    if (!widget.enabled) return;

    if (_isMultiTouching) {
      _isMultiTouching = false;

      if (_touchCount > 1) {
        _handleCelebration(CelebrationGestureType.multiTouch);
      }

      _touchCount = 0;
    }
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (!widget.enabled) return;

    // Check for pinch gesture
    if (details.scale != 1.0 && _isMultiTouching) {
      // Trigger pinch celebration on significant scale change
      if ((details.scale - 1.0).abs() > 0.5) {
        _handleCelebration(CelebrationGestureType.pinch);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: Listenable.merge([
        _celebrationAnimation,
        _pulseAnimation,
        _scaleAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value * _pulseAnimation.value,
          child: Container(
            decoration: _celebrationAnimation.value > 0
                ? BoxDecoration(
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusMedium),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.primary.withAlpha(
                              (128 * _celebrationAnimation.value).round(),
                            ),
                        blurRadius: 20 * _celebrationAnimation.value,
                        spreadRadius: 5 * _celebrationAnimation.value,
                      ),
                    ],
                  )
                : null,
            child: GestureDetector(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              onLongPressStart: _handleLongPressStart,
              onLongPressEnd: _handleLongPressEnd,
              onPanStart: _handlePanStart,
              onPanEnd: _handlePanEnd,
              onScaleStart: _handleScaleStart,
              onScaleUpdate: _handleScaleUpdate,
              onScaleEnd: _handleScaleEnd,
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}
