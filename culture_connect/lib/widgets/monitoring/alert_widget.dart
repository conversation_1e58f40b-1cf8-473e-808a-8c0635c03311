import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/services/monitoring/alert_service.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Main alert display widget
class AlertWidget extends ConsumerStatefulWidget {
  /// Whether to show in compact mode
  final bool isCompact;

  /// Maximum number of alerts to show
  final int maxAlerts;

  /// Whether to show resolved alerts
  final bool showResolved;

  /// Callback when alert is tapped
  final void Function(MonitoringAlert alert)? onAlertTapped;

  /// Creates a new alert widget
  const AlertWidget({
    super.key,
    this.isCompact = false,
    this.maxAlerts = 5,
    this.showResolved = false,
    this.onAlertTapped,
  });

  @override
  ConsumerState<AlertWidget> createState() => _AlertWidgetState();
}

class _AlertWidgetState extends ConsumerState<AlertWidget>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  List<MonitoringAlert> _alerts = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _loadAlerts();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _loadAlerts() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final alertService = ref.read(alertServiceProvider);
      await alertService.initialize();

      final alerts = widget.showResolved
          ? alertService.getAlertHistory(limit: widget.maxAlerts)
          : alertService.getActiveAlerts();

      if (mounted) {
        setState(() {
          _alerts = alerts.take(widget.maxAlerts).toList();
          _isLoading = false;
        });
        _fadeController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return _buildLoadingState(theme);
    }

    if (_error != null) {
      return _buildErrorState(theme);
    }

    if (_alerts.isEmpty) {
      return _buildEmptyState(theme);
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: widget.isCompact
          ? _buildCompactLayout(theme)
          : _buildFullLayout(theme),
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Container(
      height: widget.isCompact ? 80 : 200,
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                color: theme.colorScheme.primary,
                strokeWidth: 2,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              'Loading alerts...',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme) {
    return Container(
      height: widget.isCompact ? 80 : 200,
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withAlpha(26),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.error.withAlpha(51),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: theme.colorScheme.error,
              size: 24,
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              'Failed to load alerts',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            TextButton(
              onPressed: _loadAlerts,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      height: widget.isCompact ? 80 : 200,
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              color: theme.colorScheme.primary,
              size: 32,
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              widget.showResolved ? 'No alert history' : 'No active alerts',
              style: theme.textTheme.titleSmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              widget.showResolved
                  ? 'Alert history will appear here'
                  : 'All systems are running smoothly',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactLayout(ThemeData theme) {
    final highestPriorityAlert = _alerts.reduce(
        (a, b) => a.priority.numericValue > b.priority.numericValue ? a : b);

    return Container(
      height: 80,
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Row(
        children: [
          _buildPriorityIndicator(theme, highestPriorityAlert.priority),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${_alerts.length} Alert${_alerts.length == 1 ? '' : 's'}',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  highestPriorityAlert.title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => widget.onAlertTapped?.call(highestPriorityAlert),
            icon: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullLayout(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme),
          const SizedBox(height: AppTheme.spacingMedium),
          ..._alerts.map((alert) => Padding(
                padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
                child: AlertCard(
                  alert: alert,
                  onTap: () => widget.onAlertTapped?.call(alert),
                  onAcknowledge: () => _acknowledgeAlert(alert.id),
                  onResolve: () => _resolveAlert(alert.id),
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Row(
      children: [
        Icon(
          Icons.notifications_outlined,
          color: theme.colorScheme.primary,
          size: 20,
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Text(
          widget.showResolved ? 'Alert History' : 'Active Alerts',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: _loadAlerts,
          icon: Icon(
            Icons.refresh,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          tooltip: 'Refresh alerts',
        ),
      ],
    );
  }

  Widget _buildPriorityIndicator(ThemeData theme, AlertPriority priority) {
    Color color;
    IconData icon;

    switch (priority) {
      case AlertPriority.low:
        color = Colors.blue;
        icon = Icons.info_outline;
        break;
      case AlertPriority.medium:
        color = Colors.orange;
        icon = Icons.warning_amber_outlined;
        break;
      case AlertPriority.high:
        color = Colors.red;
        icon = Icons.error_outline;
        break;
      case AlertPriority.critical:
        color = Colors.red.shade700;
        icon = Icons.dangerous_outlined;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Icon(
        icon,
        color: color,
        size: 20,
      ),
    );
  }

  Future<void> _acknowledgeAlert(String alertId) async {
    try {
      final alertService = ref.read(alertServiceProvider);
      await alertService.acknowledgeAlert(alertId);
      await _loadAlerts(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to acknowledge alert: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _resolveAlert(String alertId) async {
    try {
      final alertService = ref.read(alertServiceProvider);
      await alertService.resolveAlert(alertId);
      await _loadAlerts(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to resolve alert: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}

/// Individual alert card widget
class AlertCard extends StatelessWidget {
  /// The alert to display
  final MonitoringAlert alert;

  /// Callback when card is tapped
  final VoidCallback? onTap;

  /// Callback when acknowledge button is tapped
  final VoidCallback? onAcknowledge;

  /// Callback when resolve button is tapped
  final VoidCallback? onResolve;

  /// Creates a new alert card
  const AlertCard({
    super.key,
    required this.alert,
    this.onTap,
    this.onAcknowledge,
    this.onResolve,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          decoration: BoxDecoration(
            color: _getPriorityColor(alert.priority).withAlpha(13),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            border: Border.all(
              color: _getPriorityColor(alert.priority).withAlpha(51),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _buildPriorityBadge(theme),
                  const SizedBox(width: AppTheme.spacingSmall),
                  Expanded(
                    child: Text(
                      alert.title,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                  _buildStatusBadge(theme),
                ],
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                alert.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 14,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatAge(alert.age),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Spacer(),
                  if (alert.isActive) ...[
                    if (alert.status != AlertStatus.acknowledged)
                      TextButton(
                        onPressed: onAcknowledge,
                        child: const Text('Acknowledge'),
                      ),
                    const SizedBox(width: AppTheme.spacingSmall),
                    TextButton(
                      onPressed: onResolve,
                      child: const Text('Resolve'),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityBadge(ThemeData theme) {
    final color = _getPriorityColor(alert.priority);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSmall,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Text(
        alert.priority.displayName.toUpperCase(),
        style: theme.textTheme.labelSmall?.copyWith(
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildStatusBadge(ThemeData theme) {
    Color color;
    switch (alert.status) {
      case AlertStatus.active:
        color = Colors.red;
        break;
      case AlertStatus.acknowledged:
        color = Colors.orange;
        break;
      case AlertStatus.resolved:
        color = Colors.green;
        break;
      case AlertStatus.dismissed:
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSmall,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Text(
        alert.status.displayName,
        style: theme.textTheme.labelSmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getPriorityColor(AlertPriority priority) {
    switch (priority) {
      case AlertPriority.low:
        return Colors.blue;
      case AlertPriority.medium:
        return Colors.orange;
      case AlertPriority.high:
        return Colors.red;
      case AlertPriority.critical:
        return Colors.red.shade700;
    }
  }

  String _formatAge(Duration age) {
    if (age.inDays > 0) {
      return '${age.inDays}d ago';
    } else if (age.inHours > 0) {
      return '${age.inHours}h ago';
    } else if (age.inMinutes > 0) {
      return '${age.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Alert statistics widget
class AlertStatisticsWidget extends ConsumerWidget {
  /// Height of the widget
  final double height;

  /// Creates a new alert statistics widget
  const AlertStatisticsWidget({
    super.key,
    this.height = 150,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Container(
      height: height,
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics_outlined,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Text(
                'Alert Statistics',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Expanded(
            child: FutureBuilder<Map<String, dynamic>>(
              future: _getAlertStatistics(ref),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Text(
                      'Failed to load statistics',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.error,
                      ),
                    ),
                  );
                }

                final stats = snapshot.data ?? {};
                return _buildStatisticsContent(theme, stats);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsContent(ThemeData theme, Map<String, dynamic> stats) {
    final totalActive = stats['totalActive'] as int? ?? 0;
    final totalResolved = stats['totalResolved'] as int? ?? 0;
    final resolvedToday = stats['resolvedToday'] as int? ?? 0;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            theme,
            'Active',
            totalActive.toString(),
            Icons.warning_amber_outlined,
            Colors.orange,
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            theme,
            'Resolved',
            totalResolved.toString(),
            Icons.check_circle_outline,
            Colors.green,
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            theme,
            'Today',
            resolvedToday.toString(),
            Icons.today_outlined,
            theme.colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    ThemeData theme,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        color: color.withAlpha(13),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        border: Border.all(
          color: color.withAlpha(51),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Future<Map<String, dynamic>> _getAlertStatistics(WidgetRef ref) async {
    try {
      final alertService = ref.read(alertServiceProvider);
      await alertService.initialize();
      return alertService.getAlertStatistics();
    } catch (e) {
      return {};
    }
  }
}
