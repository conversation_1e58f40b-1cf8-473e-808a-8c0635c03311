// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/flight/flight_info.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Enum for different flight status widget variants
enum FlightStatusVariant {
  /// Compact status indicator
  compact,

  /// Full status card with details
  full,

  /// Timeline view with progress
  timeline,
}

/// A widget for displaying flight status with real-time updates
class FlightStatusWidget extends ConsumerStatefulWidget {
  /// Flight information to display
  final FlightInfo flightInfo;

  /// Widget variant
  final FlightStatusVariant variant;

  /// Whether to show detailed information
  final bool showDetails;

  /// Whether to enable haptic feedback
  final bool enableHapticFeedback;

  /// Callback when status is tapped
  final VoidCallback? onTap;

  /// Whether to show refresh button
  final bool showRefresh;

  /// Callback for refresh action
  final VoidCallback? onRefresh;

  /// Creates a new flight status widget
  const FlightStatusWidget({
    super.key,
    required this.flightInfo,
    this.variant = FlightStatusVariant.full,
    this.showDetails = true,
    this.enableHapticFeedback = true,
    this.onTap,
    this.showRefresh = false,
    this.onRefresh,
  });

  @override
  ConsumerState<FlightStatusWidget> createState() => _FlightStatusWidgetState();
}

class _FlightStatusWidgetState extends ConsumerState<FlightStatusWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _progressController = AnimationController(
      duration: AppTheme.longAnimation,
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    // Start animations based on flight status
    _startStatusAnimations();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(FlightStatusWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.flightInfo.status != widget.flightInfo.status) {
      _startStatusAnimations();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    switch (widget.variant) {
      case FlightStatusVariant.compact:
        return _buildCompactStatus(theme);
      case FlightStatusVariant.timeline:
        return _buildTimelineStatus(theme);
      case FlightStatusVariant.full:
        return _buildFullStatus(theme);
    }
  }

  /// Build compact status indicator
  Widget _buildCompactStatus(ThemeData theme) {
    return GestureDetector(
      onTap: widget.onTap != null ? _handleTap : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: widget.flightInfo.status.color.withAlpha(26), // 0.1 opacity
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
          border: Border.all(
            color: widget.flightInfo.status.color.withAlpha(77), // 0.3 opacity
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _shouldPulse ? _pulseAnimation.value : 1.0,
                  child: Icon(
                    widget.flightInfo.status.icon,
                    size: 16,
                    color: widget.flightInfo.status.color,
                  ),
                );
              },
            ),
            const SizedBox(width: 8),
            Text(
              widget.flightInfo.status.displayName,
              style: theme.textTheme.bodySmall?.copyWith(
                color: widget.flightInfo.status.color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build full status card
  Widget _buildFullStatus(ThemeData theme) {
    return Card(
      child: InkWell(
        onTap: widget.onTap != null ? _handleTap : null,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatusHeader(theme),
              if (widget.showDetails) ...[
                const SizedBox(height: AppTheme.spacingMedium),
                _buildFlightDetails(theme),
              ],
              if (widget.flightInfo.delayMinutes != null) ...[
                const SizedBox(height: AppTheme.spacingSmall),
                _buildDelayInfo(theme),
              ],
              if (widget.flightInfo.departureGate != null ||
                  widget.flightInfo.departureTerminal != null) ...[
                const SizedBox(height: AppTheme.spacingSmall),
                _buildGateTerminalInfo(theme),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build timeline status view
  Widget _buildTimelineStatus(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusHeader(theme),
            const SizedBox(height: AppTheme.spacingMedium),
            _buildFlightTimeline(theme),
          ],
        ),
      ),
    );
  }

  /// Build status header
  Widget _buildStatusHeader(ThemeData theme) {
    return Row(
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _shouldPulse ? _pulseAnimation.value : 1.0,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: widget.flightInfo.status.color
                      .withAlpha(26), // 0.1 opacity
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  widget.flightInfo.status.icon,
                  size: 24,
                  color: widget.flightInfo.status.color,
                ),
              ),
            );
          },
        ),
        const SizedBox(width: AppTheme.spacingMedium),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.flightInfo.flightNumber,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                widget.flightInfo.status.displayName,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: widget.flightInfo.status.color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        if (widget.showRefresh)
          IconButton(
            onPressed: widget.onRefresh,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Status',
          ),
      ],
    );
  }

  /// Build flight details
  Widget _buildFlightDetails(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: _buildAirportInfo(
            theme,
            widget.flightInfo.departureAirportCode,
            'Departure',
            _formatTime(widget.flightInfo.scheduledDepartureTime),
          ),
        ),
        const SizedBox(width: AppTheme.spacingMedium),
        Icon(
          Icons.flight_takeoff,
          color: theme.colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: AppTheme.spacingMedium),
        Expanded(
          child: _buildAirportInfo(
            theme,
            widget.flightInfo.arrivalAirportCode,
            'Arrival',
            _formatTime(widget.flightInfo.scheduledArrival),
          ),
        ),
      ],
    );
  }

  /// Build airport information
  Widget _buildAirportInfo(
      ThemeData theme, String airport, String label, String time) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          airport,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          time,
          style: theme.textTheme.bodyMedium,
        ),
      ],
    );
  }

  /// Build delay information
  Widget _buildDelayInfo(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        color: Colors.orange.withAlpha(26), // 0.1 opacity
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.schedule,
            size: 16,
            color: Colors.orange,
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Text(
            'Delayed by ${widget.flightInfo.delayMinutes} minutes',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.orange,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build gate and terminal information
  Widget _buildGateTerminalInfo(ThemeData theme) {
    return Row(
      children: [
        if (widget.flightInfo.departureGate != null) ...[
          const Icon(
            Icons.location_on,
            size: 16,
            color: Colors.blue,
          ),
          const SizedBox(width: 4),
          Text(
            'Gate ${widget.flightInfo.departureGate}',
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
        if (widget.flightInfo.departureGate != null &&
            widget.flightInfo.departureTerminal != null)
          const SizedBox(width: AppTheme.spacingMedium),
        if (widget.flightInfo.departureTerminal != null) ...[
          const Icon(
            Icons.business,
            size: 16,
            color: Colors.blue,
          ),
          const SizedBox(width: 4),
          Text(
            widget.flightInfo.departureTerminal!,
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ],
    );
  }

  /// Build flight timeline
  Widget _buildFlightTimeline(ThemeData theme) {
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return Column(
          children: [
            _buildTimelineStep(
              theme,
              'Scheduled',
              _formatTime(widget.flightInfo.scheduledDepartureTime),
              true,
              widget.flightInfo.status != FlightStatus.scheduled,
            ),
            _buildTimelineStep(
              theme,
              'Boarding',
              'Gate ${widget.flightInfo.departureGate ?? 'TBA'}',
              widget.flightInfo.status == FlightStatus.boarding,
              widget.flightInfo.status.index > FlightStatus.boarding.index,
            ),
            _buildTimelineStep(
              theme,
              'Departed',
              widget.flightInfo.actualDepartureTime != null
                  ? _formatTime(widget.flightInfo.actualDepartureTime!)
                  : 'TBA',
              widget.flightInfo.status == FlightStatus.inAir,
              widget.flightInfo.status.index > FlightStatus.inAir.index,
            ),
            _buildTimelineStep(
              theme,
              'Arrived',
              _formatTime(widget.flightInfo.scheduledArrival),
              widget.flightInfo.status == FlightStatus.landed,
              false,
              isLast: true,
            ),
          ],
        );
      },
    );
  }

  /// Build timeline step
  Widget _buildTimelineStep(
    ThemeData theme,
    String title,
    String subtitle,
    bool isActive,
    bool isCompleted, {
    bool isLast = false,
  }) {
    final color = isCompleted
        ? Colors.green
        : isActive
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurfaceVariant;

    return Row(
      children: [
        Column(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: isCompleted || isActive ? color : Colors.transparent,
                border: Border.all(color: color, width: 2),
                shape: BoxShape.circle,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 32,
                color: color.withAlpha(77), // 0.3 opacity
              ),
          ],
        ),
        const SizedBox(width: AppTheme.spacingMedium),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  color: color,
                ),
              ),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              if (!isLast) const SizedBox(height: AppTheme.spacingSmall),
            ],
          ),
        ),
      ],
    );
  }

  /// Handle tap with haptic feedback
  void _handleTap() async {
    if (widget.enableHapticFeedback) {
      await HapticFeedback.selectionClick();
    }
    widget.onTap?.call();
  }

  /// Start status-based animations
  void _startStatusAnimations() {
    if (_shouldPulse) {
      _pulseController.repeat(reverse: true);
    } else {
      _pulseController.stop();
      _pulseController.reset();
    }

    // Start progress animation for timeline
    if (widget.variant == FlightStatusVariant.timeline) {
      _progressController.forward();
    }
  }

  /// Check if status should pulse
  bool get _shouldPulse {
    return widget.flightInfo.status == FlightStatus.boarding ||
        widget.flightInfo.status == FlightStatus.delayed ||
        widget.flightInfo.status == FlightStatus.cancelled;
  }

  /// Format time from DateTime
  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
