// Flutter imports
import 'package:flutter/material.dart';

// Third-party imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';

// Project imports
import 'package:culture_connect/models/mascot/mascot_state.dart';
import 'package:culture_connect/providers/mascot_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Main mascot widget that displays the animated character
class MascotWidget extends ConsumerStatefulWidget {
  /// The size of the mascot widget
  final double size;

  /// Whether to show the mascot expression label
  final bool showLabel;

  /// Custom mascot state to display (overrides provider state)
  final MascotState? customState;

  /// Callback when mascot animation completes
  final VoidCallback? onAnimationComplete;

  /// Whether to enable tap interactions
  final bool enableTap;

  /// Custom tap callback
  final VoidCallback? onTap;

  /// Creates a new mascot widget
  const MascotWidget({
    super.key,
    this.size = 120.0,
    this.showLabel = false,
    this.customState,
    this.onAnimationComplete,
    this.enableTap = false,
    this.onTap,
  });

  @override
  ConsumerState<MascotWidget> createState() => _MascotWidgetState();
}

class _MascotWidgetState extends ConsumerState<MascotWidget>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  late AnimationController _pulseController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;

  MascotState? _previousState;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Scale animation for expression transitions
    _scaleController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // Rotation animation for excited expressions
    _rotationController = AnimationController(
      duration: AppTheme.longAnimation,
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeInOut,
    ));

    // Pulse animation for helpful expressions
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _handleStateChange(MascotState newState) {
    if (_previousState?.expression != newState.expression) {
      _triggerTransitionAnimation(newState.expression);
    }
    _previousState = newState;
  }

  void _triggerTransitionAnimation(MascotExpression expression) {
    // Reset all animations
    _scaleController.reset();
    _rotationController.reset();
    _pulseController.reset();

    switch (expression) {
      case MascotExpression.celebrating:
        _scaleController.forward().then((_) {
          if (mounted) {
            _scaleController.reverse();
            widget.onAnimationComplete?.call();
          }
        });
        break;
      case MascotExpression.excited:
        _rotationController.repeat(reverse: true);
        break;
      case MascotExpression.helpful:
        _pulseController.repeat(reverse: true);
        break;
      case MascotExpression.happy:
        _scaleController.forward().then((_) {
          if (mounted) {
            _scaleController.reverse();
          }
        });
        break;
      case MascotExpression.sympathetic:
        // Gentle fade effect (handled by opacity in build method)
        break;
    }
  }

  Widget _buildLottieAnimation(MascotState state) {
    final expression = state.expression;

    return Lottie.network(
      expression.fallbackAnimationUrl,
      width: widget.size,
      height: widget.size,
      fit: BoxFit.contain,
      repeat: expression.shouldRepeat,
      animate: state.isAnimating,
      onLoaded: (composition) {
        if (mounted && widget.onAnimationComplete != null) {
          Future.delayed(expression.animationDuration, () {
            if (mounted) {
              widget.onAnimationComplete?.call();
            }
          });
        }
      },
      errorBuilder: (context, error, stackTrace) {
        return _buildFallbackMascot(state);
      },
    );
  }

  Widget _buildFallbackMascot(MascotState state) {
    final expression = state.expression;

    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: expression.primaryColor.withAlpha(51),
        border: Border.all(
          color: expression.primaryColor,
          width: 2,
        ),
      ),
      child: Icon(
        expression.icon,
        size: widget.size * 0.5,
        color: expression.primaryColor,
      ),
    );
  }

  Widget _buildMascotContent(MascotState state) {
    Widget mascotWidget = _buildLottieAnimation(state);

    // Apply expression-specific animations
    switch (state.expression) {
      case MascotExpression.celebrating:
      case MascotExpression.happy:
        mascotWidget = ScaleTransition(
          scale: _scaleAnimation,
          child: mascotWidget,
        );
        break;
      case MascotExpression.excited:
        mascotWidget = RotationTransition(
          turns: _rotationAnimation,
          child: mascotWidget,
        );
        break;
      case MascotExpression.helpful:
        mascotWidget = ScaleTransition(
          scale: _pulseAnimation,
          child: mascotWidget,
        );
        break;
      case MascotExpression.sympathetic:
        mascotWidget = AnimatedOpacity(
          opacity: 0.7,
          duration: AppTheme.shortAnimation,
          child: mascotWidget,
        );
        break;
    }

    return mascotWidget;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Use custom state if provided, otherwise watch the provider
    final MascotState currentState;
    if (widget.customState != null) {
      currentState = widget.customState!;
    } else {
      final asyncState = ref.watch(mascotStateProvider);
      currentState = asyncState.when(
        data: (state) => state,
        loading: () => MascotState.loading(),
        error: (_, __) =>
            MascotState.error(errorMessage: 'Failed to load mascot'),
      );
    }

    // Handle state changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleStateChange(currentState);
    });

    Widget mascotContent = _buildMascotContent(currentState);

    // Wrap with tap gesture if enabled
    if (widget.enableTap) {
      mascotContent = GestureDetector(
        onTap: widget.onTap,
        child: mascotContent,
      );
    }

    // Add label if requested
    if (widget.showLabel) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          mascotContent,
          const SizedBox(height: 8),
          Text(
            currentState.expression.displayName,
            style: theme.textTheme.bodySmall?.copyWith(
              color: currentState.expression.primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    }

    return mascotContent;
  }
}

/// Compact version of the mascot widget for smaller spaces
class CompactMascotWidget extends StatelessWidget {
  /// The mascot state to display
  final MascotState? state;

  /// The size of the compact mascot
  final double size;

  /// Creates a new compact mascot widget
  const CompactMascotWidget({
    super.key,
    this.state,
    this.size = 32.0,
  });

  @override
  Widget build(BuildContext context) {
    return MascotWidget(
      size: size,
      customState: state,
      showLabel: false,
      enableTap: false,
    );
  }
}

/// Mascot widget specifically for achievement celebrations
class CelebrationMascotWidget extends StatelessWidget {
  /// The achievement that was unlocked
  final String? achievementTitle;

  /// Callback when celebration completes
  final VoidCallback? onComplete;

  /// Creates a new celebration mascot widget
  const CelebrationMascotWidget({
    super.key,
    this.achievementTitle,
    this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    final celebrationState = MascotState.achievementCelebration(
      achievementTitle: achievementTitle,
    );

    return MascotWidget(
      size: 150.0,
      customState: celebrationState,
      showLabel: true,
      onAnimationComplete: onComplete,
    );
  }
}
