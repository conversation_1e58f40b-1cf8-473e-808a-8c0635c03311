// <PERSON>t imports
import 'dart:math' as math;

// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Project imports
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Widget for displaying achievement celebration animations
class CelebrationAnimation extends StatefulWidget {
  /// The user achievement to celebrate
  final UserAchievement userAchievement;

  /// Callback when the celebration is complete
  final VoidCallback? onComplete;

  /// Whether to show the celebration immediately
  final bool autoStart;

  const CelebrationAnimation({
    super.key,
    required this.userAchievement,
    this.onComplete,
    this.autoStart = true,
  });

  @override
  State<CelebrationAnimation> createState() => _CelebrationAnimationState();
}

class _CelebrationAnimationState extends State<CelebrationAnimation>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late AnimationController _confettiController;
  late AnimationController _glowController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _confettiAnimation;
  late Animation<double> _glowAnimation;

  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();

    if (widget.autoStart) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startCelebration();
      });
    }
  }

  void _initializeAnimations() {
    // Scale animation for badge reveal
    _scaleController = AnimationController(
      duration: AppTheme.longAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // Fade animation for overall appearance
    _fadeController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Confetti animation
    _confettiController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _confettiAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _confettiController,
      curve: Curves.easeOut,
    ));

    // Glow animation for the badge
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _startCelebration() async {
    if (_isAnimating) return;

    setState(() {
      _isAnimating = true;
    });

    // Trigger haptic feedback
    await HapticFeedback.mediumImpact();

    // Start animations in sequence
    _fadeController.forward();

    await Future.delayed(const Duration(milliseconds: 100));
    _scaleController.forward();

    await Future.delayed(const Duration(milliseconds: 200));
    _confettiController.forward();
    _glowController.forward();

    // Wait for animations to complete
    await Future.delayed(const Duration(milliseconds: 2000));

    if (mounted) {
      widget.onComplete?.call();
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    _confettiController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final achievement = widget.userAchievement.achievement;

    return Material(
      color: Colors.black.withAlpha(128),
      child: Center(
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _scaleAnimation,
            _fadeAnimation,
            _confettiAnimation,
            _glowAnimation,
          ]),
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Confetti particles
                  if (_confettiAnimation.value > 0)
                    ..._buildConfettiParticles(achievement.color),

                  // Main celebration card
                  ScaleTransition(
                    scale: _scaleAnimation,
                    child: Container(
                      width: 300,
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surface,
                        borderRadius:
                            BorderRadius.circular(AppTheme.borderRadiusLarge),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(51),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Achievement unlocked text
                          Text(
                            'Achievement Unlocked!',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: achievement.color,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 24),

                          // Achievement badge with glow effect
                          Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: _glowAnimation.value > 0
                                  ? [
                                      BoxShadow(
                                        color: achievement.color.withAlpha(
                                          (128 * _glowAnimation.value).round(),
                                        ),
                                        blurRadius: 30 * _glowAnimation.value,
                                        spreadRadius: 5 * _glowAnimation.value,
                                      ),
                                    ]
                                  : null,
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                color: achievement.color,
                                shape: BoxShape.circle,
                                gradient: RadialGradient(
                                  colors: [
                                    achievement.color,
                                    achievement.color.withAlpha(204),
                                  ],
                                ),
                              ),
                              child: Icon(
                                achievement.icon,
                                size: 60,
                                color: Colors.white,
                              ),
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Achievement title
                          Text(
                            achievement.title,
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 8),

                          // Achievement description
                          Text(
                            achievement.description,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 24),

                          // Reward section
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: achievement.color.withAlpha(26),
                              borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusMedium),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.card_giftcard,
                                  color: achievement.color,
                                  size: 24,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Reward: ${achievement.rewardDisplayName}',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: achievement.color,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Continue button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: () {
                                widget.onComplete?.call();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: achievement.color,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(
                                      AppTheme.borderRadiusMedium),
                                ),
                              ),
                              child: const Text(
                                'Continue',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  List<Widget> _buildConfettiParticles(Color primaryColor) {
    final particles = <Widget>[];
    const particleCount = 20;

    for (int i = 0; i < particleCount; i++) {
      final angle = (i / particleCount) * 2 * 3.14159;
      final distance = 150 * _confettiAnimation.value;
      final x = distance * math.cos(angle);
      final y = distance * math.sin(angle);

      particles.add(
        Positioned(
          left: MediaQuery.of(context).size.width / 2 + x - 4,
          top: MediaQuery.of(context).size.height / 2 + y - 4,
          child: Transform.rotate(
            angle: angle + (_confettiAnimation.value * 4),
            child: Opacity(
              opacity: (1 - _confettiAnimation.value).clamp(0.0, 1.0),
              child: Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: i % 3 == 0
                      ? primaryColor
                      : i % 3 == 1
                          ? Colors.amber
                          : Colors.pink,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
        ),
      );
    }

    return particles;
  }
}
