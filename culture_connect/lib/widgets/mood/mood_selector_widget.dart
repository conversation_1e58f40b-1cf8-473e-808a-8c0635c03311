// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/mood/mood_model.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/mascot_widget.dart';

/// Enum for different mood selector variants
enum MoodSelectorVariant {
  /// Compact horizontal layout
  compact,

  /// Full vertical layout with labels
  full,

  /// Horizontal layout with labels
  horizontal,
}

/// A widget for selecting mood with emoji interface and animations
class MoodSelectorWidget extends ConsumerStatefulWidget {
  /// Initial mood selection
  final MoodType? initialMood;

  /// Callback when mood is selected
  final ValueChanged<MoodType>? onMoodSelected;

  /// Whether to show mood labels
  final bool showLabels;

  /// Whether to enable haptic feedback
  final bool enableHapticFeedback;

  /// Whether to show mascot preview
  final bool showMascotPreview;

  /// Widget variant
  final MoodSelectorVariant variant;

  /// Custom emoji size
  final double? emojiSize;

  /// Whether the selector is enabled
  final bool enabled;

  /// Creates a new mood selector widget
  const MoodSelectorWidget({
    super.key,
    this.initialMood,
    this.onMoodSelected,
    this.showLabels = true,
    this.enableHapticFeedback = true,
    this.showMascotPreview = false,
    this.variant = MoodSelectorVariant.full,
    this.emojiSize,
    this.enabled = true,
  });

  @override
  ConsumerState<MoodSelectorWidget> createState() => _MoodSelectorWidgetState();
}

class _MoodSelectorWidgetState extends ConsumerState<MoodSelectorWidget>
    with TickerProviderStateMixin {
  MoodType? _selectedMood;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _selectedMood = widget.initialMood;

    _animationController = AnimationController(
      duration: AppTheme.shortAnimation,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: AppTheme.buttonPressScale,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    switch (widget.variant) {
      case MoodSelectorVariant.compact:
        return _buildCompactLayout(theme);
      case MoodSelectorVariant.horizontal:
        return _buildHorizontalLayout(theme);
      case MoodSelectorVariant.full:
        return _buildFullLayout(theme);
    }
  }

  /// Build compact horizontal layout
  Widget _buildCompactLayout(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: MoodType.values
          .map((mood) => _buildMoodButton(
                mood: mood,
                theme: theme,
                size: widget.emojiSize ?? 32,
                showLabel: false,
              ))
          .toList(),
    );
  }

  /// Build horizontal layout with labels
  Widget _buildHorizontalLayout(ThemeData theme) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: MoodType.values
              .map((mood) => _buildMoodButton(
                    mood: mood,
                    theme: theme,
                    size: widget.emojiSize ?? 40,
                    showLabel: false,
                  ))
              .toList(),
        ),
        if (widget.showLabels && _selectedMood != null) ...[
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            _selectedMood!.displayName,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: _selectedMood!.color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
        if (widget.showMascotPreview) ...[
          const SizedBox(height: AppTheme.spacingMedium),
          _buildMascotPreview(),
        ],
      ],
    );
  }

  /// Build full vertical layout
  Widget _buildFullLayout(ThemeData theme) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Wrap(
          alignment: WrapAlignment.center,
          spacing: AppTheme.spacingMedium,
          runSpacing: AppTheme.spacingMedium,
          children: MoodType.values
              .map((mood) => _buildMoodButton(
                    mood: mood,
                    theme: theme,
                    size: widget.emojiSize ?? 48,
                    showLabel: widget.showLabels,
                  ))
              .toList(),
        ),
        if (widget.showMascotPreview && _selectedMood != null) ...[
          const SizedBox(height: AppTheme.spacingLarge),
          _buildMascotPreview(),
        ],
      ],
    );
  }

  /// Build individual mood button
  Widget _buildMoodButton({
    required MoodType mood,
    required ThemeData theme,
    required double size,
    required bool showLabel,
  }) {
    final isSelected = _selectedMood == mood;

    return GestureDetector(
      onTap: widget.enabled ? () => _selectMood(mood) : null,
      onTapDown: widget.enabled ? (_) => _animatePress(true) : null,
      onTapUp: widget.enabled ? (_) => _animatePress(false) : null,
      onTapCancel: widget.enabled ? () => _animatePress(false) : null,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: isSelected ? _scaleAnimation.value : 1.0,
            child: Opacity(
              opacity: widget.enabled ? 1.0 : 0.5,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: size + 16,
                    height: size + 16,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? mood.color.withAlpha(51) // 0.2 opacity
                          : Colors.transparent,
                      border: isSelected
                          ? Border.all(color: mood.color, width: 2)
                          : null,
                    ),
                    child: Center(
                      child: Text(
                        mood.emoji,
                        style: TextStyle(fontSize: size),
                        semanticsLabel: mood.displayName,
                      ),
                    ),
                  ),
                  if (showLabel) ...[
                    const SizedBox(height: 4),
                    Text(
                      mood.displayName,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isSelected
                            ? mood.color
                            : theme.colorScheme.onSurfaceVariant,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Build mascot preview widget
  Widget _buildMascotPreview() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Theme.of(context)
            .colorScheme
            .surfaceContainerHighest
            .withAlpha(128),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Mascot Preview',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          const SizedBox(
            width: 60,
            height: 60,
            child: MascotWidget(
              size: 60,
            ),
          ),
        ],
      ),
    );
  }

  /// Handle mood selection
  void _selectMood(MoodType mood) async {
    if (!widget.enabled) return;

    setState(() {
      _selectedMood = mood;
    });

    // Trigger haptic feedback
    if (widget.enableHapticFeedback) {
      await HapticFeedback.mediumImpact();
    }

    // Update mascot preview if enabled
    if (widget.showMascotPreview) {
      _updateMascotPreview(mood);
    }

    // Notify parent
    widget.onMoodSelected?.call(mood);
  }

  /// Animate button press
  void _animatePress(bool pressed) {
    if (!widget.enabled) return;

    if (pressed) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  /// Update mascot preview based on selected mood
  void _updateMascotPreview(MoodType mood) {
    final mascotActions = ref.read(mascotActionsProvider);

    switch (mood) {
      case MoodType.veryHappy:
      case MoodType.happy:
        mascotActions.showHappyExpression();
        break;
      case MoodType.neutral:
        mascotActions.showHelpfulExpression();
        break;
      case MoodType.sad:
      case MoodType.verySad:
        mascotActions.showSympatheticExpression();
        break;
    }
  }
}

/// Provider for mascot actions helper
final mascotActionsProvider = Provider((ref) {
  return MascotActions(ref);
});

/// Helper class for mascot actions
class MascotActions {
  MascotActions(Ref ref);

  /// Show happy expression
  void showHappyExpression() {
    // Implementation would trigger happy mascot expression
  }

  /// Show helpful expression
  void showHelpfulExpression() {
    // Implementation would trigger helpful mascot expression
  }

  /// Show sympathetic expression
  void showSympatheticExpression() {
    // Implementation would trigger sympathetic mascot expression
  }
}
