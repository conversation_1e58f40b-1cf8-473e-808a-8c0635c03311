// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Third-party imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/providers/mascot_provider.dart';

/// Button types for different visual styles
enum ButtonType { primary, secondary, outlined, text }

/// Button states for micro-interactions
enum ButtonState { idle, pressed, loading, success, error, disabled }

/// Haptic feedback intensity levels
enum HapticIntensity { light, medium, heavy }

class CustomButton extends ConsumerStatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final bool isFullWidth;
  final bool isLoading;
  final IconData? icon;
  final double? width;
  final double height;
  final EdgeInsets? padding;
  final double borderRadius;
  final bool enableHapticFeedback;
  final bool enableMascotIntegration;
  final String? successMessage;
  final String? errorMessage;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.isFullWidth = true,
    this.isLoading = false,
    this.icon,
    this.width,
    this.height = 56,
    this.padding,
    this.borderRadius = 12,
    this.enableHapticFeedback = true,
    this.enableMascotIntegration = true,
    this.successMessage,
    this.errorMessage,
  });

  @override
  ConsumerState<CustomButton> createState() => _CustomButtonState();
}

class _CustomButtonState extends ConsumerState<CustomButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _loadingController;
  late AnimationController _successController;

  late Animation<double> _scaleAnimation;

  ButtonState _currentState = ButtonState.idle;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _scaleController = AnimationController(
      duration: AppTheme.buttonPressAnimation,
      vsync: this,
    );

    _loadingController = AnimationController(
      duration: AppTheme.loadingAnimation,
      vsync: this,
    );

    _successController = AnimationController(
      duration: AppTheme.successAnimation,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: AppTheme.buttonPressScale,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    // Loading and success animations are handled by controllers directly
  }

  @override
  void didUpdateWidget(CustomButton oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.isLoading != widget.isLoading) {
      if (widget.isLoading) {
        _currentState = ButtonState.loading;
        _loadingController.repeat();
      } else {
        _currentState = ButtonState.idle;
        _loadingController.stop();
        _loadingController.reset();
      }
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _loadingController.dispose();
    _successController.dispose();
    super.dispose();
  }

  Future<void> _triggerHapticFeedback() async {
    if (!widget.enableHapticFeedback || !mounted) return;

    try {
      final intensity = _getHapticIntensity();
      switch (intensity) {
        case HapticIntensity.light:
          await HapticFeedback.lightImpact();
          break;
        case HapticIntensity.medium:
          await HapticFeedback.mediumImpact();
          break;
        case HapticIntensity.heavy:
          await HapticFeedback.heavyImpact();
          break;
      }
    } catch (e) {
      // Graceful degradation for platforms without haptic support
    }
  }

  HapticIntensity _getHapticIntensity() {
    switch (widget.type) {
      case ButtonType.primary:
        return HapticIntensity.medium;
      case ButtonType.secondary:
        return HapticIntensity.medium;
      case ButtonType.outlined:
        return HapticIntensity.light;
      case ButtonType.text:
        return HapticIntensity.light;
    }
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onPressed == null || widget.isLoading) return;

    setState(() {
      _isPressed = true;
      _currentState = ButtonState.pressed;
    });

    _scaleController.forward();
    _triggerHapticFeedback();
  }

  void _handleTapUp(TapUpDetails details) {
    if (!_isPressed) return;

    setState(() {
      _isPressed = false;
      _currentState = ButtonState.idle;
    });

    _scaleController.reverse();
  }

  void _handleTapCancel() {
    if (!_isPressed) return;

    setState(() {
      _isPressed = false;
      _currentState = ButtonState.idle;
    });

    _scaleController.reverse();
  }

  void _handleTap() {
    if (widget.onPressed == null || widget.isLoading) return;

    widget.onPressed!();

    // Trigger success animation and mascot integration
    if (widget.enableMascotIntegration) {
      _triggerSuccessState();
    }
  }

  void _triggerSuccessState() {
    setState(() {
      _currentState = ButtonState.success;
    });

    _successController.forward().then((_) {
      if (mounted) {
        _successController.reverse();
        setState(() {
          _currentState = ButtonState.idle;
        });
      }
    });

    // Trigger mascot celebration
    if (widget.enableMascotIntegration) {
      final mascotActions = ref.read(mascotActionsProvider);
      mascotActions.showDiscovery(
        discoveryType: 'Button Success',
        message: widget.successMessage ?? 'Action completed successfully!',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SizedBox(
      width: widget.isFullWidth ? double.infinity : widget.width,
      height: widget.height,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: GestureDetector(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              onTap: _handleTap,
              child: _buildButton(theme),
            ),
          );
        },
      ),
    );
  }

  Widget _buildButton(ThemeData theme) {
    final isDisabled = widget.onPressed == null;

    switch (widget.type) {
      case ButtonType.primary:
        return _buildElevatedButton(
          theme,
          AppTheme.primaryButtonStyle,
          Colors.white,
          isDisabled,
        );
      case ButtonType.secondary:
        return _buildElevatedButton(
          theme,
          AppTheme.secondaryButtonStyle,
          Colors.white,
          isDisabled,
        );
      case ButtonType.outlined:
        return _buildOutlinedButton(theme, isDisabled);
      case ButtonType.text:
        return _buildTextButton(theme, isDisabled);
    }
  }

  Widget _buildElevatedButton(
    ThemeData theme,
    ButtonStyle baseStyle,
    Color textColor,
    bool isDisabled,
  ) {
    return ElevatedButton(
      onPressed: isDisabled || widget.isLoading ? null : () {},
      style: baseStyle.copyWith(
        padding: widget.padding != null
            ? WidgetStateProperty.all(widget.padding)
            : null,
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
        ),
        overlayColor: WidgetStateProperty.all(
          _getRippleColor(theme).withAlpha(AppTheme.buttonHoverAlpha),
        ),
      ),
      child: _buildButtonContent(textColor),
    );
  }

  Widget _buildOutlinedButton(ThemeData theme, bool isDisabled) {
    return OutlinedButton(
      onPressed: isDisabled || widget.isLoading ? null : () {},
      style: AppTheme.outlinedButtonStyle.copyWith(
        padding: widget.padding != null
            ? WidgetStateProperty.all(widget.padding)
            : null,
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
        ),
        overlayColor: WidgetStateProperty.all(
          theme.colorScheme.primary.withAlpha(AppTheme.buttonHoverAlpha),
        ),
      ),
      child: _buildButtonContent(theme.colorScheme.primary),
    );
  }

  Widget _buildTextButton(ThemeData theme, bool isDisabled) {
    return TextButton(
      onPressed: isDisabled || widget.isLoading ? null : () {},
      style: AppTheme.textButtonStyle.copyWith(
        padding: widget.padding != null
            ? WidgetStateProperty.all(widget.padding)
            : null,
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
        ),
        overlayColor: WidgetStateProperty.all(
          theme.colorScheme.primary.withAlpha(AppTheme.buttonHoverAlpha),
        ),
      ),
      child: _buildButtonContent(theme.colorScheme.primary),
    );
  }

  Color _getRippleColor(ThemeData theme) {
    switch (widget.type) {
      case ButtonType.primary:
        return theme.colorScheme.onPrimary;
      case ButtonType.secondary:
        return theme.colorScheme.onSecondary;
      case ButtonType.outlined:
      case ButtonType.text:
        return theme.colorScheme.primary;
    }
  }

  Widget _buildButtonContent(Color textColor) {
    if (widget.isLoading || _currentState == ButtonState.loading) {
      return SizedBox(
        height: 24,
        width: 24,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    }

    if (widget.icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(widget.icon, color: textColor, size: 20),
          const SizedBox(width: 8),
          Text(
            widget.text,
            style: TextStyle(
              color: textColor,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    return Text(
      widget.text,
      style: TextStyle(
        color: textColor,
        fontSize: 16,
        fontWeight: FontWeight.w600,
      ),
    );
  }
}
