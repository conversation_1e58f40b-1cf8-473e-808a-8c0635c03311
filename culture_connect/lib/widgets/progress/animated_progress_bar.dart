// Flutter imports
import 'package:flutter/material.dart';

// Third-party imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/providers/mascot_provider.dart';

/// Enum for different progress bar types
enum ProgressBarType {
  /// Linear horizontal progress bar
  linear,

  /// Circular ring progress bar
  circular,

  /// Segmented progress with milestones
  segmented,
}

/// Animated progress bar widget with celebration integration
class AnimatedProgressBar extends ConsumerStatefulWidget {
  /// The current progress value (0.0 to 1.0)
  final double progress;

  /// The type of progress bar to display
  final ProgressBarType type;

  /// The height of the progress bar (for linear type)
  final double height;

  /// The size of the progress bar (for circular type)
  final double size;

  /// The color of the progress bar
  final Color? color;

  /// Whether to show percentage text
  final bool showPercentage;

  /// Whether to show celebration on completion
  final bool enableCelebration;

  /// The user achievement for progress tracking
  final UserAchievement? achievement;

  /// Callback when progress reaches a milestone
  final Function(double progress)? onMilestone;

  /// Callback when progress completes
  final VoidCallback? onComplete;

  /// Number of segments for segmented type
  final int segments;

  /// Creates a new animated progress bar
  const AnimatedProgressBar({
    super.key,
    required this.progress,
    this.type = ProgressBarType.linear,
    this.height = 8.0,
    this.size = 120.0,
    this.color,
    this.showPercentage = false,
    this.enableCelebration = true,
    this.achievement,
    this.onMilestone,
    this.onComplete,
    this.segments = 5,
  });

  @override
  ConsumerState<AnimatedProgressBar> createState() =>
      _AnimatedProgressBarState();
}

class _AnimatedProgressBarState extends ConsumerState<AnimatedProgressBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;

  bool _hasCompletedCelebration = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void didUpdateWidget(AnimatedProgressBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.progress != widget.progress) {
      _updateProgress(oldWidget.progress, widget.progress);
    }
  }

  void _updateProgress(double oldProgress, double newProgress) {
    _progressAnimation = Tween<double>(
      begin: oldProgress,
      end: newProgress,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.reset();
    _animationController.forward();

    // Check for milestone celebrations
    if (widget.enableCelebration) {
      _checkMilestones(oldProgress, newProgress);
    }
  }

  void _checkMilestones(double oldProgress, double newProgress) {
    // Check for 25%, 50%, 75%, and 100% milestones
    const milestones = [0.25, 0.5, 0.75, 1.0];

    for (final milestone in milestones) {
      if (oldProgress < milestone && newProgress >= milestone) {
        widget.onMilestone?.call(milestone);

        if (milestone == 1.0 && !_hasCompletedCelebration) {
          _triggerCompletionCelebration();
          _hasCompletedCelebration = true;
        } else {
          _triggerMilestoneCelebration(milestone);
        }
      }
    }
  }

  void _triggerMilestoneCelebration(double milestone) {
    if (!mounted) return;

    final mascotActions = ref.read(mascotActionsProvider);
    final percentageText = '${(milestone * 100).round()}%';

    mascotActions.showDiscovery(
      discoveryType: 'Progress Milestone',
      message: '$percentageText progress reached!',
    );
  }

  void _triggerCompletionCelebration() {
    if (!mounted) return;

    final mascotActions = ref.read(mascotActionsProvider);

    if (widget.achievement != null) {
      mascotActions.celebrateAchievement(
        achievementId: widget.achievement!.achievement.id,
        achievementTitle: widget.achievement!.achievement.title,
      );
    } else {
      mascotActions.showDiscovery(
        discoveryType: 'Progress Complete',
        message: 'Goal achieved!',
      );
    }

    widget.onComplete?.call();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = widget.color ??
        widget.achievement?.achievement.color ??
        theme.colorScheme.primary;

    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        switch (widget.type) {
          case ProgressBarType.linear:
            return _buildLinearProgress(theme, progressColor);
          case ProgressBarType.circular:
            return _buildCircularProgress(theme, progressColor);
          case ProgressBarType.segmented:
            return _buildSegmentedProgress(theme, progressColor);
        }
      },
    );
  }

  Widget _buildLinearProgress(ThemeData theme, Color progressColor) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.height / 2),
            color: theme.colorScheme.outline.withAlpha(51),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(widget.height / 2),
            child: LinearProgressIndicator(
              value: _progressAnimation.value,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
            ),
          ),
        ),
        if (widget.showPercentage) ...[
          const SizedBox(height: 8),
          Text(
            '${(_progressAnimation.value * 100).round()}%',
            style: theme.textTheme.bodySmall?.copyWith(
              color: progressColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCircularProgress(ThemeData theme, Color progressColor) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: widget.size,
            height: widget.size,
            child: CircularProgressIndicator(
              value: _progressAnimation.value,
              strokeWidth: 8.0,
              backgroundColor: theme.colorScheme.outline.withAlpha(51),
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
            ),
          ),
          if (widget.showPercentage)
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${(_progressAnimation.value * 100).round()}%',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: progressColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (widget.achievement != null)
                  Text(
                    'Progress',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(153),
                    ),
                  ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildSegmentedProgress(ThemeData theme, Color progressColor) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: List.generate(widget.segments, (index) {
            final segmentProgress = (index + 1) / widget.segments;
            final isCompleted = _progressAnimation.value >= segmentProgress;
            final isActive =
                _progressAnimation.value > index / widget.segments &&
                    _progressAnimation.value < segmentProgress;

            return Expanded(
              child: Container(
                margin: EdgeInsets.only(
                  right: index < widget.segments - 1 ? 4 : 0,
                ),
                height: widget.height,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(widget.height / 2),
                  color: isCompleted
                      ? progressColor
                      : isActive
                          ? progressColor.withAlpha(128)
                          : theme.colorScheme.outline.withAlpha(51),
                ),
              ),
            );
          }),
        ),
        if (widget.showPercentage) ...[
          const SizedBox(height: 8),
          Text(
            '${(_progressAnimation.value * 100).round()}% Complete',
            style: theme.textTheme.bodySmall?.copyWith(
              color: progressColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ],
    );
  }
}

/// Compact progress bar for smaller spaces
class CompactProgressBar extends StatelessWidget {
  /// The progress value (0.0 to 1.0)
  final double progress;

  /// The color of the progress bar
  final Color? color;

  /// The height of the progress bar
  final double height;

  /// Creates a new compact progress bar
  const CompactProgressBar({
    super.key,
    required this.progress,
    this.color,
    this.height = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedProgressBar(
      progress: progress,
      type: ProgressBarType.linear,
      height: height,
      color: color,
      showPercentage: false,
      enableCelebration: false,
    );
  }
}

/// Achievement progress bar with integrated celebration
class AchievementProgressBar extends StatelessWidget {
  /// The user achievement to display progress for
  final UserAchievement achievement;

  /// The type of progress bar
  final ProgressBarType type;

  /// Whether to show percentage
  final bool showPercentage;

  /// Creates a new achievement progress bar
  const AchievementProgressBar({
    super.key,
    required this.achievement,
    this.type = ProgressBarType.linear,
    this.showPercentage = true,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedProgressBar(
      progress: achievement.progress,
      type: type,
      color: achievement.achievement.color,
      showPercentage: showPercentage,
      enableCelebration: true,
      achievement: achievement,
    );
  }
}
