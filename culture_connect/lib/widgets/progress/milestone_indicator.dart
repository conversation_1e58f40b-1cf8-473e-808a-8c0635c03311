// Flutter imports
import 'package:flutter/material.dart';

// Third-party imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/achievement/achievement.dart';
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/providers/mascot_provider.dart';
import 'package:culture_connect/widgets/progress/animated_progress_bar.dart';

/// Milestone indicator widget with achievement integration
class MilestoneIndicator extends ConsumerStatefulWidget {
  /// The user achievement to display milestones for
  final UserAchievement achievement;

  /// Whether to show milestone details
  final bool showDetails;

  /// Whether to enable tap interactions
  final bool enableTap;

  /// Callback when milestone is tapped
  final Function(UserAchievement)? onTap;

  /// Creates a new milestone indicator
  const MilestoneIndicator({
    super.key,
    required this.achievement,
    this.showDetails = true,
    this.enableTap = true,
    this.onTap,
  });

  @override
  ConsumerState<MilestoneIndicator> createState() => _MilestoneIndicatorState();
}

class _MilestoneIndicatorState extends ConsumerState<MilestoneIndicator>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Start pulsing if achievement is close to completion
    if (widget.achievement.progress > 0.8 && !widget.achievement.isUnlocked) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(MilestoneIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.achievement.progress != widget.achievement.progress) {
      _handleProgressUpdate();
    }
  }

  void _handleProgressUpdate() {
    if (widget.achievement.progress > 0.8 && !widget.achievement.isUnlocked) {
      if (!_pulseController.isAnimating) {
        _pulseController.repeat(reverse: true);
      }
    } else {
      _pulseController.stop();
      _pulseController.reset();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _handleTap() {
    if (!widget.enableTap) return;

    widget.onTap?.call(widget.achievement);

    // Trigger mascot interaction
    final mascotActions = ref.read(mascotActionsProvider);
    mascotActions.showOnboardingHelp(
      step: 'Achievement Details',
      message: 'Viewing ${widget.achievement.achievement.title}',
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final achievement = widget.achievement.achievement;

    Widget content = Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: widget.achievement.isUnlocked
              ? achievement.color
              : theme.colorScheme.outline.withAlpha(77),
          width: widget.achievement.isUnlocked ? 2 : 1,
        ),
        color: widget.achievement.isUnlocked
            ? achievement.color.withAlpha(26)
            : theme.colorScheme.surface,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme, achievement),
          const SizedBox(height: 12),
          _buildProgressSection(theme),
          if (widget.showDetails) ...[
            const SizedBox(height: 12),
            _buildDetailsSection(theme, achievement),
          ],
        ],
      ),
    );

    // Apply pulse animation if close to completion
    if (widget.achievement.progress > 0.8 && !widget.achievement.isUnlocked) {
      content = AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: content,
          );
        },
      );
    }

    if (widget.enableTap) {
      content = GestureDetector(
        onTap: _handleTap,
        child: content,
      );
    }

    return content;
  }

  Widget _buildHeader(ThemeData theme, Achievement achievement) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: widget.achievement.isUnlocked
                ? achievement.color
                : theme.colorScheme.outline.withAlpha(77),
          ),
          child: Icon(
            achievement.icon,
            color: widget.achievement.isUnlocked
                ? Colors.white
                : theme.colorScheme.onSurface.withAlpha(153),
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                achievement.title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: widget.achievement.isUnlocked
                      ? achievement.color
                      : theme.colorScheme.onSurface,
                ),
              ),
              Text(
                achievement.description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(153),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        if (widget.achievement.isUnlocked)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: achievement.color,
            ),
            child: Text(
              'Unlocked',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildProgressSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${widget.achievement.currentPoints}/${widget.achievement.achievement.pointsRequired}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(153),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        AchievementProgressBar(
          achievement: widget.achievement,
          type: ProgressBarType.linear,
          showPercentage: true,
        ),
      ],
    );
  }

  Widget _buildDetailsSection(ThemeData theme, Achievement achievement) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.emoji_events,
              size: 16,
              color: achievement.color,
            ),
            const SizedBox(width: 4),
            Text(
              '${achievement.rewardValue} points',
              style: theme.textTheme.bodySmall?.copyWith(
                color: achievement.color,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color:
                    _getDifficultyColor(achievement.difficulty).withAlpha(26),
              ),
              child: Text(
                achievement.difficulty.toString().split('.').last.toUpperCase(),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: _getDifficultyColor(achievement.difficulty),
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Color _getDifficultyColor(AchievementDifficulty difficulty) {
    switch (difficulty) {
      case AchievementDifficulty.bronze:
        return const Color(0xFFCD7F32);
      case AchievementDifficulty.silver:
        return const Color(0xFFC0C0C0);
      case AchievementDifficulty.gold:
        return const Color(0xFFFFD700);
      case AchievementDifficulty.platinum:
        return const Color(0xFFE5E4E2);
      case AchievementDifficulty.diamond:
        return const Color(0xFFB9F2FF);
    }
  }
}

/// Level progression indicator with XP-style visualization
class LevelProgressIndicator extends ConsumerWidget {
  /// The current user level
  final int currentLevel;

  /// The current XP points
  final int currentXP;

  /// The XP required for next level
  final int nextLevelXP;

  /// Whether to show level details
  final bool showDetails;

  /// Creates a new level progress indicator
  const LevelProgressIndicator({
    super.key,
    required this.currentLevel,
    required this.currentXP,
    required this.nextLevelXP,
    this.showDetails = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final progress = nextLevelXP > 0 ? currentXP / nextLevelXP : 1.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withAlpha(26),
            theme.colorScheme.secondary.withAlpha(26),
          ],
        ),
        border: Border.all(
          color: theme.colorScheme.primary.withAlpha(77),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.secondary,
                    ],
                  ),
                ),
                child: Center(
                  child: Text(
                    '$currentLevel',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Level $currentLevel',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (showDetails)
                      Text(
                        'Travel Explorer',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(153),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          if (showDetails) ...[
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'XP Progress',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '$currentXP / $nextLevelXP XP',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(153),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            AnimatedProgressBar(
              progress: progress,
              type: ProgressBarType.linear,
              height: 8,
              showPercentage: false,
              enableCelebration: true,
            ),
          ],
        ],
      ),
    );
  }
}

/// Streak counter with fire animation
class StreakCounter extends ConsumerStatefulWidget {
  /// The current streak count
  final int streakCount;

  /// The type of streak (e.g., "Booking", "Achievement")
  final String streakType;

  /// Whether to show fire animation
  final bool showFireAnimation;

  /// Creates a new streak counter
  const StreakCounter({
    super.key,
    required this.streakCount,
    required this.streakType,
    this.showFireAnimation = true,
  });

  @override
  ConsumerState<StreakCounter> createState() => _StreakCounterState();
}

class _StreakCounterState extends ConsumerState<StreakCounter>
    with SingleTickerProviderStateMixin {
  late AnimationController _fireController;
  late Animation<double> _fireAnimation;

  @override
  void initState() {
    super.initState();
    _initializeFireAnimation();
  }

  void _initializeFireAnimation() {
    _fireController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fireAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _fireController,
      curve: Curves.easeInOut,
    ));

    if (widget.showFireAnimation && widget.streakCount > 0) {
      _fireController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(StreakCounter oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.streakCount != widget.streakCount) {
      if (widget.showFireAnimation && widget.streakCount > 0) {
        _fireController.repeat(reverse: true);
      } else {
        _fireController.stop();
      }
    }
  }

  @override
  void dispose() {
    _fireController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: widget.streakCount > 0
            ? Colors.orange.withAlpha(26)
            : theme.colorScheme.outline.withAlpha(26),
        border: Border.all(
          color: widget.streakCount > 0
              ? Colors.orange
              : theme.colorScheme.outline.withAlpha(77),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.streakCount > 0 && widget.showFireAnimation)
            AnimatedBuilder(
              animation: _fireAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _fireAnimation.value,
                  child: const Icon(
                    Icons.local_fire_department,
                    color: Colors.orange,
                    size: 20,
                  ),
                );
              },
            )
          else
            Icon(
              Icons.local_fire_department,
              color: widget.streakCount > 0
                  ? Colors.orange
                  : theme.colorScheme.outline,
              size: 20,
            ),
          const SizedBox(width: 4),
          Text(
            '${widget.streakCount} ${widget.streakType}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: widget.streakCount > 0
                  ? Colors.orange
                  : theme.colorScheme.onSurface.withAlpha(153),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
