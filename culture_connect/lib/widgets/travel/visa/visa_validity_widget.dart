// Flutter imports
import 'dart:async';
import 'package:flutter/material.dart';

// Project imports
import 'package:culture_connect/services/travel/visa/visa_tracking_service.dart';

/// Widget for displaying visa status indicators and countdown timers
class VisaValidityWidget extends StatefulWidget {
  /// Creates a new visa validity widget
  const VisaValidityWidget({
    super.key,
    required this.trackedVisa,
    this.showCountdown = true,
    this.showStatusIcon = true,
    this.compact = false,
    this.onTap,
  });

  /// The tracked visa to display
  final TrackedVisa trackedVisa;

  /// Whether to show countdown timer
  final bool showCountdown;

  /// Whether to show status icon
  final bool showStatusIcon;

  /// Whether to use compact layout
  final bool compact;

  /// Callback when widget is tapped
  final VoidCallback? onTap;

  @override
  State<VisaValidityWidget> createState() => _VisaValidityWidgetState();
}

class _VisaValidityWidgetState extends State<VisaValidityWidget> {
  Timer? _countdownTimer;
  Duration _timeRemaining = Duration.zero;

  @override
  void initState() {
    super.initState();
    _updateTimeRemaining();
    _startCountdownTimer();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _updateTimeRemaining() {
    final now = DateTime.now();
    final visa = widget.trackedVisa;

    // Calculate time remaining based on the most restrictive constraint
    final visaExpiry = visa.expiryDate;
    final maxStayDate = visa.entryDate.add(Duration(days: visa.maxStayDays));

    final timeUntilVisaExpiry = visaExpiry.difference(now);
    final timeUntilMaxStay = maxStayDate.difference(now);

    // Use the shorter duration
    _timeRemaining =
        timeUntilVisaExpiry.inMilliseconds < timeUntilMaxStay.inMilliseconds
            ? timeUntilVisaExpiry
            : timeUntilMaxStay;

    // Ensure non-negative
    if (_timeRemaining.isNegative) {
      _timeRemaining = Duration.zero;
    }
  }

  void _startCountdownTimer() {
    if (!widget.showCountdown) return;

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _updateTimeRemaining();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final visa = widget.trackedVisa;
    final statusColor =
        Color(int.parse('0xFF${visa.status.colorHex.substring(1)}'));

    if (widget.compact) {
      return _buildCompactWidget(theme, statusColor);
    } else {
      return _buildFullWidget(theme, statusColor);
    }
  }

  Widget _buildCompactWidget(ThemeData theme, Color statusColor) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: statusColor.withAlpha(26),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: statusColor.withAlpha(77)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.showStatusIcon) ...[
              Icon(
                _getStatusIcon(widget.trackedVisa.status),
                size: 14,
                color: statusColor,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              widget.trackedVisa.status.displayName,
              style: theme.textTheme.labelSmall?.copyWith(
                color: statusColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (widget.showCountdown && !_timeRemaining.isNegative) ...[
              const SizedBox(width: 4),
              Text(
                _formatTimeRemaining(),
                style: theme.textTheme.labelSmall?.copyWith(
                  color: statusColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFullWidget(ThemeData theme, Color statusColor) {
    final visa = widget.trackedVisa;

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: statusColor.withAlpha(26),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: statusColor.withAlpha(77)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status
            Row(
              children: [
                if (widget.showStatusIcon) ...[
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      _getStatusIcon(visa.status),
                      size: 16,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        visa.country,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        visa.status.displayName,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: statusColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.showCountdown)
                  _buildCountdownDisplay(theme, statusColor),
              ],
            ),

            const SizedBox(height: 12),

            // Progress bar
            _buildProgressBar(theme, statusColor),

            const SizedBox(height: 8),

            // Details
            _buildDetails(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildCountdownDisplay(ThemeData theme, Color statusColor) {
    if (_timeRemaining.isNegative) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          'EXPIRED',
          style: theme.textTheme.labelSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          _formatTimeRemaining(),
          style: theme.textTheme.titleMedium?.copyWith(
            color: statusColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          'remaining',
          style: theme.textTheme.bodySmall?.copyWith(
            color: statusColor,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar(ThemeData theme, Color statusColor) {
    final visa = widget.trackedVisa;
    final totalDays = visa.maxStayDays;
    final daysPassed = visa.daysSinceEntry;
    final progress = (daysPassed / totalDays).clamp(0.0, 1.0);

    return Column(
      children: [
        LinearProgressIndicator(
          value: progress,
          backgroundColor: theme.colorScheme.surfaceContainerHighest,
          valueColor: AlwaysStoppedAnimation<Color>(statusColor),
          minHeight: 6,
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Day ${daysPassed + 1} of $totalDays',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
            Text(
              '${((1 - progress) * 100).toInt()}% remaining',
              style: theme.textTheme.bodySmall?.copyWith(
                color: statusColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetails(ThemeData theme) {
    final visa = widget.trackedVisa;

    return Row(
      children: [
        Expanded(
          child: _buildDetailItem(
            'Entry',
            _formatDate(visa.entryDate),
            Icons.flight_land,
            theme,
          ),
        ),
        Expanded(
          child: _buildDetailItem(
            'Expiry',
            _formatDate(visa.expiryDate),
            Icons.event,
            theme,
          ),
        ),
        Expanded(
          child: _buildDetailItem(
            'Type',
            visa.visaType,
            Icons.card_membership,
            theme,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailItem(
      String label, String value, IconData icon, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 12,
              color: theme.colorScheme.outline,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  IconData _getStatusIcon(VisaTrackingStatus status) {
    switch (status) {
      case VisaTrackingStatus.active:
        return Icons.check_circle;
      case VisaTrackingStatus.expiringSoon:
        return Icons.warning;
      case VisaTrackingStatus.expired:
        return Icons.error;
      case VisaTrackingStatus.overstay:
        return Icons.dangerous;
    }
  }

  String _formatTimeRemaining() {
    if (_timeRemaining.isNegative) return 'Expired';

    final days = _timeRemaining.inDays;
    final hours = _timeRemaining.inHours % 24;
    final minutes = _timeRemaining.inMinutes % 60;

    if (days > 0) {
      return '${days}d ${hours}h';
    } else if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Compact visa status badge widget
class VisaStatusBadge extends StatelessWidget {
  /// Creates a new visa status badge
  const VisaStatusBadge({
    super.key,
    required this.status,
    this.size = 16,
  });

  /// The visa tracking status
  final VisaTrackingStatus status;

  /// Size of the badge
  final double size;

  @override
  Widget build(BuildContext context) {
    final statusColor = Color(int.parse('0xFF${status.colorHex.substring(1)}'));

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: statusColor,
        shape: BoxShape.circle,
      ),
      child: Icon(
        _getStatusIcon(status),
        size: size * 0.6,
        color: Colors.white,
      ),
    );
  }

  IconData _getStatusIcon(VisaTrackingStatus status) {
    switch (status) {
      case VisaTrackingStatus.active:
        return Icons.check;
      case VisaTrackingStatus.expiringSoon:
        return Icons.warning;
      case VisaTrackingStatus.expired:
        return Icons.close;
      case VisaTrackingStatus.overstay:
        return Icons.dangerous;
    }
  }
}

/// Animated countdown timer widget
class AnimatedCountdownTimer extends StatefulWidget {
  /// Creates a new animated countdown timer
  const AnimatedCountdownTimer({
    super.key,
    required this.targetDate,
    this.style,
    this.onExpired,
  });

  /// Target date to count down to
  final DateTime targetDate;

  /// Text style for the countdown
  final TextStyle? style;

  /// Callback when countdown expires
  final VoidCallback? onExpired;

  @override
  State<AnimatedCountdownTimer> createState() => _AnimatedCountdownTimerState();
}

class _AnimatedCountdownTimerState extends State<AnimatedCountdownTimer>
    with SingleTickerProviderStateMixin {
  Timer? _timer;
  Duration _timeRemaining = Duration.zero;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _updateTimeRemaining();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _updateTimeRemaining() {
    final now = DateTime.now();
    _timeRemaining = widget.targetDate.difference(now);

    if (_timeRemaining.isNegative) {
      _timeRemaining = Duration.zero;
      widget.onExpired?.call();
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _updateTimeRemaining();

          // Animate when time is running low
          if (_timeRemaining.inHours < 24 &&
              _timeRemaining.inSeconds % 2 == 0) {
            _animationController.forward().then((_) {
              _animationController.reverse();
            });
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Text(
            _formatDuration(_timeRemaining),
            style: widget.style,
          ),
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    if (duration.isNegative) return '00:00:00';

    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    return '${hours.toString().padLeft(2, '0')}:'
        '${minutes.toString().padLeft(2, '0')}:'
        '${seconds.toString().padLeft(2, '0')}';
  }
}
