// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/services/travel/visa/travel_buddy_api_service.dart';

/// Widget for selecting countries with autocomplete and flag display
class CountrySelectorWidget extends ConsumerStatefulWidget {
  /// Creates a new country selector widget
  const CountrySelectorWidget({
    super.key,
    required this.label,
    required this.onCountrySelected,
    this.selectedCountry,
    this.icon,
  });

  /// Label for the selector
  final String label;

  /// Callback when a country is selected
  final ValueChanged<String> onCountrySelected;

  /// Currently selected country
  final String? selectedCountry;

  /// Icon to display
  final IconData? icon;

  @override
  ConsumerState<CountrySelectorWidget> createState() => _CountrySelectorWidgetState();
}

class _CountrySelectorWidgetState extends ConsumerState<CountrySelectorWidget> {
  // State variables
  final TextEditingController _controller = TextEditingController();
  List<Country> _countries = [];
  List<Country> _filteredCountries = [];
  bool _isLoading = false;
  bool _showDropdown = false;
  
  // Services
  final TravelBuddyApiService _travelBuddyService = TravelBuddyApiService();

  @override
  void initState() {
    super.initState();
    _loadCountries();
    
    // Set initial value if provided
    if (widget.selectedCountry != null) {
      _controller.text = widget.selectedCountry!;
    }
  }

  @override
  void didUpdateWidget(CountrySelectorWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update controller text if selected country changes
    if (widget.selectedCountry != oldWidget.selectedCountry) {
      _controller.text = widget.selectedCountry ?? '';
    }
  }

  Future<void> _loadCountries() async {
    setState(() => _isLoading = true);
    
    try {
      final countries = await _travelBuddyService.getSupportedCountries();
      if (mounted) {
        setState(() {
          _countries = countries;
          _filteredCountries = countries;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _filterCountries(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCountries = _countries;
      } else {
        _filteredCountries = _countries
            .where((country) =>
                country.name.toLowerCase().contains(query.toLowerCase()) ||
                country.code.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
      _showDropdown = query.isNotEmpty && _filteredCountries.isNotEmpty;
    });
  }

  void _selectCountry(Country country) {
    setState(() {
      _controller.text = country.name;
      _showDropdown = false;
    });
    widget.onCountrySelected(country.code);
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Stack(
          children: [
            _buildTextField(theme),
            if (_showDropdown) _buildDropdown(theme),
          ],
        ),
      ],
    );
  }

  Widget _buildTextField(ThemeData theme) {
    return TextFormField(
      controller: _controller,
      decoration: InputDecoration(
        hintText: 'Search for a country...',
        prefixIcon: widget.icon != null
            ? Icon(widget.icon, color: theme.colorScheme.primary)
            : const Icon(Icons.public),
        suffixIcon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            : _controller.text.isNotEmpty
                ? IconButton(
                    onPressed: () {
                      _controller.clear();
                      _filterCountries('');
                      widget.onCountrySelected('');
                    },
                    icon: const Icon(Icons.clear),
                  )
                : const Icon(Icons.arrow_drop_down),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withAlpha(77),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.primary,
            width: 2,
          ),
        ),
      ),
      onChanged: _filterCountries,
      onTap: () {
        if (_countries.isNotEmpty) {
          setState(() {
            _filteredCountries = _countries;
            _showDropdown = true;
          });
        }
      },
    );
  }

  Widget _buildDropdown(ThemeData theme) {
    return Positioned(
      top: 60,
      left: 0,
      right: 0,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          constraints: const BoxConstraints(maxHeight: 200),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.outline.withAlpha(77),
            ),
          ),
          child: ListView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            itemCount: _filteredCountries.length,
            itemBuilder: (context, index) {
              final country = _filteredCountries[index];
              return _buildCountryItem(theme, country);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildCountryItem(ThemeData theme, Country country) {
    return InkWell(
      onTap: () => _selectCountry(country),
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Flag emoji or placeholder
            Container(
              width: 32,
              height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(77),
                ),
              ),
              child: Center(
                child: Text(
                  country.flagUrl.isNotEmpty ? country.flagUrl : '🏳️',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    country.name,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    country.code,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(153),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _travelBuddyService.dispose();
    super.dispose();
  }
}
