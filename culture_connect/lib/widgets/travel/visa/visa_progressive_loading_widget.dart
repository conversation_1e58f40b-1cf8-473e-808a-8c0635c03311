// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:shimmer/shimmer.dart';

/// Widget for progressive loading with skeleton screens for visa features
class VisaProgressiveLoadingWidget extends StatefulWidget {
  /// Creates a new visa progressive loading widget
  const VisaProgressiveLoadingWidget({
    super.key,
    required this.loadingType,
    this.itemCount = 3,
    this.showProgress = false,
    this.progressValue,
    this.loadingMessage,
  });

  /// Type of loading to display
  final VisaLoadingType loadingType;

  /// Number of skeleton items to show
  final int itemCount;

  /// Whether to show progress indicator
  final bool showProgress;

  /// Progress value (0.0 to 1.0)
  final double? progressValue;

  /// Loading message to display
  final String? loadingMessage;

  @override
  State<VisaProgressiveLoadingWidget> createState() =>
      _VisaProgressiveLoadingWidgetState();
}

class _VisaProgressiveLoadingWidgetState
    extends State<VisaProgressiveLoadingWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          if (widget.showProgress) _buildProgressSection(theme),
          Expanded(
            child: _buildSkeletonContent(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (widget.loadingMessage != null) ...[
            Text(
              widget.loadingMessage!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.outline,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
          ],
          if (widget.progressValue != null)
            LinearProgressIndicator(
              value: widget.progressValue,
              backgroundColor: theme.colorScheme.surfaceContainerHighest,
              valueColor:
                  AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
            )
          else
            LinearProgressIndicator(
              backgroundColor: theme.colorScheme.surfaceContainerHighest,
              valueColor:
                  AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
            ),
        ],
      ),
    );
  }

  Widget _buildSkeletonContent(ThemeData theme) {
    switch (widget.loadingType) {
      case VisaLoadingType.requirements:
        return _buildRequirementsSkeleton(theme);
      case VisaLoadingType.providers:
        return _buildProvidersSkeleton(theme);
      case VisaLoadingType.documents:
        return _buildDocumentsSkeleton(theme);
      case VisaLoadingType.tracking:
        return _buildTrackingSkeleton(theme);
      case VisaLoadingType.notifications:
        return _buildNotificationsSkeleton(theme);
    }
  }

  Widget _buildRequirementsSkeleton(ThemeData theme) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSkeletonCard(
          theme,
          height: 120,
          children: [
            _buildSkeletonLine(theme, width: 0.7),
            const SizedBox(height: 8),
            _buildSkeletonLine(theme, width: 0.9),
            const SizedBox(height: 8),
            _buildSkeletonLine(theme, width: 0.6),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSkeletonChip(theme),
                const SizedBox(width: 8),
                _buildSkeletonChip(theme),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildSkeletonCard(
          theme,
          height: 200,
          children: [
            _buildSkeletonLine(theme, width: 0.5),
            const SizedBox(height: 16),
            ...List.generate(
                4,
                (index) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          _buildSkeletonCircle(theme, size: 16),
                          const SizedBox(width: 8),
                          _buildSkeletonLine(theme, width: 0.8),
                        ],
                      ),
                    )),
          ],
        ),
      ],
    );
  }

  Widget _buildProvidersSkeleton(ThemeData theme) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.itemCount,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildSkeletonCard(
            theme,
            height: 140,
            children: [
              Row(
                children: [
                  _buildSkeletonCircle(theme, size: 48),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSkeletonLine(theme, width: 0.6),
                        const SizedBox(height: 8),
                        _buildSkeletonLine(theme, width: 0.4),
                      ],
                    ),
                  ),
                  _buildSkeletonChip(theme),
                ],
              ),
              const SizedBox(height: 16),
              _buildSkeletonLine(theme, width: 0.9),
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildSkeletonChip(theme),
                  const SizedBox(width: 8),
                  _buildSkeletonChip(theme),
                  const SizedBox(width: 8),
                  _buildSkeletonChip(theme),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDocumentsSkeleton(ThemeData theme) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSkeletonCard(
          theme,
          height: 80,
          children: [
            Row(
              children: [
                _buildSkeletonCircle(theme, size: 32),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSkeletonLine(theme, width: 0.5),
                      const SizedBox(height: 8),
                      _buildSkeletonLine(theme, width: 0.7),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...List.generate(
            widget.itemCount,
            (index) => Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: _buildSkeletonCard(
                    theme,
                    height: 100,
                    children: [
                      Row(
                        children: [
                          _buildSkeletonRectangle(theme, width: 40, height: 40),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildSkeletonLine(theme, width: 0.6),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    _buildSkeletonChip(theme),
                                    const SizedBox(width: 8),
                                    _buildSkeletonLine(theme, width: 0.3),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      _buildSkeletonLine(theme, width: 0.4, height: 4),
                    ],
                  ),
                )),
      ],
    );
  }

  Widget _buildTrackingSkeleton(ThemeData theme) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.itemCount,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildSkeletonCard(
            theme,
            height: 180,
            children: [
              Row(
                children: [
                  _buildSkeletonRectangle(theme, width: 40, height: 40),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSkeletonLine(theme, width: 0.5),
                        const SizedBox(height: 8),
                        _buildSkeletonChip(theme),
                      ],
                    ),
                  ),
                  _buildSkeletonRectangle(theme, width: 60, height: 40),
                ],
              ),
              const SizedBox(height: 16),
              _buildSkeletonLine(theme, width: 0.9, height: 8),
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildSkeletonLine(theme, width: 0.3),
                  const Spacer(),
                  _buildSkeletonLine(theme, width: 0.2),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(child: _buildSkeletonButton(theme)),
                  const SizedBox(width: 12),
                  Expanded(child: _buildSkeletonButton(theme)),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNotificationsSkeleton(ThemeData theme) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSkeletonCard(
          theme,
          height: 100,
          children: [
            _buildSkeletonLine(theme, width: 0.4),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSkeletonRectangle(theme, width: 24, height: 24),
                const SizedBox(width: 12),
                Expanded(child: _buildSkeletonLine(theme, width: 0.7)),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...List.generate(
            4,
            (index) => Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: _buildSkeletonCard(
                    theme,
                    height: 60,
                    children: [
                      Row(
                        children: [
                          _buildSkeletonCircle(theme, size: 20),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildSkeletonLine(theme, width: 0.6),
                                const SizedBox(height: 4),
                                _buildSkeletonLine(theme, width: 0.4),
                              ],
                            ),
                          ),
                          _buildSkeletonLine(theme, width: 0.2),
                        ],
                      ),
                    ],
                  ),
                )),
      ],
    );
  }

  Widget _buildSkeletonCard(ThemeData theme,
      {required double height, required List<Widget> children}) {
    return Shimmer.fromColors(
      baseColor: theme.colorScheme.surfaceContainerHighest,
      highlightColor: theme.colorScheme.surface,
      child: Card(
        child: Container(
          height: height,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
      ),
    );
  }

  Widget _buildSkeletonLine(ThemeData theme,
      {required double width, double height = 16}) {
    return Shimmer.fromColors(
      baseColor: theme.colorScheme.surfaceContainerHighest,
      highlightColor: theme.colorScheme.surface,
      child: Container(
        width: MediaQuery.of(context).size.width * width,
        height: height,
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }

  Widget _buildSkeletonCircle(ThemeData theme, {required double size}) {
    return Shimmer.fromColors(
      baseColor: theme.colorScheme.surfaceContainerHighest,
      highlightColor: theme.colorScheme.surface,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Widget _buildSkeletonRectangle(ThemeData theme,
      {required double width, required double height}) {
    return Shimmer.fromColors(
      baseColor: theme.colorScheme.surfaceContainerHighest,
      highlightColor: theme.colorScheme.surface,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Widget _buildSkeletonChip(ThemeData theme) {
    return Shimmer.fromColors(
      baseColor: theme.colorScheme.surfaceContainerHighest,
      highlightColor: theme.colorScheme.surface,
      child: Container(
        width: 60,
        height: 24,
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildSkeletonButton(ThemeData theme) {
    return Shimmer.fromColors(
      baseColor: theme.colorScheme.surfaceContainerHighest,
      highlightColor: theme.colorScheme.surface,
      child: Container(
        height: 36,
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

/// Enum for different types of visa loading screens
enum VisaLoadingType {
  /// Loading visa requirements
  requirements,

  /// Loading visa service providers
  providers,

  /// Loading documents
  documents,

  /// Loading visa tracking
  tracking,

  /// Loading notification preferences
  notifications,
}

/// Success animation widget for visa operations
class VisaSuccessAnimationWidget extends StatefulWidget {
  /// Creates a new visa success animation widget
  const VisaSuccessAnimationWidget({
    super.key,
    required this.message,
    this.onComplete,
    this.duration = const Duration(seconds: 2),
  });

  /// Success message to display
  final String message;

  /// Callback when animation completes
  final VoidCallback? onComplete;

  /// Animation duration
  final Duration duration;

  @override
  State<VisaSuccessAnimationWidget> createState() =>
      _VisaSuccessAnimationWidgetState();
}

class _VisaSuccessAnimationWidgetState extends State<VisaSuccessAnimationWidget>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _startAnimation();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _startAnimation() async {
    await _scaleController.forward();
    await _fadeController.forward();

    await Future.delayed(widget.duration);

    if (mounted) {
      widget.onComplete?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 40,
              ),
            ),
          ),
          const SizedBox(height: 24),
          FadeTransition(
            opacity: _fadeAnimation,
            child: Text(
              widget.message,
              style: theme.textTheme.titleMedium?.copyWith(
                color: Colors.green,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
