// Flutter imports
import 'package:flutter/foundation.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/notification_service.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// Provider for the AchievementService
final achievementServiceProvider = Provider<AchievementService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final analyticsService = ref.watch(analyticsServiceProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final notificationService = ref.watch(notificationServiceProvider);

  return AchievementService(
    prefs: prefs,
    analyticsService: analyticsService,
    loggingService: loggingService,
    notificationService: notificationService,
  );
});

/// Provider for user achievements
final userAchievementsProvider =
    FutureProvider<List<UserAchievement>>((ref) async {
  final achievementService = ref.watch(achievementServiceProvider);
  await achievementService.initialize();
  return achievementService.getUserAchievements();
});

/// Provider for unlocked achievements
final unlockedAchievementsProvider = Provider<List<UserAchievement>>((ref) {
  final achievementsAsync = ref.watch(userAchievementsProvider);
  return achievementsAsync.when(
    data: (achievements) => achievements.where((a) => a.isUnlocked).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for achievements needing celebration
final achievementsNeedingCelebrationProvider =
    Provider<List<UserAchievement>>((ref) {
  final achievementsAsync = ref.watch(userAchievementsProvider);
  return achievementsAsync.when(
    data: (achievements) =>
        achievements.where((a) => a.needsCelebration).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for achievement statistics
final achievementStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final achievementService = ref.watch(achievementServiceProvider);
  return achievementService.getAchievementStats();
});

/// Notifier for tracking user actions and managing achievement state
class AchievementNotifier
    extends StateNotifier<AsyncValue<List<UserAchievement>>> {
  final AchievementService _achievementService;
  final Ref _ref;

  AchievementNotifier(this._achievementService, this._ref)
      : super(const AsyncValue.loading()) {
    _loadAchievements();
  }

  /// Load user achievements
  Future<void> _loadAchievements() async {
    try {
      state = const AsyncValue.loading();
      await _achievementService.initialize();
      final achievements = _achievementService.getUserAchievements();
      state = AsyncValue.data(achievements);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Track a user action and check for achievement progress
  Future<List<UserAchievement>> trackUserAction(
    UserAction action, {
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final unlockedAchievements = await _achievementService.trackUserAction(
        action,
        metadata: metadata,
      );

      // Refresh the achievements list
      await _loadAchievements();

      return unlockedAchievements;
    } catch (error, stackTrace) {
      // Log error but don't throw to avoid breaking the user flow
      debugPrint('Failed to track user action: $error');
      return [];
    }
  }

  /// Mark an achievement as celebrated
  Future<void> markAchievementCelebrated(String achievementId) async {
    try {
      await _achievementService.markAchievementCelebrated(achievementId);
      await _loadAchievements();
    } catch (error, stackTrace) {
      debugPrint('Failed to mark achievement as celebrated: $error');
    }
  }

  /// Refresh achievements data
  Future<void> refresh() async {
    await _loadAchievements();
  }
}

/// Provider for the achievement notifier
final achievementNotifierProvider = StateNotifierProvider<AchievementNotifier,
    AsyncValue<List<UserAchievement>>>((ref) {
  final achievementService = ref.watch(achievementServiceProvider);
  return AchievementNotifier(achievementService, ref);
});

/// Helper provider for tracking specific travel service actions
final travelServiceActionTrackerProvider = Provider((ref) {
  return TravelServiceActionTracker(ref);
});

/// Helper class for tracking travel service specific actions
class TravelServiceActionTracker {
  final Ref _ref;

  TravelServiceActionTracker(this._ref);

  /// Track insurance purchase completion
  Future<List<UserAchievement>> trackInsurancePurchase({
    String? policyId,
    double? amount,
    String? provider,
  }) async {
    final notifier = _ref.read(achievementNotifierProvider.notifier);
    return await notifier.trackUserAction(
      UserAction.insurancePurchase,
      metadata: {
        'policyId': policyId,
        'amount': amount,
        'provider': provider,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track visa application submission
  Future<List<UserAchievement>> trackVisaApplication({
    String? country,
    String? visaType,
    String? applicationId,
  }) async {
    final notifier = _ref.read(achievementNotifierProvider.notifier);
    return await notifier.trackUserAction(
      UserAction.visaApplication,
      metadata: {
        'country': country,
        'visaType': visaType,
        'applicationId': applicationId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track flight booking completion
  Future<List<UserAchievement>> trackFlightBooking({
    String? flightNumber,
    String? destination,
    double? amount,
  }) async {
    final notifier = _ref.read(achievementNotifierProvider.notifier);
    return await notifier.trackUserAction(
      UserAction.flightBooking,
      metadata: {
        'flightNumber': flightNumber,
        'destination': destination,
        'amount': amount,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track transfer booking completion
  Future<List<UserAchievement>> trackTransferBooking({
    String? transferId,
    String? fromLocation,
    String? toLocation,
    double? amount,
  }) async {
    final notifier = _ref.read(achievementNotifierProvider.notifier);
    return await notifier.trackUserAction(
      UserAction.transferBooking,
      metadata: {
        'transferId': transferId,
        'fromLocation': fromLocation,
        'toLocation': toLocation,
        'amount': amount,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track hotel booking completion
  Future<List<UserAchievement>> trackHotelBooking({
    String? hotelId,
    String? location,
    int? nights,
    double? amount,
  }) async {
    final notifier = _ref.read(achievementNotifierProvider.notifier);
    return await notifier.trackUserAction(
      UserAction.hotelBooking,
      metadata: {
        'hotelId': hotelId,
        'location': location,
        'nights': nights,
        'amount': amount,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track first booking (any service)
  Future<List<UserAchievement>> trackFirstBooking({
    String? serviceType,
    String? bookingId,
  }) async {
    final notifier = _ref.read(achievementNotifierProvider.notifier);
    return await notifier.trackUserAction(
      UserAction.firstBooking,
      metadata: {
        'serviceType': serviceType,
        'bookingId': bookingId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track service comparison usage
  Future<List<UserAchievement>> trackServiceComparison({
    String? serviceType,
    int? optionsCompared,
  }) async {
    final notifier = _ref.read(achievementNotifierProvider.notifier);
    return await notifier.trackUserAction(
      UserAction.serviceComparison,
      metadata: {
        'serviceType': serviceType,
        'optionsCompared': optionsCompared,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track review submission
  Future<List<UserAchievement>> trackReviewSubmission({
    String? serviceType,
    String? bookingId,
    double? rating,
  }) async {
    final notifier = _ref.read(achievementNotifierProvider.notifier);
    return await notifier.trackUserAction(
      UserAction.reviewSubmission,
      metadata: {
        'serviceType': serviceType,
        'bookingId': bookingId,
        'rating': rating,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
}

/// Provider for required services (these should be defined elsewhere in the app)
final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  throw UnimplementedError('AnalyticsService provider not implemented');
});

final loggingServiceProvider = Provider<LoggingService>((ref) {
  throw UnimplementedError('LoggingService provider not implemented');
});

final notificationServiceProvider = Provider<NotificationService>((ref) {
  throw UnimplementedError('NotificationService provider not implemented');
});
