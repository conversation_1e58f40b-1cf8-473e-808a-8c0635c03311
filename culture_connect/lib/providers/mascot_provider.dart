// Dart imports
import 'dart:async';

// Third-party imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/mascot/mascot_state.dart';
import 'package:culture_connect/services/mascot_service.dart';
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/providers/achievement_provider.dart';

/// Provider for the current mascot state
final mascotStateProvider = StreamProvider<MascotState>((ref) {
  final mascotService = ref.watch(mascotServiceProvider);
  return mascotService.stateStream;
});

/// Provider for mascot state notifier
final mascotStateNotifierProvider =
    StateNotifierProvider<MascotStateNotifier, MascotState>((ref) {
  final mascotService = ref.watch(mascotServiceProvider);
  return MascotStateNotifier(mascotService, ref);
});

/// Notifier for managing mascot state changes
class MascotStateNotifier extends StateNotifier<MascotState> {
  final MascotService _mascotService;
  final Ref _ref;
  StreamSubscription<MascotState>? _stateSubscription;
  ProviderSubscription<AsyncValue<List<UserAchievement>>>?
      _achievementSubscription;

  /// Creates a new mascot state notifier
  MascotStateNotifier(this._mascotService, this._ref)
      : super(MascotState.idle()) {
    _initialize();
  }

  /// Initialize the notifier
  void _initialize() {
    // Listen to mascot service state changes
    _stateSubscription = _mascotService.stateStream.listen((newState) {
      if (mounted) {
        state = newState;
      }
    });

    // Listen to achievement unlocks for celebration
    _achievementSubscription =
        _ref.listen(achievementNotifierProvider, (previous, next) {
      _handleAchievementChanges(previous, next);
    });
  }

  /// Handle achievement changes for celebration
  void _handleAchievementChanges(
    AsyncValue<List<UserAchievement>>? previous,
    AsyncValue<List<UserAchievement>> next,
  ) {
    next.whenData((achievements) {
      if (previous != null) {
        previous.whenData((previousAchievements) {
          // Find newly unlocked achievements
          final newlyUnlocked = achievements.where((achievement) {
            final wasUnlocked = previousAchievements.any((prev) =>
                prev.achievement.id == achievement.achievement.id &&
                prev.isUnlocked);
            return achievement.isUnlocked && !wasUnlocked;
          }).toList();

          // Trigger celebration for newly unlocked achievements
          for (final achievement in newlyUnlocked) {
            _mascotService.onAchievementUnlocked(achievement);
          }
        });
      }
    });
  }

  /// Update mascot state
  Future<void> updateState(MascotState newState) async {
    await _mascotService.updateState(newState);
  }

  /// Handle successful booking
  Future<void> onBookingSuccessful({
    required String bookingType,
    String? bookingId,
  }) async {
    await _mascotService.onBookingSuccessful(
      bookingType: bookingType,
      bookingId: bookingId,
    );
  }

  /// Handle booking failure
  Future<void> onBookingFailed({
    String? errorMessage,
    String? bookingType,
  }) async {
    await _mascotService.onBookingFailed(
      errorMessage: errorMessage,
      bookingType: bookingType,
    );
  }

  /// Handle loading state
  Future<void> onLoadingStarted({String? message}) async {
    await _mascotService.onLoadingStarted(message: message);
  }

  /// Handle error state
  Future<void> handleError({
    required String errorMessage,
    String? errorType,
  }) async {
    await _mascotService.onError(
      errorMessage: errorMessage,
      errorType: errorType,
    );
  }

  /// Handle onboarding step
  Future<void> onOnboardingStep({
    required String step,
    String? message,
  }) async {
    await _mascotService.onOnboardingStep(
      step: step,
      message: message,
    );
  }

  /// Handle discovery event
  Future<void> onDiscovery({
    required String discoveryType,
    String? message,
  }) async {
    await _mascotService.onDiscovery(
      discoveryType: discoveryType,
      message: message,
    );
  }

  /// Reset to idle state
  Future<void> resetToIdle() async {
    await _mascotService.resetToIdle();
  }

  @override
  void dispose() {
    _stateSubscription?.cancel();
    _achievementSubscription?.close();
    super.dispose();
  }
}

/// Provider for mascot actions helper
final mascotActionsProvider = Provider<MascotActions>((ref) {
  return MascotActions(ref);
});

/// Helper class for common mascot actions
class MascotActions {
  final Ref _ref;

  /// Creates a new mascot actions helper
  MascotActions(this._ref);

  /// Show celebration for achievement
  Future<void> celebrateAchievement({
    required String achievementId,
    String? achievementTitle,
  }) async {
    final notifier = _ref.read(mascotStateNotifierProvider.notifier);
    final celebrationState = MascotState.achievementCelebration(
      achievementId: achievementId,
      achievementTitle: achievementTitle,
    );
    await notifier.updateState(celebrationState);
  }

  /// Show success for travel booking
  Future<void> celebrateBooking({
    required String bookingType,
    String? bookingId,
  }) async {
    final notifier = _ref.read(mascotStateNotifierProvider.notifier);
    await notifier.onBookingSuccessful(
      bookingType: bookingType,
      bookingId: bookingId,
    );
  }

  /// Show error state
  Future<void> showError({
    required String errorMessage,
    String? errorType,
  }) async {
    final notifier = _ref.read(mascotStateNotifierProvider.notifier);
    await notifier.handleError(
      errorMessage: errorMessage,
      errorType: errorType,
    );
  }

  /// Show loading state
  Future<void> showLoading({String? message}) async {
    final notifier = _ref.read(mascotStateNotifierProvider.notifier);
    await notifier.onLoadingStarted(message: message);
  }

  /// Show onboarding help
  Future<void> showOnboardingHelp({
    required String step,
    String? message,
  }) async {
    final notifier = _ref.read(mascotStateNotifierProvider.notifier);
    await notifier.onOnboardingStep(
      step: step,
      message: message,
    );
  }

  /// Show discovery excitement
  Future<void> showDiscovery({
    required String discoveryType,
    String? message,
  }) async {
    final notifier = _ref.read(mascotStateNotifierProvider.notifier);
    await notifier.onDiscovery(
      discoveryType: discoveryType,
      message: message,
    );
  }

  /// Reset mascot to idle
  Future<void> resetToIdle() async {
    final notifier = _ref.read(mascotStateNotifierProvider.notifier);
    await notifier.resetToIdle();
  }
}

/// Provider for travel service specific mascot actions
final travelServiceMascotProvider = Provider<TravelServiceMascotActions>((ref) {
  return TravelServiceMascotActions(ref);
});

/// Helper class for travel service specific mascot actions
class TravelServiceMascotActions {
  final Ref _ref;

  /// Creates a new travel service mascot actions helper
  TravelServiceMascotActions(this._ref);

  /// Handle insurance purchase completion
  Future<void> onInsurancePurchased({
    String? policyId,
    String? provider,
  }) async {
    final actions = _ref.read(mascotActionsProvider);
    await actions.celebrateBooking(
      bookingType: 'Travel Insurance',
      bookingId: policyId,
    );
  }

  /// Handle flight booking completion
  Future<void> onFlightBooked({
    String? bookingId,
    String? airline,
  }) async {
    final actions = _ref.read(mascotActionsProvider);
    await actions.celebrateBooking(
      bookingType: 'Flight',
      bookingId: bookingId,
    );
  }

  /// Handle hotel booking completion
  Future<void> onHotelBooked({
    String? reservationId,
    String? hotelName,
  }) async {
    final actions = _ref.read(mascotActionsProvider);
    await actions.celebrateBooking(
      bookingType: 'Hotel',
      bookingId: reservationId,
    );
  }

  /// Handle transfer booking completion
  Future<void> onTransferBooked({
    String? transferId,
    String? transferType,
  }) async {
    final actions = _ref.read(mascotActionsProvider);
    await actions.celebrateBooking(
      bookingType: 'Airport Transfer',
      bookingId: transferId,
    );
  }

  /// Handle car rental booking completion
  Future<void> onCarRentalBooked({
    String? rentalId,
    String? carModel,
  }) async {
    final actions = _ref.read(mascotActionsProvider);
    await actions.celebrateBooking(
      bookingType: 'Car Rental',
      bookingId: rentalId,
    );
  }

  /// Handle visa application submission
  Future<void> onVisaApplicationSubmitted({
    String? applicationId,
    String? country,
  }) async {
    final actions = _ref.read(mascotActionsProvider);
    await actions.celebrateBooking(
      bookingType: 'Visa Application',
      bookingId: applicationId,
    );
  }

  /// Handle booking failure
  Future<void> onBookingFailed({
    required String bookingType,
    String? errorMessage,
  }) async {
    final actions = _ref.read(mascotActionsProvider);
    await actions.showError(
      errorMessage: errorMessage ?? '$bookingType booking failed',
      errorType: 'booking_failed',
    );
  }

  /// Handle payment processing
  Future<void> onPaymentProcessing() async {
    final actions = _ref.read(mascotActionsProvider);
    await actions.showLoading(message: 'Processing payment...');
  }

  /// Handle search in progress
  Future<void> onSearchInProgress({String? searchType}) async {
    final actions = _ref.read(mascotActionsProvider);
    await actions.showLoading(
      message:
          searchType != null ? 'Searching for $searchType...' : 'Searching...',
    );
  }
}
