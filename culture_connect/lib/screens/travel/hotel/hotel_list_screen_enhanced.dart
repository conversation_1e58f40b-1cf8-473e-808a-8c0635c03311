import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/hotel_filter_provider.dart';
import 'package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/hotel_filter_dialog.dart';
import 'package:culture_connect/widgets/travel/hotel_grid_item.dart';
import 'package:culture_connect/widgets/travel/hotel_skeleton_loading.dart';
import 'package:culture_connect/widgets/travel/hotel_sort_selector.dart';
import 'package:culture_connect/widgets/travel/travel_service_card.dart';

/// Enhanced screen for displaying a list of hotels
class HotelListScreenEnhanced extends ConsumerStatefulWidget {
  /// Creates a new hotel list screen
  const HotelListScreenEnhanced({super.key});

  @override
  ConsumerState<HotelListScreenEnhanced> createState() =>
      _HotelListScreenEnhancedState();
}

class _HotelListScreenEnhancedState
    extends ConsumerState<HotelListScreenEnhanced>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  bool _isGridView = true;
  late AnimationController _animationController;
  late Animation<double> _animation;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();

    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => const HotelFilterDialog(),
    );
  }

  void _toggleViewMode() {
    setState(() {
      _isGridView = !_isGridView;
      _animationController.reset();
      _animationController.forward();
    });
  }

  void _navigateToHotelDetails(Hotel hotel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HotelDetailsScreenEnhanced(hotel: hotel),
      ),
    );
  }

  List<Hotel> _filterBySearch(List<Hotel> hotels) {
    if (_searchQuery.isEmpty) return hotels;

    final query = _searchQuery.toLowerCase();
    return hotels.where((hotel) {
      return hotel.name.toLowerCase().contains(query) ||
          hotel.location.toLowerCase().contains(query) ||
          hotel.description.toLowerCase().contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search hotels...',
                  border: InputBorder.none,
                  hintStyle: TextStyle(
                      color: theme.colorScheme.onSurface.withAlpha(153)),
                ),
                style: TextStyle(color: theme.colorScheme.onSurface),
                autofocus: true,
              )
            : const Text('Hotels'),
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            tooltip: _isSearching ? 'Clear search' : 'Search',
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
          const HotelSortSelector(),
          IconButton(
            icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
            tooltip: _isGridView ? 'List view' : 'Grid view',
            onPressed: _toggleViewMode,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter',
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: _buildHotelList(),
    );
  }

  Widget _buildHotelList() {
    final filteredHotelsAsyncValue = ref.watch(filteredHotelsProvider);

    return filteredHotelsAsyncValue.when(
      data: (hotels) {
        final searchFilteredHotels = _filterBySearch(hotels);

        if (searchFilteredHotels.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.hotel,
                  size: 64,
                  color: Theme.of(context)
                      .colorScheme
                      .onSurfaceVariant
                      .withAlpha(128),
                ),
                const SizedBox(height: 16),
                Text(
                  _searchQuery.isNotEmpty
                      ? 'No hotels found for "$_searchQuery"'
                      : 'No hotels available',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
                if (_searchQuery.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _searchController.clear();
                      });
                    },
                    child: const Text('Clear Search'),
                  ),
                ],
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            // Refresh data
            ref.invalidate(filteredHotelsProvider);
          },
          child: FadeTransition(
            opacity: _animation,
            child: _isGridView
                ? _buildGridView(searchFilteredHotels)
                : _buildListView(searchFilteredHotels),
          ),
        );
      },
      loading: () => HotelSkeletonLoading(isGrid: _isGridView),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () => ref.refresh(filteredHotelsProvider),
        ),
      ),
    );
  }

  Widget _buildGridView(List<Hotel> hotels) {
    return GridView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: hotels.length,
      itemBuilder: (context, index) {
        final hotel = hotels[index];
        return HotelGridItem(
          hotel: hotel,
          onTap: () => _navigateToHotelDetails(hotel),
        );
      },
    );
  }

  Widget _buildListView(List<Hotel> hotels) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: hotels.length,
      itemBuilder: (context, index) {
        final hotel = hotels[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: TravelServiceCard(
            travelService: hotel,
            onTap: () => _navigateToHotelDetails(hotel),
          ),
        );
      },
    );
  }
}
