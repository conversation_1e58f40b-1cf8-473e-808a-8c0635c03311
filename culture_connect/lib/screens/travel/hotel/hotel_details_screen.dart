import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';
import 'package:culture_connect/widgets/travel/price_alert_button.dart';

/// Screen for displaying hotel details
class HotelDetailsScreen extends ConsumerStatefulWidget {
  /// The hotel to display
  final Hotel hotel;

  /// Creates a new hotel details screen
  const HotelDetailsScreen({
    super.key,
    required this.hotel,
  });

  @override
  ConsumerState<HotelDetailsScreen> createState() => _HotelDetailsScreenState();
}

class _HotelDetailsScreenState extends ConsumerState<HotelDetailsScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;
  DateTime _checkInDate = DateTime.now().add(const Duration(days: 1));
  DateTime _checkOutDate = DateTime.now().add(const Duration(days: 3));
  int _nights = 2;
  HotelRoom? _selectedRoom;
  double _totalPrice = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    if (widget.hotel.rooms.isNotEmpty) {
      _selectedRoom = widget.hotel.rooms.first;
    }
    _calculateTotalPrice();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _calculateTotalPrice() {
    _nights = _checkOutDate.difference(_checkInDate).inDays;
    if (_nights < 1) _nights = 1;
    if (_selectedRoom != null) {
      _totalPrice = _selectedRoom!.pricePerNight * _nights;
    } else {
      _totalPrice = widget.hotel.price * _nights;
    }
  }

  Future<void> _selectCheckInDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _checkInDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null && picked != _checkInDate) {
      setState(() {
        _checkInDate = picked;
        if (_checkInDate.isAfter(_checkOutDate)) {
          _checkOutDate = _checkInDate.add(const Duration(days: 1));
        }
        _calculateTotalPrice();
      });
    }
  }

  Future<void> _selectCheckOutDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _checkOutDate,
      firstDate: _checkInDate.add(const Duration(days: 1)),
      lastDate: _checkInDate.add(const Duration(days: 30)),
    );

    if (picked != null && picked != _checkOutDate) {
      setState(() {
        _checkOutDate = picked;
        _calculateTotalPrice();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App bar with image
          SliverAppBar(
            expandedHeight: 250,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Hero(
                tag: 'hotel_image_${widget.hotel.id}',
                child: Image.network(
                  widget.hotel.imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: theme.colorScheme.surfaceContainerHighest,
                      child: Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title, rating, and price
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title and rating
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.hotel.name,
                                  style:
                                      theme.textTheme.headlineSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.star,
                                      size: 16,
                                      color: Colors.amber,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      '${widget.hotel.starRating.value}-Star Hotel',
                                      style:
                                          theme.textTheme.titleMedium?.copyWith(
                                        color:
                                            theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              RatingDisplay(
                                rating: widget.hotel.rating,
                                size: 20,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '${widget.hotel.reviewCount} reviews',
                                style: theme.textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Location
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 16,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              widget.hotel.location,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Price
                      Row(
                        children: [
                          if (widget.hotel.isOnSale &&
                              widget.hotel.originalPrice != null) ...[
                            Text(
                              widget.hotel.formattedOriginalPrice!,
                              style: theme.textTheme.titleMedium?.copyWith(
                                decoration: TextDecoration.lineThrough,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            const SizedBox(width: 8),
                          ],
                          Text(
                            'From ${widget.hotel.formattedPrice} / night',
                            style: theme.textTheme.titleLarge?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Tabs
                TabBar(
                  controller: _tabController,
                  tabs: const [
                    Tab(text: 'Details'),
                    Tab(text: 'Rooms'),
                    Tab(text: 'Amenities'),
                  ],
                  labelColor: theme.colorScheme.primary,
                  unselectedLabelColor: theme.colorScheme.onSurface,
                  indicatorColor: theme.colorScheme.primary,
                ),

                // Tab content
                SizedBox(
                  height: 500, // Fixed height for tab content
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildDetailsTab(),
                      _buildRoomsTab(),
                      _buildAmenitiesTab(),
                    ],
                  ),
                ),

                // Booking section
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Book Your Stay',
                        style: theme.textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              // Check-in date
                              ListTile(
                                title: const Text('Check-in Date'),
                                subtitle: Text(
                                  '${_checkInDate.day}/${_checkInDate.month}/${_checkInDate.year}',
                                  style: theme.textTheme.titleMedium,
                                ),
                                trailing: const Icon(Icons.calendar_today),
                                onTap: _selectCheckInDate,
                              ),
                              const Divider(),

                              // Check-out date
                              ListTile(
                                title: const Text('Check-out Date'),
                                subtitle: Text(
                                  '${_checkOutDate.day}/${_checkOutDate.month}/${_checkOutDate.year}',
                                  style: theme.textTheme.titleMedium,
                                ),
                                trailing: const Icon(Icons.calendar_today),
                                onTap: _selectCheckOutDate,
                              ),
                              const Divider(),

                              // Duration
                              ListTile(
                                title: const Text('Duration'),
                                subtitle: Text(
                                  '$_nights nights',
                                  style: theme.textTheme.titleMedium,
                                ),
                              ),
                              const Divider(),

                              // Selected room
                              ListTile(
                                title: const Text('Selected Room'),
                                subtitle: Text(
                                  _selectedRoom?.type.displayName ??
                                      'No room selected',
                                  style: theme.textTheme.titleMedium,
                                ),
                              ),
                              const Divider(),

                              // Total price
                              ListTile(
                                title: const Text('Total Price'),
                                subtitle: Text(
                                  '${widget.hotel.currency}${_totalPrice.toStringAsFixed(2)}',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Price alert button
                      PriceAlertButton(
                        travelServiceId: widget.hotel.id,
                        travelServiceType: TravelServiceType.hotel,
                        travelServiceName: widget.hotel.name,
                        currentPrice: widget.hotel.price,
                        currency: widget.hotel.currency,
                      ),

                      const SizedBox(height: 16),

                      // Travel Insurance Recommendation
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.security,
                                    color: theme.colorScheme.primary,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Protect Your Trip',
                                    style: theme.textTheme.titleSmall?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.primary,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Add travel insurance to protect your hotel booking against cancellations and emergencies.',
                                style: theme.textTheme.bodySmall,
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: OutlinedButton(
                                      onPressed: () {
                                        Navigator.pushNamed(
                                            context, '/insurance');
                                      },
                                      child: const Text('Learn More'),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () {
                                        Navigator.pushNamed(
                                          context,
                                          '/insurance/search',
                                          arguments: {
                                            'destination':
                                                widget.hotel.location,
                                            'startDate': _checkInDate,
                                            'endDate': _checkOutDate,
                                            'tripValue': _totalPrice,
                                            'currency': widget.hotel.currency,
                                            'hotelBooking': true,
                                            'accommodationProtection': true,
                                          },
                                        );
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            theme.colorScheme.primary,
                                        foregroundColor:
                                            theme.colorScheme.onPrimary,
                                      ),
                                      child: const Text('Get Quote'),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Book now button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _selectedRoom == null
                              ? null
                              : () {
                                  // Show booking confirmation dialog
                                  showDialog(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      title: const Text('Booking Confirmation'),
                                      content: Text(
                                        'This would normally navigate to the payment screen. '
                                        'Total: ${widget.hotel.currency}${_totalPrice.toStringAsFixed(2)}\n'
                                        'Hotel: ${widget.hotel.name}, ${_selectedRoom!.type.displayName} ($_nights nights)',
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          child: const Text('CANCEL'),
                                        ),
                                        TextButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              const SnackBar(
                                                content:
                                                    Text('Booking confirmed!'),
                                                behavior:
                                                    SnackBarBehavior.floating,
                                              ),
                                            );
                                          },
                                          child: const Text('CONFIRM'),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                          ),
                          child: Text(
                            'Book Now - ${widget.hotel.currency}${_totalPrice.toStringAsFixed(2)}',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: theme.colorScheme.onPrimary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Description
          Text(
            'Description',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.hotel.description,
            style: Theme.of(context).textTheme.bodyMedium,
          ),

          const SizedBox(height: 16),

          // Check-in/out times
          Text(
            'Check-in/out Times',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          Icons.login,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Check-in',
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.hotel.formattedCheckInTime,
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          Icons.logout,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Check-out',
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.hotel.formattedCheckOutTime,
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Location details
          Text(
            'Location',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.hotel.formattedDistanceFromCityCenter,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.hotel.formattedDistanceFromAirport,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoomsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.hotel.rooms.length,
      itemBuilder: (context, index) {
        final room = widget.hotel.rooms[index];
        final isSelected = _selectedRoom?.id == room.id;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: isSelected
                ? BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  )
                : BorderSide.none,
          ),
          child: InkWell(
            onTap: () {
              setState(() {
                _selectedRoom = room;
                _calculateTotalPrice();
              });
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Room image
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                  child: Image.network(
                    room.imageUrl,
                    height: 150,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 150,
                        color: Theme.of(context)
                            .colorScheme
                            .surfaceContainerHighest,
                        child: Center(
                          child: Icon(
                            Icons.image_not_supported,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // Room details
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Room type and price
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            room.type.displayName,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          Text(
                            '${room.formattedPricePerNight} / night',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Room description
                      Text(
                        room.description,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 8),

                      // Room details
                      Wrap(
                        spacing: 16,
                        runSpacing: 8,
                        children: [
                          _buildRoomFeature(
                              Icons.people, '${room.maxGuests} guests'),
                          _buildRoomFeature(Icons.bed,
                              '${room.bedCount} ${room.bedType} bed${room.bedCount > 1 ? 's' : ''}'),
                          _buildRoomFeature(Icons.square_foot, room.roomSize),
                          if (room.hasView)
                            _buildRoomFeature(
                                Icons.visibility, room.viewType ?? 'View'),
                          if (room.hasBalcony)
                            _buildRoomFeature(Icons.balcony, 'Balcony'),
                          if (room.hasPrivateBathroom)
                            _buildRoomFeature(
                                Icons.bathroom, 'Private Bathroom'),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Select button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _selectedRoom = room;
                              _calculateTotalPrice();
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context)
                                    .colorScheme
                                    .surfaceContainerHighest,
                            foregroundColor: isSelected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context)
                                    .colorScheme
                                    .onSurfaceVariant,
                          ),
                          child: Text(isSelected ? 'Selected' : 'Select'),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRoomFeature(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildAmenitiesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hotel amenities
          Text(
            'Hotel Amenities',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildAmenityChip(
                  Icons.wifi, 'Free WiFi', widget.hotel.hasFreeWifi),
              _buildAmenityChip(Icons.local_parking, 'Free Parking',
                  widget.hotel.hasFreeParking),
              _buildAmenityChip(Icons.pool, 'Pool', widget.hotel.hasPool),
              _buildAmenityChip(Icons.spa, 'Spa', widget.hotel.hasSpa),
              _buildAmenityChip(
                  Icons.fitness_center, 'Gym', widget.hotel.hasGym),
              _buildAmenityChip(
                  Icons.restaurant, 'Restaurant', widget.hotel.hasRestaurant),
              _buildAmenityChip(Icons.local_bar, 'Bar', widget.hotel.hasBar),
              _buildAmenityChip(Icons.room_service, 'Room Service',
                  widget.hotel.hasRoomService),
              _buildAmenityChip(Icons.business_center, 'Business Center',
                  widget.hotel.hasBusinessCenter),
              _buildAmenityChip(Icons.meeting_room, 'Conference Room',
                  widget.hotel.hasConferenceRoom),
              _buildAmenityChip(
                  Icons.child_care, 'Kids Club', widget.hotel.hasKidsClub),
              _buildAmenityChip(Icons.support_agent, 'Concierge',
                  widget.hotel.hasConciergeService),
              _buildAmenityChip(Icons.local_laundry_service, 'Laundry',
                  widget.hotel.hasLaundryService),
              _buildAmenityChip(Icons.airport_shuttle, 'Shuttle',
                  widget.hotel.hasShuttleService),
              _buildAmenityChip(Icons.access_time, '24h Front Desk',
                  widget.hotel.has24HrFrontDesk),
            ],
          ),

          const SizedBox(height: 16),

          // Additional amenities
          Text(
            'Additional Amenities',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: widget.hotel.amenities.map((amenity) {
              return Chip(
                label: Text(amenity),
                backgroundColor:
                    Theme.of(context).colorScheme.surfaceContainerHighest,
                labelStyle: TextStyle(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAmenityChip(IconData icon, String label, bool isAvailable) {
    return Chip(
      avatar: Icon(
        icon,
        size: 16,
        color: isAvailable
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(128),
      ),
      label: Text(label),
      backgroundColor: isAvailable
          ? Theme.of(context).colorScheme.primaryContainer
          : Theme.of(context)
              .colorScheme
              .surfaceContainerHighest
              .withAlpha(128),
      labelStyle: TextStyle(
        color: isAvailable
            ? Theme.of(context).colorScheme.onPrimaryContainer
            : Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(128),
        decoration: isAvailable ? null : TextDecoration.lineThrough,
      ),
    );
  }
}
