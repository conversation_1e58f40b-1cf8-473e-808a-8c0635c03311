// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/widgets/travel/price_alert_button.dart';

/// Screen for displaying flight details
class FlightDetailsScreen extends ConsumerStatefulWidget {
  /// The flight to display
  final Flight flight;

  /// Creates a new flight details screen
  const FlightDetailsScreen({
    super.key,
    required this.flight,
  });

  @override
  ConsumerState<FlightDetailsScreen> createState() =>
      _FlightDetailsScreenState();
}

class _FlightDetailsScreenState extends ConsumerState<FlightDetailsScreen> {
  final ScrollController _scrollController = ScrollController();
  int _passengerCount = 1;
  double _totalPrice = 0;

  @override
  void initState() {
    super.initState();
    _calculateTotalPrice();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _calculateTotalPrice() {
    _totalPrice = widget.flight.price * _passengerCount;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Flight Details'),
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Flight summary card
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Airline and flight number
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundImage: NetworkImage(
                              widget.flight.segments.first.airlineLogoUrl),
                          radius: 20,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.flight.segments.first.airlineName,
                                style: theme.textTheme.titleLarge,
                              ),
                              Text(
                                'Flight ${widget.flight.segments.first.flightNumber}',
                                style: theme.textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color:
                                _getFlightTypeColor(widget.flight.flightType),
                            borderRadius:
                                const BorderRadius.all(Radius.circular(4)),
                          ),
                          child: Text(
                            widget.flight.flightType.displayName,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Flight route
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.flight.departureAirportCode,
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                widget.flight.departureCity,
                                style: theme.textTheme.bodyMedium,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                widget.flight.formattedDepartureTime,
                                style: theme.textTheme.titleMedium,
                              ),
                              Text(
                                widget.flight.formattedDepartureDate,
                                style: theme.textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                widget.flight.formattedTotalDuration,
                                style: theme.textTheme.bodySmall,
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              Stack(
                                alignment: Alignment.center,
                                children: [
                                  Container(
                                    height: 1,
                                    color: theme.colorScheme.onSurfaceVariant
                                        .withAlpha(128),
                                  ),
                                  Icon(
                                    Icons.flight,
                                    size: 24,
                                    color: theme.colorScheme.primary,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                widget.flight.formattedStops,
                                style: theme.textTheme.bodySmall,
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                widget.flight.arrivalAirportCode,
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.right,
                              ),
                              Text(
                                widget.flight.arrivalCity,
                                style: theme.textTheme.bodyMedium,
                                textAlign: TextAlign.right,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                widget.flight.formattedArrivalTime,
                                style: theme.textTheme.titleMedium,
                                textAlign: TextAlign.right,
                              ),
                              Text(
                                widget.flight.formattedArrivalDate,
                                style: theme.textTheme.bodySmall,
                                textAlign: TextAlign.right,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Class and baggage
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primaryContainer,
                            borderRadius:
                                const BorderRadius.all(Radius.circular(4)),
                          ),
                          child: Text(
                            widget.flight.flightClass.displayName,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onPrimaryContainer,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            Icon(
                              Icons.luggage,
                              size: 16,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              widget.flight.formattedBaggageAllowance,
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Flight segments
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Flight Segments',
                style: theme.textTheme.titleLarge,
              ),
            ),
            const SizedBox(height: 8),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: widget.flight.segments.length,
              itemBuilder: (context, index) {
                final segment = widget.flight.segments[index];
                final isLastSegment =
                    index == widget.flight.segments.length - 1;

                return Column(
                  children: [
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            // Airline and flight number
                            Row(
                              children: [
                                CircleAvatar(
                                  backgroundImage:
                                      NetworkImage(segment.airlineLogoUrl),
                                  radius: 16,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        segment.airlineName,
                                        style: theme.textTheme.titleMedium,
                                      ),
                                      Text(
                                        'Flight ${segment.flightNumber}',
                                        style: theme.textTheme.bodySmall,
                                      ),
                                    ],
                                  ),
                                ),
                                Text(
                                  segment.formattedDuration,
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 16),

                            // Segment route
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        segment.departureAirportCode,
                                        style: theme.textTheme.titleMedium
                                            ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Text(
                                        segment.departureCity,
                                        style: theme.textTheme.bodySmall,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        segment.formattedDepartureTime,
                                        style: theme.textTheme.bodyMedium,
                                      ),
                                      if (segment.departureTerminal != null)
                                        Text(
                                          'Terminal ${segment.departureTerminal}',
                                          style: theme.textTheme.bodySmall,
                                        ),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.flight,
                                        size: 24,
                                        color: theme.colorScheme.primary,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        segment.formattedDistance,
                                        style: theme.textTheme.bodySmall,
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        segment.aircraftType,
                                        style: theme.textTheme.bodySmall,
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Text(
                                        segment.arrivalAirportCode,
                                        style: theme.textTheme.titleMedium
                                            ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.right,
                                      ),
                                      Text(
                                        segment.arrivalCity,
                                        style: theme.textTheme.bodySmall,
                                        textAlign: TextAlign.right,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        segment.formattedArrivalTime,
                                        style: theme.textTheme.bodyMedium,
                                        textAlign: TextAlign.right,
                                      ),
                                      if (segment.arrivalTerminal != null)
                                        Text(
                                          'Terminal ${segment.arrivalTerminal}',
                                          style: theme.textTheme.bodySmall,
                                          textAlign: TextAlign.right,
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 8),

                            // Flight status
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: segment.isOnTime
                                    ? Colors.green
                                    : Colors.orange,
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(4)),
                              ),
                              child: Text(
                                segment.formattedStatus,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Layover information
                    if (!isLastSegment)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          children: [
                            Container(
                              width: 24,
                              height: 24,
                              decoration: const BoxDecoration(
                                color: Colors
                                    .blue, // Use a fixed color instead of theme
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.access_time,
                                size: 16,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Layover in ${widget.flight.layovers[index].city} (${widget.flight.layovers[index].airportCode}) - ${widget.flight.layovers[index].formattedDuration}',
                                style: theme.textTheme.bodyMedium,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                );
              },
            ),

            const SizedBox(height: 24),

            // Flight amenities
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Amenities',
                style: theme.textTheme.titleLarge,
              ),
            ),
            const SizedBox(height: 8),
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildAmenityRow(
                      Icons.tv,
                      'In-flight Entertainment',
                      widget.flight.hasInFlightEntertainment,
                    ),
                    _buildAmenityRow(
                      Icons.wifi,
                      'Wi-Fi',
                      widget.flight.hasWifi,
                    ),
                    _buildAmenityRow(
                      Icons.power,
                      'Power Outlets',
                      widget.flight.hasPowerOutlets,
                    ),
                    _buildAmenityRow(
                      Icons.fastfood,
                      'Meal Service',
                      widget.flight.hasMeal,
                    ),
                    _buildAmenityRow(
                      Icons.airline_seat_legroom_extra,
                      'Extra Legroom',
                      widget.flight.hasExtraLegroom,
                    ),
                    _buildAmenityRow(
                      Icons.meeting_room,
                      'Lounge Access',
                      widget.flight.hasLoungeAccess,
                    ),
                    _buildAmenityRow(
                      Icons.priority_high,
                      'Priority Boarding',
                      widget.flight.hasPriorityBoarding,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Fare details
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Fare Details',
                style: theme.textTheme.titleLarge,
              ),
            ),
            const SizedBox(height: 8),
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Refundable',
                          style: theme.textTheme.bodyMedium,
                        ),
                        Text(
                          widget.flight.isRefundable ? 'Yes' : 'No',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Changeable',
                          style: theme.textTheme.bodyMedium,
                        ),
                        Text(
                          widget.flight.isChangeable ? 'Yes' : 'No',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    if (widget.flight.isChangeable &&
                        widget.flight.changeFee != null) ...[
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Change Fee',
                            style: theme.textTheme.bodyMedium,
                          ),
                          Text(
                            widget.flight.formattedChangeFee!,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Booking section
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Passenger Information',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          // Passenger count
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Number of Passengers',
                                style: theme.textTheme.titleMedium,
                              ),
                              Row(
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.remove),
                                    onPressed: _passengerCount > 1
                                        ? () {
                                            setState(() {
                                              _passengerCount--;
                                              _calculateTotalPrice();
                                            });
                                          }
                                        : null,
                                  ),
                                  Text(
                                    '$_passengerCount',
                                    style: theme.textTheme.titleMedium,
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.add),
                                    onPressed: _passengerCount <
                                            widget.flight.passengerCount
                                        ? () {
                                            setState(() {
                                              _passengerCount++;
                                              _calculateTotalPrice();
                                            });
                                          }
                                        : null,
                                  ),
                                ],
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Price breakdown
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Price per passenger',
                                style: theme.textTheme.bodyMedium,
                              ),
                              Row(
                                children: [
                                  if (widget.flight.isOnSale &&
                                      widget.flight.originalPrice != null) ...[
                                    Text(
                                      widget.flight.formattedOriginalPrice!,
                                      style:
                                          theme.textTheme.bodyMedium?.copyWith(
                                        decoration: TextDecoration.lineThrough,
                                        color:
                                            theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                  ],
                                  Text(
                                    widget.flight.formattedPrice,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Total price',
                                style: theme.textTheme.titleMedium,
                              ),
                              Text(
                                '${widget.flight.currency}${_totalPrice.toStringAsFixed(2)}',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Price alert button
                  PriceAlertButton(
                    travelServiceId: widget.flight.id,
                    travelServiceType: TravelServiceType.flight,
                    travelServiceName:
                        '${widget.flight.departureAirportCode} to ${widget.flight.arrivalAirportCode}',
                    currentPrice: widget.flight.price,
                    currency: widget.flight.currency,
                  ),

                  const SizedBox(height: 16),

                  // Book now button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // Show booking confirmation dialog
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('Booking Confirmation'),
                            content: Text(
                              'This would normally navigate to the payment screen. '
                              'Total: ${widget.flight.currency}${_totalPrice.toStringAsFixed(2)}\n'
                              'Flight: ${widget.flight.departureAirportCode} to ${widget.flight.arrivalAirportCode} ($_passengerCount passengers)',
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('CANCEL'),
                              ),
                              TextButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('Booking confirmed!'),
                                      behavior: SnackBarBehavior.floating,
                                    ),
                                  );
                                },
                                child: const Text('CONFIRM'),
                              ),
                            ],
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                      ),
                      child: Text(
                        'Book Now - ${widget.flight.currency}${_totalPrice.toStringAsFixed(2)}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmenityRow(IconData icon, String label, bool isAvailable) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: isAvailable
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(128),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isAvailable
                        ? Theme.of(context).colorScheme.onSurface
                        : Theme.of(context)
                            .colorScheme
                            .onSurfaceVariant
                            .withAlpha(128),
                    decoration: isAvailable ? null : TextDecoration.lineThrough,
                  ),
            ),
          ),
          Icon(
            isAvailable ? Icons.check_circle : Icons.cancel,
            size: 20,
            color: isAvailable ? Colors.green : Colors.red.withAlpha(128),
          ),
        ],
      ),
    );
  }

  Color _getFlightTypeColor(FlightType type) {
    switch (type) {
      case FlightType.direct:
        return Colors.green;
      case FlightType.oneStop:
        return Colors.orange;
      case FlightType.multiStop:
        return Colors.red;
    }
  }
}
