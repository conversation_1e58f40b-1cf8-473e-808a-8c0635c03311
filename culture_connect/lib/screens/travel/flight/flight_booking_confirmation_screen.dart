// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/flight/booking_summary.dart';
import 'package:culture_connect/models/travel/flight/fare_breakdown.dart';
import 'package:culture_connect/models/travel/flight_search_params.dart';
import 'package:culture_connect/providers/travel/flight_providers.dart';
import 'package:culture_connect/screens/payment/payment_screen.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen for confirming flight booking details and processing payment
class FlightBookingConfirmationScreen extends ConsumerStatefulWidget {
  /// Creates a new flight booking confirmation screen
  const FlightBookingConfirmationScreen({super.key});

  @override
  ConsumerState<FlightBookingConfirmationScreen> createState() =>
      _FlightBookingConfirmationScreenState();
}

class _FlightBookingConfirmationScreenState
    extends ConsumerState<FlightBookingConfirmationScreen> {
  // State variables
  bool _isLoading = false;
  bool _termsAccepted = false;
  bool _privacyAccepted = false;
  String? _errorMessage;
  BookingSummary? _bookingSummary;

  @override
  void initState() {
    super.initState();
    _generateBookingSummary();
  }

  void _generateBookingSummary() {
    try {
      setState(() => _isLoading = true);

      // Get data from providers
      final selectedFlight = ref.read(selectedFlightProvider);
      final passengerInfo = ref.read(passengerInfoProvider);
      final selectedSeats = ref.read(selectedSeatsProvider);
      final selectedServices = ref.read(selectedServicesProvider);

      if (selectedFlight == null || passengerInfo.isEmpty) {
        throw Exception('Missing flight or passenger information');
      }

      // Calculate fare breakdown (mock implementation)
      final basePrice = selectedFlight.price * passengerInfo.length;
      final seatFees = _calculateSeatFees(selectedSeats);
      final serviceFees =
          selectedServices.fold(0.0, (sum, service) => sum + service.price);
      final taxes = basePrice * 0.15; // 15% tax
      const fees = 25.0; // Fixed booking fee

      final fareBreakdown = FareBreakdown(
        baseFare: basePrice,
        taxes: taxes,
        fees: fees,
        discount: 0.0,
        totalPrice: basePrice + taxes + fees + seatFees + serviceFees,
        currency: selectedFlight.currency,
      );

      _bookingSummary = BookingSummary(
        flight: selectedFlight,
        passengers: passengerInfo,
        selectedSeats: selectedSeats,
        selectedServices: selectedServices,
        fareBreakdown: fareBreakdown,
        totalPrice: fareBreakdown.totalPrice,
      );

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  double _calculateSeatFees(Map<String, String> selectedSeats) {
    // Mock seat fee calculation
    return selectedSeats.length * 20.0; // $20 per seat
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Confirmation'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: ErrorView(
                    error: _errorMessage!,
                    onRetry: _generateBookingSummary,
                  ),
                )
              : _bookingSummary == null
                  ? const Center(
                      child: Text('No booking information available'))
                  : _buildConfirmationContent(theme),
      bottomNavigationBar: _buildPaymentButton(theme),
    );
  }

  Widget _buildConfirmationContent(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFlightSummary(theme),
          const SizedBox(height: 24),
          _buildPassengerSummary(theme),
          const SizedBox(height: 24),
          _buildSeatSummary(theme),
          const SizedBox(height: 24),
          _buildFareBreakdown(theme),
          const SizedBox(height: 24),
          _buildTermsAndConditions(theme),
          const SizedBox(height: 100), // Space for bottom button
        ],
      ),
    );
  }

  Widget _buildFlightSummary(ThemeData theme) {
    final flight = _bookingSummary!.flight;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Flight Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        flight.departureAirport,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        flight.departureCity,
                        style: theme.textTheme.bodyMedium,
                      ),
                      Text(
                        flight.formattedDepartureTime,
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.flight_takeoff,
                  color: theme.colorScheme.primary,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        flight.arrivalAirport,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        flight.arrivalCity,
                        style: theme.textTheme.bodyMedium,
                      ),
                      Text(
                        flight.formattedArrivalTime,
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Flight Number: ${flight.flightNumber}'),
                Text('Duration: ${flight.formattedDuration}'),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Aircraft: ${flight.aircraft ?? 'N/A'}'),
                Text('Class: ${flight.cabinClass}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPassengerSummary(ThemeData theme) {
    final passengers = _bookingSummary!.passengers;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Passengers (${passengers.length})',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...passengers.asMap().entries.map((entry) {
              final index = entry.key;
              final passenger = entry.value;

              return Padding(
                padding: EdgeInsets.only(
                    bottom: index < passengers.length - 1 ? 12 : 0),
                child: Row(
                  children: [
                    Icon(
                      _getPassengerTypeIcon(passenger.type),
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            passenger.fullName,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            '${_getPassengerTypeLabel(passenger.type)} • ${passenger.nationality}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withAlpha(153),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildSeatSummary(ThemeData theme) {
    final selectedSeats = _bookingSummary!.selectedSeats;

    if (selectedSeats.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Seat Selection',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'No seats selected - seats will be assigned at check-in',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(153),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Selected Seats',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...selectedSeats.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(entry.key),
                    Text(
                      'Seat ${entry.value}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildFareBreakdown(ThemeData theme) {
    final fareBreakdown = _bookingSummary!.fareBreakdown;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fare Breakdown',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildFareRow(theme, 'Base Fare', fareBreakdown.baseFare,
                _bookingSummary!.flight.currency),
            _buildFareRow(
                theme,
                'Taxes & Fees',
                fareBreakdown.taxes + fareBreakdown.fees,
                _bookingSummary!.flight.currency),
            if (_bookingSummary!.selectedSeats.isNotEmpty)
              _buildFareRow(
                  theme,
                  'Seat Selection',
                  _calculateSeatFees(_bookingSummary!.selectedSeats),
                  _bookingSummary!.flight.currency),
            if (_bookingSummary!.selectedServices.isNotEmpty)
              _buildFareRow(
                  theme,
                  'Additional Services',
                  _bookingSummary!.additionalServicesTotal,
                  _bookingSummary!.flight.currency),
            const Divider(),
            _buildFareRow(
              theme,
              'Total',
              _bookingSummary!.totalPrice,
              _bookingSummary!.flight.currency,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFareRow(
      ThemeData theme, String label, double amount, String currency,
      {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '$currency ${amount.toStringAsFixed(2)}',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? theme.colorScheme.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsAndConditions(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Terms & Conditions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              value: _termsAccepted,
              onChanged: (value) {
                setState(() => _termsAccepted = value ?? false);
              },
              title: const Text('I accept the Terms and Conditions'),
              subtitle: GestureDetector(
                onTap: _showTermsDialog,
                child: Text(
                  'View Terms and Conditions',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
            ),
            CheckboxListTile(
              value: _privacyAccepted,
              onChanged: (value) {
                setState(() => _privacyAccepted = value ?? false);
              },
              title: const Text('I accept the Privacy Policy'),
              subtitle: GestureDetector(
                onTap: _showPrivacyDialog,
                child: Text(
                  'View Privacy Policy',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentButton(ThemeData theme) {
    final canProceed = _termsAccepted && _privacyAccepted && !_isLoading;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withAlpha(26),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_bookingSummary != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Amount:',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _bookingSummary!.formattedTotalPrice,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: canProceed ? _proceedToPayment : null,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Text(
                      'Proceed to Payment',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  IconData _getPassengerTypeIcon(PassengerType type) {
    switch (type) {
      case PassengerType.adult:
        return Icons.person;
      case PassengerType.child:
        return Icons.child_care;
      case PassengerType.infant:
        return Icons.baby_changing_station;
    }
  }

  String _getPassengerTypeLabel(PassengerType type) {
    switch (type) {
      case PassengerType.adult:
        return 'Adult (12+ years)';
      case PassengerType.child:
        return 'Child (2-11 years)';
      case PassengerType.infant:
        return 'Infant (under 2 years)';
    }
  }

  void _showTermsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Terms and Conditions'),
        content: const SingleChildScrollView(
          child: Text(
            'By booking this flight, you agree to the following terms and conditions:\n\n'
            '1. All bookings are subject to availability and confirmation.\n'
            '2. Cancellation and change policies vary by fare type.\n'
            '3. Passengers must arrive at the airport at least 2 hours before domestic flights and 3 hours before international flights.\n'
            '4. Valid identification is required for all passengers.\n'
            '5. Baggage allowances and restrictions apply.\n'
            '6. The airline reserves the right to change schedules.\n'
            '7. Travel insurance is recommended.\n'
            '8. Special assistance requests must be made in advance.\n'
            '9. Seat assignments are subject to aircraft configuration.\n'
            '10. Additional fees may apply for services and amenities.\n\n'
            'For complete terms and conditions, please visit our website.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const SingleChildScrollView(
          child: Text(
            'Your privacy is important to us. This privacy policy explains how we collect, use, and protect your information:\n\n'
            '1. We collect personal information necessary for booking and travel.\n'
            '2. Your information is used to process bookings and provide services.\n'
            '3. We may share information with airlines and travel partners.\n'
            '4. Your payment information is securely processed.\n'
            '5. We use cookies to improve your experience.\n'
            '6. You can access and update your personal information.\n'
            '7. We retain information as required by law.\n'
            '8. We implement security measures to protect your data.\n'
            '9. We may send you booking confirmations and updates.\n'
            '10. You can opt out of marketing communications.\n\n'
            'For our complete privacy policy, please visit our website.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Future<void> _proceedToPayment() async {
    if (_bookingSummary == null) return;

    setState(() => _isLoading = true);

    try {
      if (mounted) {
        final result = await Navigator.of(context).push<bool>(
          MaterialPageRoute(
            builder: (context) => PaymentScreen(
              amount: _bookingSummary!.totalPrice,
              currency: _bookingSummary!.flight.currency,
              description:
                  'Flight Booking - ${_bookingSummary!.flight.flightNumber}',
            ),
          ),
        );

        if (result == true && mounted) {
          // Payment successful
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Flight booking confirmed! You will receive a confirmation email shortly.'),
              backgroundColor: Colors.green,
            ),
          );

          // Navigate back to home or booking management
          Navigator.of(context).popUntil((route) => route.isFirst);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Payment failed: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
