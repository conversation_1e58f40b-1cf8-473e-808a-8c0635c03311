// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

// Project imports
import 'package:culture_connect/models/travel/flight_search_params.dart';
import 'package:culture_connect/models/travel/flight.dart';
import 'package:culture_connect/services/travel/flight_search_service.dart';
import 'package:culture_connect/screens/travel/flight/flight_list_screen.dart';

import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Screen for searching flights with advanced filters
class FlightSearchScreen extends ConsumerStatefulWidget {
  /// Creates a new flight search screen
  const FlightSearchScreen({super.key});

  @override
  ConsumerState<FlightSearchScreen> createState() => _FlightSearchScreenState();
}

class _FlightSearchScreenState extends ConsumerState<FlightSearchScreen>
    with TickerProviderStateMixin {
  // Controllers and state
  final _formKey = GlobalKey<FormState>();
  final _originController = TextEditingController();
  final _destinationController = TextEditingController();

  // Animation controllers
  late AnimationController _filterAnimationController;
  late Animation<double> _filterAnimation;

  // Search parameters
  TripType _tripType = TripType.roundTrip;
  Airport? _originAirport;
  Airport? _destinationAirport;
  DateTime? _departureDate;
  DateTime? _returnDate;
  FlightClass _flightClass = FlightClass.economy;
  int _adultCount = 1;
  int _childCount = 0;
  int _infantCount = 0;
  bool _directFlightsOnly = false;
  bool _flexibleDates = false;
  final List<String> _preferredAirlines = [];

  // UI state
  bool _isSearching = false;
  bool _showAdvancedFilters = false;
  List<Airport> _airports = [];
  List<Airport> _filteredOriginAirports = [];
  List<Airport> _filteredDestinationAirports = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadAirports();
  }

  @override
  void dispose() {
    _originController.dispose();
    _destinationController.dispose();
    _filterAnimationController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _filterAnimationController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );
    _filterAnimation = CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeInOut,
    );
  }

  Future<void> _loadAirports() async {
    try {
      final flightService = FlightSearchService();
      final airports = await flightService.getAirports();
      if (mounted) {
        setState(() {
          _airports = airports;
          _filteredOriginAirports = airports;
          _filteredDestinationAirports = airports;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load airports: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Search Flights'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
      ),
      body: _errorMessage != null
          ? Center(
              child: ErrorView(
                error: _errorMessage!,
                onRetry: _loadAirports,
              ),
            )
          : _buildSearchForm(theme),
    );
  }

  Widget _buildSearchForm(ThemeData theme) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTripTypeSelector(theme),
            const SizedBox(height: 24),
            _buildAirportSelectors(theme),
            const SizedBox(height: 24),
            _buildDateSelectors(theme),
            const SizedBox(height: 24),
            _buildPassengerSelector(theme),
            const SizedBox(height: 24),
            _buildFlightClassSelector(theme),
            const SizedBox(height: 16),
            _buildAdvancedFiltersToggle(theme),
            _buildAdvancedFilters(theme),
            const SizedBox(height: 32),
            _buildSearchButton(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildTripTypeSelector(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trip Type',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildTripTypeOption(
                    theme,
                    TripType.oneWay,
                    'One Way',
                    Icons.flight_takeoff,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildTripTypeOption(
                    theme,
                    TripType.roundTrip,
                    'Round Trip',
                    Icons.flight,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildTripTypeOption(
                    theme,
                    TripType.multiCity,
                    'Multi-City',
                    Icons.connecting_airports,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTripTypeOption(
    ThemeData theme,
    TripType type,
    String label,
    IconData icon,
  ) {
    final isSelected = _tripType == type;
    return InkWell(
      onTap: () {
        setState(() {
          _tripType = type;
          if (type == TripType.oneWay) {
            _returnDate = null;
          }
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primary.withAlpha(26)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withAlpha(77),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface.withAlpha(153),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withAlpha(153),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAirportSelectors(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Route',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _swapAirports,
                  icon: const Icon(Icons.swap_horiz),
                  tooltip: 'Swap airports',
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildAirportField(
              theme,
              'From',
              _originController,
              _originAirport,
              _filteredOriginAirports,
              (airport) => _selectOriginAirport(airport),
              (query) => _filterOriginAirports(query),
            ),
            const SizedBox(height: 16),
            _buildAirportField(
              theme,
              'To',
              _destinationController,
              _destinationAirport,
              _filteredDestinationAirports,
              (airport) => _selectDestinationAirport(airport),
              (query) => _filterDestinationAirports(query),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAirportField(
    ThemeData theme,
    String label,
    TextEditingController controller,
    Airport? selectedAirport,
    List<Airport> filteredAirports,
    Function(Airport) onAirportSelected,
    Function(String) onQueryChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: 'Search airports...',
            prefixIcon: const Icon(Icons.flight_takeoff),
            suffixIcon: selectedAirport != null
                ? IconButton(
                    onPressed: () =>
                        _clearAirportSelection(controller, onAirportSelected),
                    icon: const Icon(Icons.clear),
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          onChanged: onQueryChanged,
          validator: (value) {
            if (selectedAirport == null) {
              return 'Please select an airport';
            }
            return null;
          },
        ),
        if (controller.text.isNotEmpty && selectedAirport == null)
          Container(
            margin: const EdgeInsets.only(top: 4),
            constraints: const BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              border:
                  Border.all(color: theme.colorScheme.outline.withAlpha(77)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: filteredAirports.length,
              itemBuilder: (context, index) {
                final airport = filteredAirports[index];
                return ListTile(
                  dense: true,
                  leading: Icon(
                    Icons.flight,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  title: Text(
                    '${airport.code} - ${airport.name}',
                    style: theme.textTheme.bodyMedium,
                  ),
                  subtitle: Text(
                    '${airport.city}, ${airport.country}',
                    style: theme.textTheme.bodySmall,
                  ),
                  onTap: () => onAirportSelected(airport),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildDateSelectors(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Travel Dates',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildDateField(
                    theme,
                    'Departure',
                    _departureDate,
                    (date) => _selectDepartureDate(date),
                  ),
                ),
                if (_tripType == TripType.roundTrip) ...[
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildDateField(
                      theme,
                      'Return',
                      _returnDate,
                      (date) => _selectReturnDate(date),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField(
    ThemeData theme,
    String label,
    DateTime? selectedDate,
    Function(DateTime) onDateSelected,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _showDatePicker(onDateSelected, selectedDate),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              border:
                  Border.all(color: theme.colorScheme.outline.withAlpha(77)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    selectedDate != null
                        ? DateFormat('MMM dd, yyyy').format(selectedDate)
                        : 'Select date',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: selectedDate != null
                          ? theme.colorScheme.onSurface
                          : theme.colorScheme.onSurface.withAlpha(153),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPassengerSelector(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Passengers',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildPassengerCounter(theme, 'Adults', _adultCount, (count) {
              setState(() => _adultCount = count);
            }),
            _buildPassengerCounter(theme, 'Children (2-11)', _childCount,
                (count) {
              setState(() => _childCount = count);
            }),
            _buildPassengerCounter(theme, 'Infants (under 2)', _infantCount,
                (count) {
              setState(() => _infantCount = count);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildPassengerCounter(
    ThemeData theme,
    String label,
    int count,
    Function(int) onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: theme.textTheme.bodyMedium,
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: count > 0 ? () => onChanged(count - 1) : null,
                icon: const Icon(Icons.remove_circle_outline),
              ),
              Container(
                width: 40,
                alignment: Alignment.center,
                child: Text(
                  count.toString(),
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                onPressed: count < 9 ? () => onChanged(count + 1) : null,
                icon: const Icon(Icons.add_circle_outline),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFlightClassSelector(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Class',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: FlightClass.values.map((flightClass) {
                final isSelected = _flightClass == flightClass;
                return ChoiceChip(
                  label: Text(_getFlightClassLabel(flightClass)),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() => _flightClass = flightClass);
                    }
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedFiltersToggle(ThemeData theme) {
    return InkWell(
      onTap: () {
        setState(() {
          _showAdvancedFilters = !_showAdvancedFilters;
          if (_showAdvancedFilters) {
            _filterAnimationController.forward();
          } else {
            _filterAnimationController.reverse();
          }
        });
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Icon(
              _showAdvancedFilters ? Icons.expand_less : Icons.expand_more,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              'Advanced Filters',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedFilters(ThemeData theme) {
    return SizeTransition(
      sizeFactor: _filterAnimation,
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SwitchListTile(
                title: const Text('Direct flights only'),
                subtitle: const Text('No layovers or connections'),
                value: _directFlightsOnly,
                onChanged: (value) {
                  setState(() => _directFlightsOnly = value);
                },
              ),
              SwitchListTile(
                title: const Text('Flexible dates'),
                subtitle: const Text('Show flights ±3 days'),
                value: _flexibleDates,
                onChanged: (value) {
                  setState(() => _flexibleDates = value);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchButton(ThemeData theme) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _isSearching ? null : _performSearch,
        icon: _isSearching
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.search),
        label: Text(_isSearching ? 'Searching...' : 'Search Flights'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  // Helper methods
  void _swapAirports() {
    setState(() {
      final tempAirport = _originAirport;
      final tempController = _originController.text;

      _originAirport = _destinationAirport;
      _destinationAirport = tempAirport;

      _originController.text = _destinationController.text;
      _destinationController.text = tempController;
    });
  }

  void _selectOriginAirport(Airport airport) {
    setState(() {
      _originAirport = airport;
      _originController.text = '${airport.code} - ${airport.name}';
      _filteredOriginAirports = [];
    });
  }

  void _selectDestinationAirport(Airport airport) {
    setState(() {
      _destinationAirport = airport;
      _destinationController.text = '${airport.code} - ${airport.name}';
      _filteredDestinationAirports = [];
    });
  }

  void _filterOriginAirports(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredOriginAirports = [];
        _originAirport = null;
      } else {
        _filteredOriginAirports = _airports
            .where((airport) =>
                airport.name.toLowerCase().contains(query.toLowerCase()) ||
                airport.code.toLowerCase().contains(query.toLowerCase()) ||
                airport.city.toLowerCase().contains(query.toLowerCase()))
            .take(10)
            .toList();
      }
    });
  }

  void _filterDestinationAirports(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredDestinationAirports = [];
        _destinationAirport = null;
      } else {
        _filteredDestinationAirports = _airports
            .where((airport) =>
                airport.name.toLowerCase().contains(query.toLowerCase()) ||
                airport.code.toLowerCase().contains(query.toLowerCase()) ||
                airport.city.toLowerCase().contains(query.toLowerCase()))
            .take(10)
            .toList();
      }
    });
  }

  void _clearAirportSelection(
      TextEditingController controller, Function(Airport) onAirportSelected) {
    setState(() {
      controller.clear();
      if (controller == _originController) {
        _originAirport = null;
        _filteredOriginAirports = [];
      } else {
        _destinationAirport = null;
        _filteredDestinationAirports = [];
      }
    });
  }

  void _selectDepartureDate(DateTime date) {
    setState(() {
      _departureDate = date;
      if (_returnDate != null && _returnDate!.isBefore(date)) {
        _returnDate = date.add(const Duration(days: 1));
      }
    });
  }

  void _selectReturnDate(DateTime date) {
    setState(() {
      _returnDate = date;
    });
  }

  Future<void> _showDatePicker(
      Function(DateTime) onDateSelected, DateTime? selectedDate) async {
    final initialDate =
        selectedDate ?? DateTime.now().add(const Duration(days: 1));
    final firstDate = DateTime.now();
    final lastDate = DateTime.now().add(const Duration(days: 365));

    final pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate.isBefore(firstDate) ? firstDate : initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).colorScheme.primary,
                ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null && mounted) {
      onDateSelected(pickedDate);
    }
  }

  String _getFlightClassLabel(FlightClass flightClass) {
    switch (flightClass) {
      case FlightClass.economy:
        return 'Economy';
      case FlightClass.premiumEconomy:
        return 'Premium Economy';
      case FlightClass.business:
        return 'Business';
      case FlightClass.first:
        return 'First';
    }
  }

  Future<void> _performSearch() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_originAirport == null || _destinationAirport == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please select origin and destination airports')),
      );
      return;
    }

    if (_departureDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select departure date')),
      );
      return;
    }

    if (_tripType == TripType.roundTrip && _returnDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please select return date for round trip')),
      );
      return;
    }

    setState(() => _isSearching = true);

    try {
      final routes = <FlightRoute>[
        FlightRoute(
          originCode: _originAirport!.code,
          originName: _originAirport!.name,
          originCity: _originAirport!.city,
          destinationCode: _destinationAirport!.code,
          destinationName: _destinationAirport!.name,
          destinationCity: _destinationAirport!.city,
          departureDate: _departureDate!,
        ),
      ];

      if (_tripType == TripType.roundTrip && _returnDate != null) {
        routes.add(
          FlightRoute(
            originCode: _destinationAirport!.code,
            originName: _destinationAirport!.name,
            originCity: _destinationAirport!.city,
            destinationCode: _originAirport!.code,
            destinationName: _originAirport!.name,
            destinationCity: _originAirport!.city,
            departureDate: _returnDate!,
          ),
        );
      }

      final passengers = <Passenger>[];
      if (_adultCount > 0) {
        passengers
            .add(Passenger(type: PassengerType.adult, count: _adultCount));
      }
      if (_childCount > 0) {
        passengers
            .add(Passenger(type: PassengerType.child, count: _childCount));
      }
      if (_infantCount > 0) {
        passengers
            .add(Passenger(type: PassengerType.infant, count: _infantCount));
      }

      final searchParams = FlightSearchParams(
        tripType: _tripType,
        routes: routes,
        passengers: passengers,
        flightClass: _flightClass,
        directFlightsOnly: _directFlightsOnly,
        flexibleDates: _flexibleDates,
        preferredAirlines:
            _preferredAirlines.isNotEmpty ? _preferredAirlines : null,
      );

      // Perform the actual search
      final flightService = FlightSearchService();
      await flightService.searchFlights(searchParams);

      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const FlightListScreen(),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Search failed: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSearching = false);
      }
    }
  }
}
