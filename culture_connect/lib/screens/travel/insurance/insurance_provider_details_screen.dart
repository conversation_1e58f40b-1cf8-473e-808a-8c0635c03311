// Dart SDK imports
import 'dart:async';

// Flutter imports
import 'package:flutter/material.dart';

// Third-party package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart';
import 'package:culture_connect/providers/travel/insurance/insurance_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/insurance/insurance_policy_card.dart';

/// A screen for displaying insurance provider details
class InsuranceProviderDetailsScreen extends ConsumerWidget {
  /// The ID of the provider to display
  final String providerId;

  /// Creates a new insurance provider details screen
  const InsuranceProviderDetailsScreen({
    super.key,
    required this.providerId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final providerAsync = ref.watch(providerProvider(providerId));
    final availablePoliciesAsync = ref.watch(availablePoliciesProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Provider Details',
        showBackButton: true,
      ),
      body: providerAsync.when(
        data: (provider) {
          if (provider == null) {
            return Center(
              child: Text(
                'Provider not found',
                style: theme.textTheme.titleMedium,
              ),
            );
          }

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Provider header
                _buildProviderHeader(context, theme, provider),

                // Provider details
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Description
                      Text(
                        'About',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        provider.description,
                        style: theme.textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 24),

                      // Contact information
                      Text(
                        'Contact Information',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildContactItem(
                        theme,
                        Icons.phone,
                        'Phone',
                        provider.phoneNumber,
                        () => _launchUrl('tel:${provider.phoneNumber}'),
                      ),
                      const SizedBox(height: 16),
                      _buildContactItem(
                        theme,
                        Icons.email,
                        'Email',
                        provider.email,
                        () => _launchUrl('mailto:${provider.email}'),
                      ),
                      const SizedBox(height: 16),
                      _buildContactItem(
                        theme,
                        Icons.language,
                        'Website',
                        provider.websiteUrl,
                        () => _launchUrl(provider.websiteUrl),
                      ),
                      const SizedBox(height: 24),

                      // Countries
                      Text(
                        'Available Countries',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: provider.countries.map((country) {
                          return Chip(
                            label: Text(country),
                            backgroundColor:
                                theme.colorScheme.surfaceContainerLow,
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 24),

                      // Available policies
                      Text(
                        'Available Policies',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      availablePoliciesAsync.when(
                        data: (policies) {
                          final providerPolicies = policies
                              .where(
                                  (policy) => policy.provider.id == provider.id)
                              .toList();

                          if (providerPolicies.isEmpty) {
                            return Center(
                              child: Text(
                                'No policies available from this provider',
                                style: theme.textTheme.bodyMedium,
                              ),
                            );
                          }

                          return Column(
                            children: providerPolicies.map((policy) {
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: InsurancePolicyCard(
                                  policy: policy,
                                  showDetails: true,
                                  showStatus: false,
                                  showProvider: false,
                                  showCoverage: true,
                                  showDates: false,
                                  showPrice: true,
                                  showActions: true,
                                  onTap: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/travel/insurance/policy',
                                      arguments: policy.id,
                                    );
                                  },
                                  onViewDetails: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/travel/insurance/policy',
                                      arguments: policy.id,
                                    );
                                  },
                                  onPurchase: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/travel/insurance/purchase',
                                      arguments: policy.id,
                                    );
                                  },
                                ),
                              );
                            }).toList(),
                          );
                        },
                        loading: () => const Center(
                          child: CircularProgressIndicator(),
                        ),
                        error: (error, stackTrace) => Center(
                          child: Text(
                            'Error loading policies: $error',
                            style: TextStyle(color: theme.colorScheme.error),
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Reviews
                      Text(
                        'Reviews',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildReviewSummary(theme, provider),
                      const SizedBox(height: 16),

                      // Sample reviews
                      _buildSampleReviews(theme),

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () => ref.refresh(providerProvider(providerId)),
          ),
        ),
      ),
    );
  }

  Widget _buildProviderHeader(
      BuildContext context, ThemeData theme, InsuranceProvider provider) {
    return Container(
      width: double.infinity,
      color: theme.colorScheme.surfaceContainerHigh,
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Logo
          ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Image.network(
              provider.logoUrl,
              width: 120,
              height: 120,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 120,
                  height: 120,
                  color: provider.primaryColor.withAlpha(25), // 0.1 * 255 = 25
                  child: Icon(
                    Icons.business,
                    size: 64,
                    color: provider.primaryColor,
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),

          // Name
          Text(
            provider.name,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),

          // Rating
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ...List.generate(5, (index) {
                return Icon(
                  index < provider.rating.floor()
                      ? Icons.star
                      : index < provider.rating
                          ? Icons.star_half
                          : Icons.star_border,
                  color: Colors.amber,
                  size: 24,
                );
              }),
              const SizedBox(width: 8),
              Text(
                '${provider.rating} (${provider.reviewCount} reviews)',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Tags
          Wrap(
            alignment: WrapAlignment.center,
            spacing: 8,
            runSpacing: 8,
            children: [
              if (provider.isPartner)
                Chip(
                  label: const Text('Partner'),
                  backgroundColor: theme.colorScheme.primaryContainer,
                  labelStyle: TextStyle(
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
              if (provider.isFeatured)
                Chip(
                  label: const Text('Featured'),
                  backgroundColor: Colors.amber.withAlpha(51), // 0.2 * 255 = 51
                  labelStyle: TextStyle(
                    color: Colors.amber[800],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem(
    ThemeData theme,
    IconData icon,
    String label,
    String value,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: theme.colorScheme.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReviewSummary(ThemeData theme, InsuranceProvider provider) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerLow,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Text(
                provider.rating.toString(),
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: List.generate(5, (index) {
                  return Icon(
                    index < provider.rating.floor()
                        ? Icons.star
                        : index < provider.rating
                            ? Icons.star_half
                            : Icons.star_border,
                    color: Colors.amber,
                    size: 16,
                  );
                }),
              ),
              const SizedBox(height: 4),
              Text(
                '${provider.reviewCount} reviews',
                style: theme.textTheme.bodySmall,
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            children: [
              _buildRatingBar(theme, 5, 0.7),
              const SizedBox(height: 8),
              _buildRatingBar(theme, 4, 0.2),
              const SizedBox(height: 8),
              _buildRatingBar(theme, 3, 0.05),
              const SizedBox(height: 8),
              _buildRatingBar(theme, 2, 0.03),
              const SizedBox(height: 8),
              _buildRatingBar(theme, 1, 0.02),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRatingBar(ThemeData theme, int rating, double percentage) {
    return Row(
      children: [
        Text(
          '$rating',
          style: theme.textTheme.bodySmall,
        ),
        const SizedBox(width: 4),
        const Icon(
          Icons.star,
          size: 12,
          color: Colors.amber,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(2),
            child: LinearProgressIndicator(
              value: percentage,
              backgroundColor: theme.colorScheme.surfaceContainerLowest,
              valueColor: AlwaysStoppedAnimation<Color>(
                rating >= 4
                    ? Colors.green
                    : rating >= 3
                        ? Colors.amber
                        : Colors.red,
              ),
              minHeight: 8,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${(percentage * 100).toInt()}%',
          style: theme.textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildSampleReviews(ThemeData theme) {
    // In a real app, these would come from an API
    final sampleReviews = [
      {
        'name': 'John D.',
        'rating': 5,
        'date': '2 months ago',
        'comment':
            'Excellent service! The claim process was quick and hassle-free. Highly recommended.',
      },
      {
        'name': 'Sarah M.',
        'rating': 4,
        'date': '3 months ago',
        'comment':
            'Good coverage for the price. Customer service was helpful when I needed assistance.',
      },
      {
        'name': 'Robert L.',
        'rating': 5,
        'date': '1 month ago',
        'comment':
            'Saved me a lot of trouble when my flight was cancelled. Will definitely use again.',
      },
    ];

    return Column(
      children: sampleReviews.map((review) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: theme.colorScheme.primary,
                        child: Text(
                          (review['name'] as String).substring(0, 1),
                          style: TextStyle(
                            color: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              review['name'] as String,
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              review['date'] as String,
                              style: theme.textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: List.generate(5, (index) {
                          return Icon(
                            index < (review['rating'] as int)
                                ? Icons.star
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 16,
                          );
                        }),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    review['comment'] as String,
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        debugPrint('Could not launch $url');
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }
}
