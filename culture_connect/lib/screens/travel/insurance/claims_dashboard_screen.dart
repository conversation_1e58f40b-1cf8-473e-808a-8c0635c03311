// Dart SDK imports
import 'dart:async';

// Flutter imports
import 'package:flutter/material.dart';

// Third-party package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart'
    hide InsuranceCoverageType, InsuranceClaimStatus;
import 'package:culture_connect/models/travel/insurance/insurance_claim_status.dart';
import 'package:culture_connect/providers/travel/insurance/insurance_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/insurance/insurance_claim_card.dart';

/// A screen for displaying the claims dashboard
class ClaimsDashboardScreen extends ConsumerStatefulWidget {
  /// Creates a new claims dashboard screen
  const ClaimsDashboardScreen({super.key});

  @override
  ConsumerState<ClaimsDashboardScreen> createState() =>
      _ClaimsDashboardScreenState();
}

class _ClaimsDashboardScreenState extends ConsumerState<ClaimsDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  InsuranceClaimStatus? _selectedStatusFilter;
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final claimsAsync = ref.watch(claimsProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Claims Dashboard',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: claimsAsync.when(
        data: (claims) {
          if (claims.isEmpty) {
            return _buildEmptyState(theme);
          }

          final filteredClaims = _applyFilters(claims);
          return _buildClaimsContent(theme, filteredClaims);
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () => ref.refresh(claimsProvider),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _navigateToNewClaim,
        icon: const Icon(Icons.add),
        label: const Text('New Claim'),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_outlined,
              size: 80,
              color: theme.colorScheme.onSurface.withAlpha(128),
            ),
            const SizedBox(height: 24),
            Text(
              'No Claims Yet',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'You haven\'t filed any insurance claims yet. If you need to file a claim, tap the button below.',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(179),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _navigateToNewClaim,
              icon: const Icon(Icons.add),
              label: const Text('File Your First Claim'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClaimsContent(ThemeData theme, List<InsuranceClaim> claims) {
    return Column(
      children: [
        // Statistics overview
        _buildStatisticsOverview(theme, claims),

        // Filter chips
        if (_selectedStatusFilter != null || _selectedDateRange != null)
          _buildActiveFilters(theme),

        // Tab bar
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Active'),
            Tab(text: 'Resolved'),
            Tab(text: 'All'),
          ],
        ),

        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildClaimsList(theme, _getActiveClaims(claims)),
              _buildClaimsList(theme, _getResolvedClaims(claims)),
              _buildClaimsList(theme, claims),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsOverview(
      ThemeData theme, List<InsuranceClaim> claims) {
    final activeClaims = _getActiveClaims(claims);
    final resolvedClaims = _getResolvedClaims(claims);
    final totalClaimAmount = claims.fold<double>(
      0.0,
      (sum, claim) => sum + claim.claimAmount,
    );
    final approvedAmount =
        claims.where((claim) => claim.approvedAmount != null).fold<double>(
              0.0,
              (sum, claim) => sum + (claim.approvedAmount ?? 0.0),
            );

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Claims Overview',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  theme,
                  'Active Claims',
                  '${activeClaims.length}',
                  Icons.pending_actions,
                  theme.colorScheme.primary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  theme,
                  'Resolved',
                  '${resolvedClaims.length}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  theme,
                  'Total Claimed',
                  '\$${totalClaimAmount.toStringAsFixed(0)}',
                  Icons.attach_money,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  theme,
                  'Approved',
                  '\$${approvedAmount.toStringAsFixed(0)}',
                  Icons.verified,
                  Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    ThemeData theme,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: color,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(179),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveFilters(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Wrap(
        spacing: 8,
        children: [
          if (_selectedStatusFilter != null)
            Chip(
              label: Text(_selectedStatusFilter!.displayName),
              onDeleted: () {
                setState(() {
                  _selectedStatusFilter = null;
                });
              },
              backgroundColor: _selectedStatusFilter!.color.withAlpha(51),
            ),
          if (_selectedDateRange != null)
            Chip(
              label: Text(
                '${_selectedDateRange!.start.day}/${_selectedDateRange!.start.month} - ${_selectedDateRange!.end.day}/${_selectedDateRange!.end.month}',
              ),
              onDeleted: () {
                setState(() {
                  _selectedDateRange = null;
                });
              },
            ),
        ],
      ),
    );
  }

  Widget _buildClaimsList(ThemeData theme, List<InsuranceClaim> claims) {
    if (claims.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 64,
                color: theme.colorScheme.onSurface.withAlpha(128),
              ),
              const SizedBox(height: 16),
              Text(
                'No claims found',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'Try adjusting your filters or file a new claim.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(179),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: claims.length,
      itemBuilder: (context, index) {
        final claim = claims[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: InsuranceClaimCard(
            claim: claim,
            showDetails: true,
            showStatus: true,
            showPolicy: true,
            showDates: true,
            showAmount: true,
            showActions: true,
            onTap: () => _navigateToClaimDetails(claim.id),
            onViewDetails: () => _navigateToClaimDetails(claim.id),
            onUpdate: claim.status == InsuranceClaimStatus.infoRequested
                ? () => _navigateToClaimUpdate(claim.id)
                : null,
          ),
        );
      },
    );
  }

  List<InsuranceClaim> _applyFilters(List<InsuranceClaim> claims) {
    var filteredClaims = claims;

    if (_selectedStatusFilter != null) {
      filteredClaims = filteredClaims
          .where((claim) => claim.status == _selectedStatusFilter)
          .toList();
    }

    if (_selectedDateRange != null) {
      filteredClaims = filteredClaims.where((claim) {
        return claim.submittedDate.isAfter(_selectedDateRange!.start) &&
            claim.submittedDate.isBefore(_selectedDateRange!.end);
      }).toList();
    }

    return filteredClaims;
  }

  List<InsuranceClaim> _getActiveClaims(List<InsuranceClaim> claims) {
    return claims
        .where((claim) =>
            claim.status == InsuranceClaimStatus.submitted ||
            claim.status == InsuranceClaimStatus.inReview ||
            claim.status == InsuranceClaimStatus.infoRequested)
        .toList();
  }

  List<InsuranceClaim> _getResolvedClaims(List<InsuranceClaim> claims) {
    return claims
        .where((claim) =>
            claim.status == InsuranceClaimStatus.approved ||
            claim.status == InsuranceClaimStatus.denied ||
            claim.status == InsuranceClaimStatus.paid ||
            claim.status == InsuranceClaimStatus.partiallyPaid)
        .toList();
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Claims'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<InsuranceClaimStatus>(
              decoration: const InputDecoration(
                labelText: 'Status',
                border: OutlineInputBorder(),
              ),
              value: _selectedStatusFilter,
              items: InsuranceClaimStatus.values
                  .map((status) => DropdownMenuItem(
                        value: status,
                        child: Text(status.displayName),
                      ))
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStatusFilter = value;
                });
              },
            ),
            const SizedBox(height: 16),
            OutlinedButton(
              onPressed: _selectDateRange,
              child: Text(
                _selectedDateRange == null
                    ? 'Select Date Range'
                    : '${_selectedDateRange!.start.day}/${_selectedDateRange!.start.month} - ${_selectedDateRange!.end.day}/${_selectedDateRange!.end.month}',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedStatusFilter = null;
                _selectedDateRange = null;
              });
              Navigator.pop(context);
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDateRange() async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );

    if (dateRange != null && mounted) {
      setState(() {
        _selectedDateRange = dateRange;
      });
    }
  }

  void _navigateToNewClaim() {
    Navigator.pushNamed(context, '/travel/insurance/claim/new');
  }

  void _navigateToClaimDetails(String claimId) {
    Navigator.pushNamed(
      context,
      '/travel/insurance/claim',
      arguments: claimId,
    );
  }

  void _navigateToClaimUpdate(String claimId) {
    Navigator.pushNamed(
      context,
      '/travel/insurance/claim/update',
      arguments: claimId,
    );
  }
}
