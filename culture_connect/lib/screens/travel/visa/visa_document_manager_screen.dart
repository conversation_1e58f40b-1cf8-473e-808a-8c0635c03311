// Flutter imports
import 'dart:io';
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

// Project imports
import 'package:culture_connect/models/travel/document/visa_requirement.dart';
import 'package:culture_connect/models/travel/document/visa_service_provider.dart';
import 'package:culture_connect/services/travel/visa/visa_document_manager_service.dart';
import 'package:culture_connect/services/travel/visa/visa_routing_service.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';

/// Screen for conditional document upload and management
class VisaDocumentManagerScreen extends ConsumerStatefulWidget {
  /// Creates a new visa document manager screen
  const VisaDocumentManagerScreen({
    super.key,
    required this.applicationId,
    required this.visaRequirement,
    required this.serviceRoute,
    this.selectedProvider,
  });

  /// Visa application ID
  final String applicationId;

  /// Visa requirement details
  final VisaRequirement visaRequirement;

  /// Selected service route
  final VisaServiceRoute serviceRoute;

  /// Selected provider (if applicable)
  final VisaServiceProvider? selectedProvider;

  @override
  ConsumerState<VisaDocumentManagerScreen> createState() =>
      _VisaDocumentManagerScreenState();
}

class _VisaDocumentManagerScreenState
    extends ConsumerState<VisaDocumentManagerScreen> {
  final VisaDocumentManagerService _documentService =
      VisaDocumentManagerService();
  final ImagePicker _imagePicker = ImagePicker();

  // State
  List<VisaDocument> _documents = [];
  List<String> _requiredDocuments = [];
  bool _isLoading = true;
  String? _errorMessage;
  final Map<String, double> _uploadProgress = {};

  @override
  void initState() {
    super.initState();
    _loadDocuments();
  }

  Future<void> _loadDocuments() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _documentService.initialize();

      // Check if document management is required for this service route
      if (!_documentService.isDocumentManagementRequired(widget.serviceRoute)) {
        if (mounted) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Document management not required for self-service applications'),
            ),
          );
        }
        return;
      }

      final documents =
          _documentService.getApplicationDocuments(widget.applicationId);
      final requiredDocs =
          _documentService.getRequiredDocuments(widget.visaRequirement);

      if (mounted) {
        setState(() {
          _documents = documents;
          _requiredDocuments = requiredDocs;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load documents: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Document Manager'),
        actions: [
          if (_documents.isNotEmpty)
            IconButton(
              onPressed: _showUploadOptions,
              icon: const Icon(Icons.add),
              tooltip: 'Add document',
            ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LoadingIndicator(),
            SizedBox(height: 16),
            Text('Loading documents...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return ErrorView(
        error: _errorMessage!,
        onRetry: _loadDocuments,
      );
    }

    return Column(
      children: [
        _buildServiceInfo(theme),
        _buildRequiredDocuments(theme),
        Expanded(
          child: _buildDocumentsList(theme),
        ),
      ],
    );
  }

  Widget _buildServiceInfo(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getServiceRouteIcon(widget.serviceRoute),
                color: theme.colorScheme.onPrimaryContainer,
              ),
              const SizedBox(width: 8),
              Text(
                widget.serviceRoute.displayName,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.serviceRoute.description,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onPrimaryContainer,
            ),
          ),
          if (widget.selectedProvider != null) ...[
            const SizedBox(height: 8),
            Text(
              'Provider: ${widget.selectedProvider!.name}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRequiredDocuments(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Required Documents',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: _requiredDocuments.map((doc) {
              final isUploaded = _documents.any((d) => d.type == doc);
              return Chip(
                label: Text(doc),
                backgroundColor: isUploaded
                    ? theme.colorScheme.primaryContainer
                    : theme.colorScheme.surfaceContainerHighest,
                side: BorderSide(
                  color: isUploaded
                      ? theme.colorScheme.primary
                      : theme.colorScheme.outline,
                ),
                avatar: Icon(
                  isUploaded
                      ? Icons.check_circle
                      : Icons.radio_button_unchecked,
                  size: 16,
                  color: isUploaded
                      ? theme.colorScheme.primary
                      : theme.colorScheme.outline,
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildDocumentsList(ThemeData theme) {
    if (_documents.isEmpty) {
      return _buildEmptyState(theme);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _documents.length,
      itemBuilder: (context, index) {
        final document = _documents[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildDocumentCard(document, theme),
        );
      },
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.upload_file,
            size: 64,
            color: theme.colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No documents uploaded',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Upload your required documents to proceed',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.outline,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          FilledButton.icon(
            onPressed: _showUploadOptions,
            icon: const Icon(Icons.add),
            label: const Text('Upload Document'),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentCard(VisaDocument document, ThemeData theme) {
    final statusColor =
        Color(int.parse('0xFF${document.status.colorHex.substring(1)}'));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: statusColor.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getDocumentIcon(document.type),
                    color: statusColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        document.name,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: statusColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              document.status.displayName,
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            document.formattedFileSize,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.outline,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => _showDocumentOptions(document),
                  icon: const Icon(Icons.more_vert),
                ),
              ],
            ),
            if (document.status == DocumentUploadStatus.uploading) ...[
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: document.uploadProgress,
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation<Color>(statusColor),
              ),
              const SizedBox(height: 4),
              Text(
                '${(document.uploadProgress * 100).toInt()}% uploaded',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.outline,
                ),
              ),
            ],
            if (document.providerNotes != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.note,
                      size: 16,
                      color: theme.colorScheme.outline,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        document.providerNotes!,
                        style: theme.textTheme.bodySmall,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getServiceRouteIcon(VisaServiceRoute route) {
    switch (route) {
      case VisaServiceRoute.selfService:
        return Icons.person;
      case VisaServiceRoute.providerAssisted:
        return Icons.support_agent;
      case VisaServiceRoute.hybrid:
        return Icons.group_work;
      case VisaServiceRoute.providerRequired:
        return Icons.business_center;
    }
  }

  IconData _getDocumentIcon(String type) {
    switch (type.toLowerCase()) {
      case 'passport':
        return Icons.book;
      case 'photo':
      case 'passport photo':
        return Icons.photo_camera;
      case 'bank statement':
        return Icons.account_balance;
      case 'travel itinerary':
        return Icons.flight;
      case 'hotel booking':
      case 'accommodation proof':
        return Icons.hotel;
      case 'invitation letter':
        return Icons.mail;
      default:
        return Icons.description;
    }
  }

  void _showUploadOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildUploadOptionsSheet(),
    );
  }

  Widget _buildUploadOptionsSheet() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Upload Document',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          ListTile(
            leading: const Icon(Icons.camera_alt),
            title: const Text('Take Photo'),
            onTap: () {
              Navigator.pop(context);
              _pickDocument(ImageSource.camera);
            },
          ),
          ListTile(
            leading: const Icon(Icons.photo_library),
            title: const Text('Choose from Gallery'),
            onTap: () {
              Navigator.pop(context);
              _pickDocument(ImageSource.gallery);
            },
          ),
        ],
      ),
    );
  }

  Future<void> _pickDocument(ImageSource source) async {
    try {
      final pickedFile = await _imagePicker.pickImage(source: source);
      if (pickedFile != null) {
        await _uploadDocument(File(pickedFile.path));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking document: $e')),
        );
      }
    }
  }

  Future<void> _uploadDocument(File file) async {
    // Show document type selection dialog
    final documentType = await _showDocumentTypeSelectionDialog();
    if (documentType == null) return; // User canceled

    try {
      final document = await _documentService.uploadDocument(
        applicationId: widget.applicationId,
        documentType: documentType,
        file: file,
        isRequired: _requiredDocuments.contains(documentType),
        onProgress: (progress) {
          setState(() {
            _uploadProgress[file.path] = progress;
          });
        },
      );

      setState(() {
        _documents.add(document);
        _uploadProgress.remove(file.path);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Document uploaded successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Upload failed: $e')),
        );
      }
    }
  }

  Future<String?> _showDocumentTypeSelectionDialog() async {
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Document Type'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _requiredDocuments.length,
            itemBuilder: (context, index) {
              final docType = _requiredDocuments[index];
              final isUploaded = _documents.any((d) => d.type == docType);

              return ListTile(
                leading: Icon(
                  _getDocumentIcon(docType),
                  color: isUploaded
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.outline,
                ),
                title: Text(docType),
                subtitle: isUploaded
                    ? Text(
                        'Already uploaded',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      )
                    : null,
                trailing: isUploaded
                    ? Icon(
                        Icons.check_circle,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    : null,
                onTap: () => Navigator.pop(context, docType),
                enabled: !isUploaded,
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 'Other Document'),
            child: const Text('Other'),
          ),
        ],
      ),
    );
  }

  void _showDocumentOptions(VisaDocument document) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildDocumentOptionsSheet(document),
    );
  }

  Widget _buildDocumentOptionsSheet(VisaDocument document) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Document Options',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            document.name,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
          const SizedBox(height: 16),
          ListTile(
            leading: const Icon(Icons.visibility),
            title: const Text('View Document'),
            onTap: () {
              Navigator.pop(context);
              _viewDocument(document);
            },
          ),
          if (document.status == DocumentUploadStatus.failed ||
              document.status == DocumentUploadStatus.rejected)
            ListTile(
              leading: const Icon(Icons.refresh),
              title: const Text('Re-upload'),
              onTap: () {
                Navigator.pop(context);
                _reuploadDocument(document);
              },
            ),
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('Delete Document'),
            textColor: Colors.red,
            onTap: () {
              Navigator.pop(context);
              _confirmDeleteDocument(document);
            },
          ),
        ],
      ),
    );
  }

  void _viewDocument(VisaDocument document) {
    // TODO: Implement document viewer
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing ${document.name} - coming soon!')),
    );
  }

  void _reuploadDocument(VisaDocument document) {
    // TODO: Implement document re-upload
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Re-uploading ${document.name} - coming soon!')),
    );
  }

  Future<void> _confirmDeleteDocument(VisaDocument document) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Document'),
        content: Text('Are you sure you want to delete "${document.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final success = await _documentService.deleteDocument(
        widget.applicationId,
        document.id,
      );

      if (success && mounted) {
        setState(() {
          _documents.removeWhere((d) => d.id == document.id);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Document deleted successfully')),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to delete document')),
        );
      }
    }
  }
}
