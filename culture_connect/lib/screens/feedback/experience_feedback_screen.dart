// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/mood/mood_model.dart';
import 'package:culture_connect/services/mood_tracking_service.dart';
import 'package:culture_connect/widgets/mood/mood_selector_widget.dart';
import 'package:culture_connect/widgets/feedback/satisfaction_widget.dart';
import 'package:culture_connect/widgets/common/mascot_widget.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/providers/achievement_provider.dart';

/// Screen for collecting experience feedback with mood and satisfaction
class ExperienceFeedbackScreen extends ConsumerStatefulWidget {
  /// Experience ID for the feedback
  final String? experienceId;

  /// Booking ID for the feedback
  final String? bookingId;

  /// Whether this is post-experience feedback
  final bool isPostExperience;

  /// Experience title for context
  final String? experienceTitle;

  /// Creates a new experience feedback screen
  const ExperienceFeedbackScreen({
    super.key,
    this.experienceId,
    this.bookingId,
    this.isPostExperience = true,
    this.experienceTitle,
  });

  @override
  ConsumerState<ExperienceFeedbackScreen> createState() =>
      _ExperienceFeedbackScreenState();
}

class _ExperienceFeedbackScreenState
    extends ConsumerState<ExperienceFeedbackScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();

  int _currentStep = 0;
  MoodType? _selectedMood;
  double _satisfactionRating = 0.0;
  List<FeedbackTag> _selectedTags = [];
  String _feedbackText = '';
  bool _isSubmitting = false;
  bool _showCelebration = false;

  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  static const int _totalSteps = 3;

  @override
  void initState() {
    super.initState();

    _progressController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pageController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_showCelebration) {
      return _buildCelebrationScreen();
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: widget.isPostExperience
            ? 'Share Your Experience'
            : 'Quick Feedback',
        showBackButton: true,
        onBackPressed: _handleBackPressed,
      ),
      body: Column(
        children: [
          // Progress indicator
          _buildProgressIndicator(theme),

          // Content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildMoodStep(theme),
                _buildSatisfactionStep(theme),
                _buildSummaryStep(theme),
              ],
            ),
          ),

          // Navigation buttons
          _buildNavigationButtons(theme),
        ],
      ),
    );
  }

  /// Build progress indicator
  Widget _buildProgressIndicator(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                'Step ${_currentStep + 1} of $_totalSteps',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const Spacer(),
              Text(
                '${((_currentStep + 1) / _totalSteps * 100).round()}%',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return LinearProgressIndicator(
                value: (_currentStep + _progressAnimation.value) / _totalSteps,
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                valueColor:
                    AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
              );
            },
          ),
        ],
      ),
    );
  }

  /// Build mood selection step
  Widget _buildMoodStep(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        children: [
          const SizedBox(height: AppTheme.spacingLarge),

          // Mascot
          const MascotWidget(
            size: 80,
          ),

          const SizedBox(height: AppTheme.spacingLarge),

          Text(
            'How are you feeling?',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          if (widget.experienceTitle != null) ...[
            Text(
              'About "${widget.experienceTitle}"',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
          ],

          // Mood selector
          MoodSelectorWidget(
            variant: MoodSelectorVariant.full,
            showLabels: true,
            showMascotPreview: false,
            onMoodSelected: (mood) {
              setState(() {
                _selectedMood = mood;
              });
            },
          ),

          const Spacer(),
        ],
      ),
    );
  }

  /// Build satisfaction rating step
  Widget _buildSatisfactionStep(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        children: [
          const SizedBox(height: AppTheme.spacingLarge),

          Text(
            'Rate Your Experience',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          Text(
            'Help us improve our services',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppTheme.spacingLarge),

          // Satisfaction widget
          Expanded(
            child: SingleChildScrollView(
              child: SatisfactionWidget(
                onRatingChanged: (rating) {
                  setState(() {
                    _satisfactionRating = rating;
                  });
                },
                onTagsChanged: (tags) {
                  setState(() {
                    _selectedTags = tags;
                  });
                },
                onFeedbackChanged: (feedback) {
                  setState(() {
                    _feedbackText = feedback;
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build summary step
  Widget _buildSummaryStep(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        children: [
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'Review Your Feedback',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Mood summary
                  if (_selectedMood != null) ...[
                    _buildSummaryCard(
                      title: 'Your Mood',
                      content: Row(
                        children: [
                          Text(
                            _selectedMood!.emoji,
                            style: const TextStyle(fontSize: 32),
                          ),
                          const SizedBox(width: AppTheme.spacingMedium),
                          Text(
                            _selectedMood!.displayName,
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: _selectedMood!.color,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),
                  ],

                  // Rating summary
                  if (_satisfactionRating > 0) ...[
                    _buildSummaryCard(
                      title: 'Your Rating',
                      content: Row(
                        children: [
                          ...List.generate(5, (index) {
                            return Icon(
                              index < _satisfactionRating
                                  ? Icons.star
                                  : Icons.star_border,
                              color: Colors.amber,
                              size: 24,
                            );
                          }),
                          const SizedBox(width: AppTheme.spacingMedium),
                          Text(
                            '${_satisfactionRating.toStringAsFixed(1)}/5.0',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),
                  ],

                  // Tags summary
                  if (_selectedTags.isNotEmpty) ...[
                    _buildSummaryCard(
                      title: 'Quick Feedback',
                      content: Wrap(
                        spacing: AppTheme.spacingSmall,
                        children: _selectedTags
                            .map((tag) => Chip(
                                  label: Text(tag.displayName),
                                  backgroundColor: tag.color.withAlpha(51),
                                  labelStyle: TextStyle(color: tag.color),
                                ))
                            .toList(),
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),
                  ],

                  // Feedback text summary
                  if (_feedbackText.isNotEmpty) ...[
                    _buildSummaryCard(
                      title: 'Additional Comments',
                      content: Text(
                        _feedbackText,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build summary card
  Widget _buildSummaryCard({
    required String title,
    required Widget content,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            content,
          ],
        ),
      ),
    );
  }

  /// Build navigation buttons
  Widget _buildNavigationButtons(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Row(
        children: [
          if (_currentStep > 0) ...[
            Expanded(
              child: OutlinedButton(
                onPressed: _isSubmitting ? null : _previousStep,
                child: const Text('Previous'),
              ),
            ),
            const SizedBox(width: AppTheme.spacingMedium),
          ],
          Expanded(
            child: ElevatedButton(
              onPressed: _isSubmitting ? null : _nextStep,
              child: _isSubmitting
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(_currentStep == _totalSteps - 1 ? 'Submit' : 'Next'),
            ),
          ),
        ],
      ),
    );
  }

  /// Build celebration screen
  Widget _buildCelebrationScreen() {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle,
              size: 80,
              color: AppTheme.successColor,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            Text(
              'Thank You!',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.successColor,
                  ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              'Your feedback helps us improve',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingLarge * 2),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Done'),
            ),
          ],
        ),
      ),
    );
  }

  /// Handle back button press
  bool _handleBackPressed() {
    if (_currentStep > 0) {
      _previousStep();
      return false; // Don't pop the screen
    }
    return true; // Allow pop
  }

  /// Go to next step
  void _nextStep() async {
    if (_currentStep < _totalSteps - 1) {
      // Validate current step
      if (!_validateCurrentStep()) return;

      setState(() {
        _currentStep++;
      });

      await _progressController.forward();
      await _pageController.nextPage(
        duration: AppTheme.mediumAnimation,
        curve: Curves.easeInOut,
      );
      _progressController.reset();
    } else {
      // Submit feedback
      await _submitFeedback();
    }
  }

  /// Go to previous step
  void _previousStep() async {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });

      await _pageController.previousPage(
        duration: AppTheme.mediumAnimation,
        curve: Curves.easeInOut,
      );
    }
  }

  /// Validate current step
  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        return _selectedMood != null;
      case 1:
        return _satisfactionRating > 0;
      case 2:
        return true; // Summary step is always valid
      default:
        return false;
    }
  }

  /// Submit feedback
  Future<void> _submitFeedback() async {
    setState(() {
      _isSubmitting = true;
    });

    try {
      // Record mood if selected
      if (_selectedMood != null) {
        final moodService = ref.read(moodTrackingServiceProvider);
        await moodService.recordMood(
          _selectedMood!,
          experienceId: widget.experienceId,
          bookingId: widget.bookingId,
          notes: _feedbackText.isNotEmpty ? _feedbackText : null,
          context: {
            'satisfactionRating': _satisfactionRating,
            'tags': _selectedTags.map((tag) => tag.displayName).toList(),
            'isPostExperience': widget.isPostExperience,
          },
        );
      }

      // Track achievement for feedback submission
      final achievementTracker = ref.read(travelServiceActionTrackerProvider);
      await achievementTracker.trackReviewSubmission(
        serviceType: 'experience',
        bookingId: widget.bookingId,
        rating: _satisfactionRating,
      );

      // Trigger haptic feedback
      await HapticFeedback.mediumImpact();

      // Show celebration
      setState(() {
        _showCelebration = true;
        _isSubmitting = false;
      });
    } catch (e) {
      setState(() {
        _isSubmitting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit feedback: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }
}
