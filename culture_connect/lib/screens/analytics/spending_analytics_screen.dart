// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/models/analytics/travel_analytics_model.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen displaying detailed travel spending analytics
class SpendingAnalyticsScreen extends ConsumerStatefulWidget {
  const SpendingAnalyticsScreen({super.key});

  @override
  ConsumerState<SpendingAnalyticsScreen> createState() =>
      _SpendingAnalyticsScreenState();
}

class _SpendingAnalyticsScreenState
    extends ConsumerState<SpendingAnalyticsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  bool _isLoading = true;
  String? _error;
  TravelSpending? _spending;
  String _selectedPeriod = 'Year';

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadSpendingData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _loadSpendingData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // TODO: Load actual spending data from service
      await Future.delayed(const Duration(milliseconds: 1000));

      if (mounted) {
        setState(() {
          _spending = TravelSpending(
            id: 'spending_2024',
            userId: 'demo_user_id',
            totalAmount: 4250.0,
            currency: 'USD',
            categoryBreakdown: {
              TravelServiceCategory.flights: 3200.0,
              TravelServiceCategory.insurance: 650.0,
              TravelServiceCategory.visa: 400.0,
            },
            monthlySpending: {
              'Jan': 800.0,
              'Feb': 0.0,
              'Mar': 1200.0,
              'Apr': 950.0,
              'May': 0.0,
              'Jun': 0.0,
              'Jul': 0.0,
              'Aug': 0.0,
              'Sep': 0.0,
              'Oct': 0.0,
              'Nov': 0.0,
              'Dec': 1300.0,
            },
            totalSavings: 680.0,
            totalBookings: 6,
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
            lastUpdated: DateTime.now(),
          );
          _isLoading = false;
        });

        _animationController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to load spending data: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Spending Analytics',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              Icons.download,
              color: theme.colorScheme.primary,
            ),
            onPressed: () {
              // TODO: Implement export functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Export functionality coming soon!'),
                ),
              );
            },
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator(),
      );
    }

    if (_error != null) {
      return ErrorView(
        error: _error!,
        onRetry: _loadSpendingData,
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: RefreshIndicator(
        onRefresh: _loadSpendingData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPeriodSelector(theme),
              const SizedBox(height: AppTheme.spacingLarge),
              _buildTotalSpendingCard(theme),
              const SizedBox(height: AppTheme.spacingLarge),
              _buildCategoryBreakdown(theme),
              const SizedBox(height: AppTheme.spacingLarge),
              _buildMonthlyTrends(theme),
              const SizedBox(height: AppTheme.spacingLarge),
              _buildSavingsInsights(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodSelector(ThemeData theme) {
    final periods = ['Month', 'Quarter', 'Year'];

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withAlpha(128),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Row(
        children: periods.map((period) {
          final isSelected = period == _selectedPeriod;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedPeriod = period;
                });
                // TODO: Reload data for selected period
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : Colors.transparent,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Text(
                  period,
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isSelected
                        ? Colors.white
                        : theme.colorScheme.onSurfaceVariant,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTotalSpendingCard(ThemeData theme) {
    if (_spending == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withAlpha(204),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withAlpha(77),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Total Spending',
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.white.withAlpha(230),
            ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            _spending!.formattedTotalAmount,
            style: theme.textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Row(
            children: [
              Icon(
                Icons.trending_up,
                color: Colors.white.withAlpha(230),
                size: 20,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Text(
                'Saved ${_spending!.formattedTotalSavings}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withAlpha(230),
                ),
              ),
              const Spacer(),
              Text(
                '${_spending!.totalBookings} bookings',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withAlpha(230),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryBreakdown(ThemeData theme) {
    if (_spending == null || _spending!.categoryBreakdown.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Spending by Category',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        ..._spending!.categoryBreakdown.entries.map((entry) {
          final category = entry.key;
          final amount = entry.value;
          final percentage = _spending!.getCategoryPercentage(category);

          return Padding(
            padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
            child: _buildCategoryItem(theme, category, amount, percentage),
          );
        }),
      ],
    );
  }

  Widget _buildCategoryItem(
    ThemeData theme,
    TravelServiceCategory category,
    double amount,
    double percentage,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: category.color.withAlpha(26),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Icon(
                  category.icon,
                  color: category.color,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category.displayName,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      '${percentage.toStringAsFixed(1)}% of total',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '\$${amount.toStringAsFixed(0)}',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: theme.colorScheme.surfaceContainerHighest,
            valueColor: AlwaysStoppedAnimation<Color>(category.color),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyTrends(ThemeData theme) {
    if (_spending == null || _spending!.monthlySpending.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Monthly Trends',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        Container(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            border: Border.all(
              color: theme.colorScheme.outline.withAlpha(51),
            ),
          ),
          child: Column(
            children: [
              Text(
                'Monthly spending visualization would go here',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: AppTheme.spacingMedium),
              Text(
                'Peak month: ${_getHighestSpendingMonth()}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSavingsInsights(ThemeData theme) {
    if (_spending == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Savings Insights',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        Container(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          decoration: BoxDecoration(
            color: AppTheme.successColor.withAlpha(13),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            border: Border.all(
              color: AppTheme.successColor.withAlpha(51),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.savings,
                    color: AppTheme.successColor,
                    size: 24,
                  ),
                  const SizedBox(width: AppTheme.spacingSmall),
                  Text(
                    'You saved ${_spending!.savingsPercentage.toStringAsFixed(1)}%',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                'Compared to booking directly with providers, you saved ${_spending!.formattedTotalSavings} this year.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getHighestSpendingMonth() {
    if (_spending == null || _spending!.monthlySpending.isEmpty) {
      return 'Unknown';
    }

    final highestEntry = _spending!.monthlySpending.entries
        .reduce((a, b) => a.value > b.value ? a : b);

    return '${highestEntry.key} (\$${highestEntry.value.toStringAsFixed(0)})';
  }
}

// TODO: Backend Integration - Spending Analytics Screen API
// GET /api/v1/analytics/spending?userId={id}&period=monthly&year=2024
// Headers: Authorization: Bearer {token}
// Response: {
//   "spendingData": TravelSpending.toJson(),
//   "monthlyBreakdown": MonthlySpending[],
//   "categoryTrends": CategoryTrend[],
//   "savingsAnalysis": SavingsInsight[],
//   "budgetComparison": BudgetComparison
// }

// TODO: Spending Export API
// POST /api/v1/analytics/spending/export
// Headers: Authorization: Bearer {token}
// Body: { "userId": "string", "format": "pdf|csv|excel", "period": "monthly|yearly" }
// Response: { "downloadUrl": "string", "expiresAt": "ISO8601" }

// TODO: Budget Management API
// GET /api/v1/analytics/budget?userId={id}
// PUT /api/v1/analytics/budget
// Body: { "monthlyBudget": number, "categories": BudgetCategory[] }
// Response: { "budget": Budget, "alerts": BudgetAlert[] }
