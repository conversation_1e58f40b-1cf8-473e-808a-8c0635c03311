// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/models/analytics/travel_analytics_model.dart';
import 'package:culture_connect/services/analytics/user_insights_service.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen displaying travel insights and analytics dashboard
class TravelInsightsScreen extends ConsumerStatefulWidget {
  const TravelInsightsScreen({super.key});

  @override
  ConsumerState<TravelInsightsScreen> createState() =>
      _TravelInsightsScreenState();
}

class _TravelInsightsScreenState extends ConsumerState<TravelInsightsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isLoading = true;
  String? _error;
  TravelSpending? _spending;
  TravelPattern? _patterns;
  List<PersonalInsight> _insights = [];
  List<TravelRecommendation> _recommendations = [];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadAnalyticsData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  Future<void> _loadAnalyticsData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // TODO: Get actual user ID from auth provider
      const userId = 'demo_user_id';

      // TODO: Initialize services with proper dependencies
      // For now, create mock data for demonstration
      await Future.delayed(const Duration(milliseconds: 1500));

      if (mounted) {
        setState(() {
          _spending = TravelSpending(
            id: 'spending_2024',
            userId: userId,
            totalAmount: 4250.0,
            currency: 'USD',
            categoryBreakdown: {
              TravelServiceCategory.flights: 3200.0,
              TravelServiceCategory.insurance: 650.0,
              TravelServiceCategory.visa: 400.0,
            },
            monthlySpending: {
              'Jan': 800.0,
              'Mar': 1200.0,
              'Jun': 950.0,
              'Dec': 1300.0,
            },
            totalSavings: 680.0,
            totalBookings: 6,
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
            lastUpdated: DateTime.now(),
          );

          _patterns = TravelPattern(
            id: 'pattern_2024',
            userId: userId,
            topDestinations: {
              'Dubai': 3,
              'London': 2,
              'New York': 1,
            },
            averageAdvanceBookingDays: 16.0,
            preferredBookingDay: 'Tuesday',
            mostActiveMonth: 'December',
            travelFrequency: 6.0,
            travelPreferences: {
              'Economy': 4,
              'Business': 2,
            },
            seasonalPatterns: {
              'Winter': 3,
              'Summer': 2,
              'Spring': 1,
            },
            lastUpdated: DateTime.now(),
          );

          _insights = [
            PersonalInsight(
              title: 'Smart Savings',
              description: 'You saved \$680 this year by using CultureConnect!',
              category: 'savings',
              data: {'savings_amount': 680.0},
              importance: 5,
              generatedAt: DateTime.now(),
            ),
            PersonalInsight(
              title: 'Travel Style',
              description:
                  'You are an Advance Booker - you book 16 days in advance on average.',
              category: 'booking_pattern',
              data: {'advance_days': 16.0},
              importance: 4,
              generatedAt: DateTime.now(),
            ),
          ];

          _recommendations = [
            const TravelRecommendation(
              title: 'Book December Trip Early',
              description:
                  'December is your most active travel month. Book now to save up to 25%.',
              type: 'timing',
              potentialSavings: 200.0,
              confidence: 0.8,
            ),
          ];

          _isLoading = false;
        });

        _animationController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to load analytics data: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Travel Insights',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.primary,
            ),
            onPressed: _loadAnalyticsData,
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator(),
      );
    }

    if (_error != null) {
      return ErrorView(
        error: _error!,
        onRetry: _loadAnalyticsData,
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: RefreshIndicator(
          onRefresh: _loadAnalyticsData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildWelcomeSection(theme),
                const SizedBox(height: AppTheme.spacingLarge),
                _buildQuickStatsSection(theme),
                const SizedBox(height: AppTheme.spacingLarge),
                _buildInsightsSection(theme),
                const SizedBox(height: AppTheme.spacingLarge),
                _buildRecommendationsSection(theme),
                const SizedBox(height: AppTheme.spacingLarge),
                _buildActionButtonsSection(theme),
                const SizedBox(height: AppTheme.spacingLarge),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withAlpha(26),
            theme.colorScheme.secondary.withAlpha(13),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
      ),
      child: Row(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(26),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.analytics,
              color: theme.colorScheme.primary,
              size: 40,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Travel Year',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingSmall),
                Text(
                  'Discover insights about your travel patterns and savings',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStatsSection(ThemeData theme) {
    if (_spending == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Stats',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                theme,
                'Total Spent',
                _spending!.formattedTotalAmount,
                Icons.account_balance_wallet,
                theme.colorScheme.primary,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                theme,
                'Total Saved',
                _spending!.formattedTotalSavings,
                Icons.savings,
                AppTheme.successColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                theme,
                'Bookings',
                '${_spending!.totalBookings}',
                Icons.flight_takeoff,
                theme.colorScheme.secondary,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                theme,
                'Travel Style',
                _patterns?.travelStyle ?? 'Unknown',
                Icons.style,
                AppTheme.infoColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    ThemeData theme,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withAlpha(13),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 24,
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightsSection(ThemeData theme) {
    if (_insights.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Personal Insights',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        ..._insights.map((insight) => Padding(
              padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
              child: _buildInsightCard(theme, insight),
            )),
      ],
    );
  }

  Widget _buildInsightCard(ThemeData theme, PersonalInsight insight) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withAlpha(26),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Icon(
                  _getInsightIcon(insight.category),
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Text(
                  insight.title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            insight.description,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationsSection(ThemeData theme) {
    if (_recommendations.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recommendations',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        ..._recommendations.map((recommendation) => Padding(
              padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
              child: _buildRecommendationCard(theme, recommendation),
            )),
      ],
    );
  }

  Widget _buildRecommendationCard(
      ThemeData theme, TravelRecommendation recommendation) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: AppTheme.successColor.withAlpha(13),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: AppTheme.successColor.withAlpha(51),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb,
                color: AppTheme.successColor,
                size: 20,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Expanded(
                child: Text(
                  recommendation.title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
              if (recommendation.potentialSavings != null)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.successColor,
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusSmall),
                  ),
                  child: Text(
                    'Save \$${recommendation.potentialSavings!.toStringAsFixed(0)}',
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            recommendation.description,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtonsSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          onPressed: () {
            // TODO: Navigate to spending analytics screen when implemented
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Detailed analytics coming soon!'),
              ),
            );
          },
          icon: const Icon(Icons.analytics),
          label: const Text('View Detailed Analytics'),
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: Colors.white,
            padding:
                const EdgeInsets.symmetric(vertical: AppTheme.spacingMedium),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            ),
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        OutlinedButton.icon(
          onPressed: () {
            // TODO: Implement data export functionality
          },
          icon: const Icon(Icons.download),
          label: const Text('Export Travel Data'),
          style: OutlinedButton.styleFrom(
            padding:
                const EdgeInsets.symmetric(vertical: AppTheme.spacingMedium),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            ),
          ),
        ),
      ],
    );
  }

  IconData _getInsightIcon(String category) {
    switch (category) {
      case 'savings':
        return Icons.savings;
      case 'spending_pattern':
        return Icons.pie_chart;
      case 'travel_pattern':
        return Icons.map;
      case 'booking_pattern':
        return Icons.schedule;
      default:
        return Icons.insights;
    }
  }
}

// TODO: Backend Integration - Travel Insights Screen API
// GET /api/v1/analytics/dashboard?userId={id}&year=2024
// Headers: Authorization: Bearer {token}
// Response: {
//   "travelSummary": {
//     "totalSpent": number,
//     "totalSaved": number,
//     "totalBookings": number,
//     "travelStyle": string
//   },
//   "quickInsights": PersonalInsight[],
//   "recommendations": TravelRecommendation[],
//   "achievementProgress": AchievementProgress
// }

// TODO: Real-time Analytics Updates WebSocket
// WS /ws/analytics/live-updates?userId={id}
// Receives: {
//   "type": "spending_update" | "new_insight" | "achievement_progress",
//   "data": any,
//   "timestamp": "ISO8601"
// }

// TODO: Analytics Privacy Settings API
// GET /api/v1/analytics/privacy-settings?userId={id}
// PUT /api/v1/analytics/privacy-settings
// Body: AnalyticsPreferences.toJson()
// Response: { "success": boolean, "settings": AnalyticsPreferences }
