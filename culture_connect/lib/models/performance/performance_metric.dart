import 'package:flutter/foundation.dart';

/// Enumeration of performance metric types
enum MetricType {
  startup('startup', 'App Startup'),
  transition('transition', 'Screen Transition'),
  api('api', 'API Request'),
  memory('memory', 'Memory Usage'),
  battery('battery', 'Battery Consumption'),
  rendering('rendering', 'Frame Rendering');

  const MetricType(this.value, this.displayName);

  /// The string value of the metric type
  final String value;

  /// The display name of the metric type
  final String displayName;

  /// Convert from string value
  static MetricType fromString(String value) {
    return MetricType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => MetricType.startup,
    );
  }
}

/// Base model for performance metrics
@immutable
class PerformanceMetric {
  /// Unique identifier for the metric
  final String id;

  /// Type of performance metric
  final MetricType type;

  /// Timestamp when the metric was recorded
  final DateTime timestamp;

  /// Primary value of the metric (e.g., duration in milliseconds)
  final double value;

  /// Unit of measurement for the value
  final String unit;

  /// Additional metadata for the metric
  final Map<String, dynamic> metadata;

  /// Session identifier for grouping metrics
  final String sessionId;

  /// User identifier (anonymized for privacy)
  final String? userId;

  const PerformanceMetric({
    required this.id,
    required this.type,
    required this.timestamp,
    required this.value,
    required this.unit,
    this.metadata = const {},
    required this.sessionId,
    this.userId,
  });

  /// Create a copy with updated values
  PerformanceMetric copyWith({
    String? id,
    MetricType? type,
    DateTime? timestamp,
    double? value,
    String? unit,
    Map<String, dynamic>? metadata,
    String? sessionId,
    String? userId,
  }) {
    return PerformanceMetric(
      id: id ?? this.id,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      value: value ?? this.value,
      unit: unit ?? this.unit,
      metadata: metadata ?? this.metadata,
      sessionId: sessionId ?? this.sessionId,
      userId: userId ?? this.userId,
    );
  }

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.value,
      'timestamp': timestamp.toIso8601String(),
      'value': value,
      'unit': unit,
      'metadata': metadata,
      'sessionId': sessionId,
      'userId': userId,
    };
  }

  /// Create from JSON
  factory PerformanceMetric.fromJson(Map<String, dynamic> json) {
    return PerformanceMetric(
      id: json['id'] as String,
      type: MetricType.fromString(json['type'] as String),
      timestamp: DateTime.parse(json['timestamp'] as String),
      value: (json['value'] as num).toDouble(),
      unit: json['unit'] as String,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      sessionId: json['sessionId'] as String,
      userId: json['userId'] as String?,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PerformanceMetric &&
        other.id == id &&
        other.type == type &&
        other.timestamp == timestamp &&
        other.value == value &&
        other.unit == unit &&
        mapEquals(other.metadata, metadata) &&
        other.sessionId == sessionId &&
        other.userId == userId;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      type,
      timestamp,
      value,
      unit,
      metadata,
      sessionId,
      userId,
    );
  }

  @override
  String toString() {
    return 'PerformanceMetric(id: $id, type: ${type.displayName}, '
        'value: $value $unit, timestamp: $timestamp)';
  }
}

/// Specialized metric for app startup performance
class StartupMetric extends PerformanceMetric {
  /// Type of startup (cold, warm, hot)
  final String startupType;

  /// Time from app launch to first frame
  final Duration timeToFirstFrame;

  /// Time from app launch to interactive
  final Duration timeToInteractive;

  /// Memory usage at startup
  final double initialMemoryUsage;

  const StartupMetric({
    required super.id,
    required super.timestamp,
    required super.sessionId,
    super.userId,
    required this.startupType,
    required this.timeToFirstFrame,
    required this.timeToInteractive,
    required this.initialMemoryUsage,
  }) : super(
          type: MetricType.startup,
          value: 0, // Will be calculated from timeToInteractive
          unit: 'ms',
          metadata: const {},
        );

  @override
  double get value => timeToInteractive.inMilliseconds.toDouble();

  @override
  Map<String, dynamic> get metadata => {
        'startupType': startupType,
        'timeToFirstFrame': timeToFirstFrame.inMilliseconds,
        'timeToInteractive': timeToInteractive.inMilliseconds,
        'initialMemoryUsage': initialMemoryUsage,
      };

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'startupType': startupType,
      'timeToFirstFrame': timeToFirstFrame.inMilliseconds,
      'timeToInteractive': timeToInteractive.inMilliseconds,
      'initialMemoryUsage': initialMemoryUsage,
    });
    return json;
  }

  factory StartupMetric.fromJson(Map<String, dynamic> json) {
    return StartupMetric(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      sessionId: json['sessionId'] as String,
      userId: json['userId'] as String?,
      startupType: json['startupType'] as String,
      timeToFirstFrame: Duration(milliseconds: json['timeToFirstFrame'] as int),
      timeToInteractive:
          Duration(milliseconds: json['timeToInteractive'] as int),
      initialMemoryUsage: (json['initialMemoryUsage'] as num).toDouble(),
    );
  }
}

/// Specialized metric for screen transition performance
class TransitionMetric extends PerformanceMetric {
  /// Source screen name
  final String fromScreen;

  /// Destination screen name
  final String toScreen;

  /// Transition duration
  final Duration transitionDuration;

  /// Whether the transition was animated
  final bool isAnimated;

  /// Route type (push, pop, replace)
  final String routeType;

  const TransitionMetric({
    required super.id,
    required super.timestamp,
    required super.sessionId,
    super.userId,
    required this.fromScreen,
    required this.toScreen,
    required this.transitionDuration,
    required this.isAnimated,
    required this.routeType,
  }) : super(
          type: MetricType.transition,
          value: 0, // Will be calculated from transitionDuration
          unit: 'ms',
          metadata: const {},
        );

  @override
  double get value => transitionDuration.inMilliseconds.toDouble();

  @override
  Map<String, dynamic> get metadata => {
        'fromScreen': fromScreen,
        'toScreen': toScreen,
        'transitionDuration': transitionDuration.inMilliseconds,
        'isAnimated': isAnimated,
        'routeType': routeType,
      };

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'fromScreen': fromScreen,
      'toScreen': toScreen,
      'transitionDuration': transitionDuration.inMilliseconds,
      'isAnimated': isAnimated,
      'routeType': routeType,
    });
    return json;
  }

  factory TransitionMetric.fromJson(Map<String, dynamic> json) {
    return TransitionMetric(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      sessionId: json['sessionId'] as String,
      userId: json['userId'] as String?,
      fromScreen: json['fromScreen'] as String,
      toScreen: json['toScreen'] as String,
      transitionDuration:
          Duration(milliseconds: json['transitionDuration'] as int),
      isAnimated: json['isAnimated'] as bool,
      routeType: json['routeType'] as String,
    );
  }
}

/// Specialized metric for API request performance
class APIMetric extends PerformanceMetric {
  /// API endpoint URL
  final String endpoint;

  /// HTTP method (GET, POST, etc.)
  final String method;

  /// Response time duration
  final Duration responseTime;

  /// HTTP status code
  final int statusCode;

  /// Request size in bytes
  final int requestSize;

  /// Response size in bytes
  final int responseSize;

  /// Whether the request was successful
  final bool isSuccessful;

  const APIMetric({
    required super.id,
    required super.timestamp,
    required super.sessionId,
    super.userId,
    required this.endpoint,
    required this.method,
    required this.responseTime,
    required this.statusCode,
    required this.requestSize,
    required this.responseSize,
    required this.isSuccessful,
  }) : super(
          type: MetricType.api,
          value: 0, // Will be calculated from responseTime
          unit: 'ms',
          metadata: const {},
        );

  @override
  double get value => responseTime.inMilliseconds.toDouble();

  @override
  Map<String, dynamic> get metadata => {
        'endpoint': endpoint,
        'method': method,
        'responseTime': responseTime.inMilliseconds,
        'statusCode': statusCode,
        'requestSize': requestSize,
        'responseSize': responseSize,
        'isSuccessful': isSuccessful,
      };

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'endpoint': endpoint,
      'method': method,
      'responseTime': responseTime.inMilliseconds,
      'statusCode': statusCode,
      'requestSize': requestSize,
      'responseSize': responseSize,
      'isSuccessful': isSuccessful,
    });
    return json;
  }

  factory APIMetric.fromJson(Map<String, dynamic> json) {
    return APIMetric(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      sessionId: json['sessionId'] as String,
      userId: json['userId'] as String?,
      endpoint: json['endpoint'] as String,
      method: json['method'] as String,
      responseTime: Duration(milliseconds: json['responseTime'] as int),
      statusCode: json['statusCode'] as int,
      requestSize: json['requestSize'] as int,
      responseSize: json['responseSize'] as int,
      isSuccessful: json['isSuccessful'] as bool,
    );
  }
}

/// Specialized metric for memory usage performance
class MemoryMetric extends PerformanceMetric {
  /// Current heap memory usage in bytes
  final double heapUsage;

  /// Current native memory usage in bytes
  final double nativeUsage;

  /// Total memory usage in bytes
  final double totalUsage;

  /// Available memory in bytes
  final double availableMemory;

  /// Memory pressure level (low, medium, high)
  final String pressureLevel;

  const MemoryMetric({
    required super.id,
    required super.timestamp,
    required super.sessionId,
    super.userId,
    required this.heapUsage,
    required this.nativeUsage,
    required this.totalUsage,
    required this.availableMemory,
    required this.pressureLevel,
  }) : super(
          type: MetricType.memory,
          value: 0, // Will be calculated from totalUsage
          unit: 'MB',
          metadata: const {},
        );

  @override
  double get value => totalUsage / (1024 * 1024); // Convert to MB

  @override
  Map<String, dynamic> get metadata => {
        'heapUsage': heapUsage,
        'nativeUsage': nativeUsage,
        'totalUsage': totalUsage,
        'availableMemory': availableMemory,
        'pressureLevel': pressureLevel,
      };

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'heapUsage': heapUsage,
      'nativeUsage': nativeUsage,
      'totalUsage': totalUsage,
      'availableMemory': availableMemory,
      'pressureLevel': pressureLevel,
    });
    return json;
  }

  factory MemoryMetric.fromJson(Map<String, dynamic> json) {
    return MemoryMetric(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      sessionId: json['sessionId'] as String,
      userId: json['userId'] as String?,
      heapUsage: (json['heapUsage'] as num).toDouble(),
      nativeUsage: (json['nativeUsage'] as num).toDouble(),
      totalUsage: (json['totalUsage'] as num).toDouble(),
      availableMemory: (json['availableMemory'] as num).toDouble(),
      pressureLevel: json['pressureLevel'] as String,
    );
  }
}

/// Specialized metric for battery consumption performance
class BatteryMetric extends PerformanceMetric {
  /// Current battery level (0.0 to 1.0)
  final double batteryLevel;

  /// Battery consumption rate (percentage per hour)
  final double consumptionRate;

  /// Whether device is charging
  final bool isCharging;

  /// Power state (unplugged, charging, full)
  final String powerState;

  /// Estimated time remaining in minutes
  final int? estimatedTimeRemaining;

  const BatteryMetric({
    required super.id,
    required super.timestamp,
    required super.sessionId,
    super.userId,
    required this.batteryLevel,
    required this.consumptionRate,
    required this.isCharging,
    required this.powerState,
    this.estimatedTimeRemaining,
  }) : super(
          type: MetricType.battery,
          value: 0, // Will be calculated from consumptionRate
          unit: '%/hr',
          metadata: const {},
        );

  @override
  double get value => consumptionRate;

  @override
  Map<String, dynamic> get metadata => {
        'batteryLevel': batteryLevel,
        'consumptionRate': consumptionRate,
        'isCharging': isCharging,
        'powerState': powerState,
        'estimatedTimeRemaining': estimatedTimeRemaining,
      };

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'batteryLevel': batteryLevel,
      'consumptionRate': consumptionRate,
      'isCharging': isCharging,
      'powerState': powerState,
      'estimatedTimeRemaining': estimatedTimeRemaining,
    });
    return json;
  }

  factory BatteryMetric.fromJson(Map<String, dynamic> json) {
    return BatteryMetric(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      sessionId: json['sessionId'] as String,
      userId: json['userId'] as String?,
      batteryLevel: (json['batteryLevel'] as num).toDouble(),
      consumptionRate: (json['consumptionRate'] as num).toDouble(),
      isCharging: json['isCharging'] as bool,
      powerState: json['powerState'] as String,
      estimatedTimeRemaining: json['estimatedTimeRemaining'] as int?,
    );
  }
}
