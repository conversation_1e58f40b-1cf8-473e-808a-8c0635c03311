// No imports needed for this model file

/// Enum for visa service provider specializations
enum VisaProviderSpecialization {
  business,
  tourist,
  student,
  work,
  transit,
  diplomatic,
  emergency,
  all,
}

/// Extension for visa provider specialization display
extension VisaProviderSpecializationExtension on VisaProviderSpecialization {
  /// Display name for the specialization
  String get displayName {
    switch (this) {
      case VisaProviderSpecialization.business:
        return 'Business Visas';
      case VisaProviderSpecialization.tourist:
        return 'Tourist Visas';
      case VisaProviderSpecialization.student:
        return 'Student Visas';
      case VisaProviderSpecialization.work:
        return 'Work Visas';
      case VisaProviderSpecialization.transit:
        return 'Transit Visas';
      case VisaProviderSpecialization.diplomatic:
        return 'Diplomatic Visas';
      case VisaProviderSpecialization.emergency:
        return 'Emergency Visas';
      case VisaProviderSpecialization.all:
        return 'All Visa Types';
    }
  }

  /// Icon name for the specialization
  String get iconName {
    switch (this) {
      case VisaProviderSpecialization.business:
        return 'business';
      case VisaProviderSpecialization.tourist:
        return 'explore';
      case VisaProviderSpecialization.student:
        return 'school';
      case VisaProviderSpecialization.work:
        return 'work';
      case VisaProviderSpecialization.transit:
        return 'connecting_airports';
      case VisaProviderSpecialization.diplomatic:
        return 'account_balance';
      case VisaProviderSpecialization.emergency:
        return 'emergency';
      case VisaProviderSpecialization.all:
        return 'public';
    }
  }
}

/// Enum for visa service provider tiers
enum VisaProviderTier {
  basic,
  premium,
  platinum,
}

/// Extension for visa provider tier display
extension VisaProviderTierExtension on VisaProviderTier {
  /// Display name for the tier
  String get displayName {
    switch (this) {
      case VisaProviderTier.basic:
        return 'Basic';
      case VisaProviderTier.premium:
        return 'Premium';
      case VisaProviderTier.platinum:
        return 'Platinum';
    }
  }

  /// Color for the tier
  String get colorHex {
    switch (this) {
      case VisaProviderTier.basic:
        return '#6B7280'; // Gray
      case VisaProviderTier.premium:
        return '#3B82F6'; // Blue
      case VisaProviderTier.platinum:
        return '#F59E0B'; // Amber
    }
  }
}

/// Model representing a visa service provider's service offering
class VisaServiceOffering {
  /// Unique identifier for the service offering
  final String id;

  /// Name of the service
  final String serviceName;

  /// Description of the service
  final String description;

  /// Base price for the service in USD
  final double basePrice;

  /// Processing time in business days
  final int processingDays;

  /// Success rate percentage (0-100)
  final double successRate;

  /// Whether this service includes document review
  final bool includesDocumentReview;

  /// Whether this service includes application submission
  final bool includesApplicationSubmission;

  /// Whether this service includes status tracking
  final bool includesStatusTracking;

  /// Whether this service includes customer support
  final bool includesCustomerSupport;

  /// Additional features included in this service
  final List<String> additionalFeatures;

  /// Countries this service is available for
  final List<String> availableCountries;

  /// Visa types this service supports
  final List<VisaProviderSpecialization> supportedVisaTypes;

  /// Creates a new visa service offering
  const VisaServiceOffering({
    required this.id,
    required this.serviceName,
    required this.description,
    required this.basePrice,
    required this.processingDays,
    required this.successRate,
    required this.includesDocumentReview,
    required this.includesApplicationSubmission,
    required this.includesStatusTracking,
    required this.includesCustomerSupport,
    required this.additionalFeatures,
    required this.availableCountries,
    required this.supportedVisaTypes,
  });

  /// Creates a visa service offering from JSON
  factory VisaServiceOffering.fromJson(Map<String, dynamic> json) {
    return VisaServiceOffering(
      id: json['id'] as String,
      serviceName: json['serviceName'] as String,
      description: json['description'] as String,
      basePrice: (json['basePrice'] as num).toDouble(),
      processingDays: json['processingDays'] as int,
      successRate: (json['successRate'] as num).toDouble(),
      includesDocumentReview: json['includesDocumentReview'] as bool,
      includesApplicationSubmission:
          json['includesApplicationSubmission'] as bool,
      includesStatusTracking: json['includesStatusTracking'] as bool,
      includesCustomerSupport: json['includesCustomerSupport'] as bool,
      additionalFeatures: (json['additionalFeatures'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      availableCountries: (json['availableCountries'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      supportedVisaTypes: (json['supportedVisaTypes'] as List<dynamic>)
          .map((e) => VisaProviderSpecialization.values
              .firstWhere((type) => type.name == e))
          .toList(),
    );
  }

  /// Converts the visa service offering to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'serviceName': serviceName,
      'description': description,
      'basePrice': basePrice,
      'processingDays': processingDays,
      'successRate': successRate,
      'includesDocumentReview': includesDocumentReview,
      'includesApplicationSubmission': includesApplicationSubmission,
      'includesStatusTracking': includesStatusTracking,
      'includesCustomerSupport': includesCustomerSupport,
      'additionalFeatures': additionalFeatures,
      'availableCountries': availableCountries,
      'supportedVisaTypes': supportedVisaTypes.map((e) => e.name).toList(),
    };
  }

  /// Formatted price string
  String get formattedPrice => '\$${basePrice.toStringAsFixed(2)}';

  /// Formatted processing time
  String get formattedProcessingTime {
    if (processingDays == 1) {
      return '1 business day';
    }
    return '$processingDays business days';
  }

  /// Formatted success rate
  String get formattedSuccessRate => '${successRate.toStringAsFixed(1)}%';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VisaServiceOffering &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Model representing a customer review for a visa service provider
class VisaProviderReview {
  /// Unique identifier for the review
  final String id;

  /// Customer name (can be anonymized)
  final String customerName;

  /// Rating out of 5
  final double rating;

  /// Review title
  final String title;

  /// Review content
  final String content;

  /// Date the review was submitted
  final DateTime reviewDate;

  /// Country the visa was for
  final String visaCountry;

  /// Visa type that was processed
  final VisaProviderSpecialization visaType;

  /// Whether the visa application was successful
  final bool wasSuccessful;

  /// Processing time experienced by the customer
  final int actualProcessingDays;

  /// Whether this review is verified
  final bool isVerified;

  /// Creates a new visa provider review
  const VisaProviderReview({
    required this.id,
    required this.customerName,
    required this.rating,
    required this.title,
    required this.content,
    required this.reviewDate,
    required this.visaCountry,
    required this.visaType,
    required this.wasSuccessful,
    required this.actualProcessingDays,
    required this.isVerified,
  });

  /// Creates a visa provider review from JSON
  factory VisaProviderReview.fromJson(Map<String, dynamic> json) {
    return VisaProviderReview(
      id: json['id'] as String,
      customerName: json['customerName'] as String,
      rating: (json['rating'] as num).toDouble(),
      title: json['title'] as String,
      content: json['content'] as String,
      reviewDate: DateTime.parse(json['reviewDate'] as String),
      visaCountry: json['visaCountry'] as String,
      visaType: VisaProviderSpecialization.values
          .firstWhere((type) => type.name == json['visaType']),
      wasSuccessful: json['wasSuccessful'] as bool,
      actualProcessingDays: json['actualProcessingDays'] as int,
      isVerified: json['isVerified'] as bool,
    );
  }

  /// Converts the visa provider review to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customerName': customerName,
      'rating': rating,
      'title': title,
      'content': content,
      'reviewDate': reviewDate.toIso8601String(),
      'visaCountry': visaCountry,
      'visaType': visaType.name,
      'wasSuccessful': wasSuccessful,
      'actualProcessingDays': actualProcessingDays,
      'isVerified': isVerified,
    };
  }

  /// Formatted review date
  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(reviewDate);

    if (difference.inDays < 1) {
      return 'Today';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()} weeks ago';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()} months ago';
    } else {
      return '${(difference.inDays / 365).floor()} years ago';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VisaProviderReview &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Model representing a certified visa service provider
class VisaServiceProvider {
  /// Unique identifier for the provider
  final String id;

  /// Provider name
  final String name;

  /// Provider description
  final String description;

  /// Provider logo URL
  final String logoUrl;

  /// Provider website URL
  final String websiteUrl;

  /// Contact phone number
  final String phoneNumber;

  /// Contact email address
  final String emailAddress;

  /// Physical address
  final String address;

  /// City where provider is located
  final String city;

  /// Country where provider is located
  final String country;

  /// Overall rating (0-5)
  final double rating;

  /// Total number of reviews
  final int reviewCount;

  /// Whether this provider is featured
  final bool isFeatured;

  /// Whether this provider is a verified partner
  final bool isVerifiedPartner;

  /// Provider tier level
  final VisaProviderTier tier;

  /// Years of experience
  final int yearsOfExperience;

  /// Total successful applications processed
  final int totalApplicationsProcessed;

  /// Overall success rate percentage (0-100)
  final double overallSuccessRate;

  /// Average processing time in business days
  final double averageProcessingDays;

  /// Commission rate for CultureConnect (0-100)
  final double commissionRate;

  /// Countries this provider serves
  final List<String> serviceCountries;

  /// Provider specializations
  final List<VisaProviderSpecialization> specializations;

  /// Service offerings
  final List<VisaServiceOffering> serviceOfferings;

  /// Recent customer reviews
  final List<VisaProviderReview> recentReviews;

  /// Languages supported by the provider
  final List<String> supportedLanguages;

  /// Business hours (e.g., "Mon-Fri 9AM-6PM UTC")
  final String businessHours;

  /// Response time for inquiries (in hours)
  final int responseTimeHours;

  /// Whether provider offers emergency services
  final bool offersEmergencyServices;

  /// Whether provider offers document pickup/delivery
  final bool offersDocumentPickup;

  /// Whether provider offers consultation services
  final bool offersConsultation;

  /// Minimum service fee in USD
  final double minimumServiceFee;

  /// Date when provider was last verified
  final DateTime lastVerifiedDate;

  /// Additional provider details
  final Map<String, dynamic> additionalDetails;

  /// Creates a new visa service provider
  const VisaServiceProvider({
    required this.id,
    required this.name,
    required this.description,
    required this.logoUrl,
    required this.websiteUrl,
    required this.phoneNumber,
    required this.emailAddress,
    required this.address,
    required this.city,
    required this.country,
    required this.rating,
    required this.reviewCount,
    required this.isFeatured,
    required this.isVerifiedPartner,
    required this.tier,
    required this.yearsOfExperience,
    required this.totalApplicationsProcessed,
    required this.overallSuccessRate,
    required this.averageProcessingDays,
    required this.commissionRate,
    required this.serviceCountries,
    required this.specializations,
    required this.serviceOfferings,
    required this.recentReviews,
    required this.supportedLanguages,
    required this.businessHours,
    required this.responseTimeHours,
    required this.offersEmergencyServices,
    required this.offersDocumentPickup,
    required this.offersConsultation,
    required this.minimumServiceFee,
    required this.lastVerifiedDate,
    this.additionalDetails = const {},
  });

  /// Creates a visa service provider from JSON
  factory VisaServiceProvider.fromJson(Map<String, dynamic> json) {
    return VisaServiceProvider(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      logoUrl: json['logoUrl'] as String,
      websiteUrl: json['websiteUrl'] as String,
      phoneNumber: json['phoneNumber'] as String,
      emailAddress: json['emailAddress'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      country: json['country'] as String,
      rating: (json['rating'] as num).toDouble(),
      reviewCount: json['reviewCount'] as int,
      isFeatured: json['isFeatured'] as bool,
      isVerifiedPartner: json['isVerifiedPartner'] as bool,
      tier: VisaProviderTier.values
          .firstWhere((tier) => tier.name == json['tier']),
      yearsOfExperience: json['yearsOfExperience'] as int,
      totalApplicationsProcessed: json['totalApplicationsProcessed'] as int,
      overallSuccessRate: (json['overallSuccessRate'] as num).toDouble(),
      averageProcessingDays: (json['averageProcessingDays'] as num).toDouble(),
      commissionRate: (json['commissionRate'] as num).toDouble(),
      serviceCountries: (json['serviceCountries'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      specializations: (json['specializations'] as List<dynamic>)
          .map((e) => VisaProviderSpecialization.values
              .firstWhere((spec) => spec.name == e))
          .toList(),
      serviceOfferings: (json['serviceOfferings'] as List<dynamic>)
          .map((e) => VisaServiceOffering.fromJson(e as Map<String, dynamic>))
          .toList(),
      recentReviews: (json['recentReviews'] as List<dynamic>)
          .map((e) => VisaProviderReview.fromJson(e as Map<String, dynamic>))
          .toList(),
      supportedLanguages: (json['supportedLanguages'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      businessHours: json['businessHours'] as String,
      responseTimeHours: json['responseTimeHours'] as int,
      offersEmergencyServices: json['offersEmergencyServices'] as bool,
      offersDocumentPickup: json['offersDocumentPickup'] as bool,
      offersConsultation: json['offersConsultation'] as bool,
      minimumServiceFee: (json['minimumServiceFee'] as num).toDouble(),
      lastVerifiedDate: DateTime.parse(json['lastVerifiedDate'] as String),
      additionalDetails:
          json['additionalDetails'] as Map<String, dynamic>? ?? {},
    );
  }

  /// Converts the visa service provider to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'logoUrl': logoUrl,
      'websiteUrl': websiteUrl,
      'phoneNumber': phoneNumber,
      'emailAddress': emailAddress,
      'address': address,
      'city': city,
      'country': country,
      'rating': rating,
      'reviewCount': reviewCount,
      'isFeatured': isFeatured,
      'isVerifiedPartner': isVerifiedPartner,
      'tier': tier.name,
      'yearsOfExperience': yearsOfExperience,
      'totalApplicationsProcessed': totalApplicationsProcessed,
      'overallSuccessRate': overallSuccessRate,
      'averageProcessingDays': averageProcessingDays,
      'commissionRate': commissionRate,
      'serviceCountries': serviceCountries,
      'specializations': specializations.map((e) => e.name).toList(),
      'serviceOfferings': serviceOfferings.map((e) => e.toJson()).toList(),
      'recentReviews': recentReviews.map((e) => e.toJson()).toList(),
      'supportedLanguages': supportedLanguages,
      'businessHours': businessHours,
      'responseTimeHours': responseTimeHours,
      'offersEmergencyServices': offersEmergencyServices,
      'offersDocumentPickup': offersDocumentPickup,
      'offersConsultation': offersConsultation,
      'minimumServiceFee': minimumServiceFee,
      'lastVerifiedDate': lastVerifiedDate.toIso8601String(),
      'additionalDetails': additionalDetails,
    };
  }

  /// Formatted rating string
  String get formattedRating => rating.toStringAsFixed(1);

  /// Formatted review count
  String get formattedReviewCount {
    if (reviewCount < 1000) {
      return reviewCount.toString();
    } else if (reviewCount < 1000000) {
      return '${(reviewCount / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(reviewCount / 1000000).toStringAsFixed(1)}M';
    }
  }

  /// Formatted success rate
  String get formattedSuccessRate =>
      '${overallSuccessRate.toStringAsFixed(1)}%';

  /// Formatted average processing time
  String get formattedAverageProcessingTime {
    final days = averageProcessingDays.round();
    if (days == 1) {
      return '1 business day';
    }
    return '$days business days';
  }

  /// Formatted minimum service fee
  String get formattedMinimumFee => '\$${minimumServiceFee.toStringAsFixed(2)}';

  /// Formatted response time
  String get formattedResponseTime {
    if (responseTimeHours < 24) {
      return '$responseTimeHours hours';
    } else {
      final days = (responseTimeHours / 24).round();
      return '$days days';
    }
  }

  /// Whether provider serves a specific country
  bool servesCountry(String countryCode) {
    return serviceCountries.contains(countryCode);
  }

  /// Whether provider has a specific specialization
  bool hasSpecialization(VisaProviderSpecialization specialization) {
    return specializations.contains(specialization) ||
        specializations.contains(VisaProviderSpecialization.all);
  }

  /// Get service offering for a specific country and visa type
  VisaServiceOffering? getServiceOffering(
    String countryCode,
    VisaProviderSpecialization visaType,
  ) {
    try {
      return serviceOfferings.firstWhere(
        (offering) =>
            offering.availableCountries.contains(countryCode) &&
            (offering.supportedVisaTypes.contains(visaType) ||
                offering.supportedVisaTypes
                    .contains(VisaProviderSpecialization.all)),
      );
    } catch (e) {
      try {
        return serviceOfferings.firstWhere(
          (offering) => offering.availableCountries.contains(countryCode),
        );
      } catch (e) {
        return serviceOfferings.isNotEmpty ? serviceOfferings.first : null;
      }
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VisaServiceProvider &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
