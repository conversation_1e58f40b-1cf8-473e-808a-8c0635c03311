# CultureConnect Project Guardrails

This document serves as a comprehensive guide for AI assistants working on the CultureConnect project. It outlines the correct project structure, coding standards, and best practices to ensure consistency and prevent common mistakes.

## 1. Project Structure

### Single Source of Truth

The **ONLY** valid source code location is:
```
culture_connect/lib/
```

**IMPORTANT**: There should never be a separate `lib` folder at the root level or anywhere else in the project. All code must reside within `culture_connect/lib/`.

### Directory Structure

The project follows this directory structure:

```
culture_connect/
├── lib/
│   ├── config/           # Configuration files, router definitions
│   ├── models/           # Data models and DTOs
│   ├── providers/        # Riverpod providers
│   ├── screens/          # UI screens
│   │   ├── messaging/    # Messaging-related screens
│   │   └── safety/       # Safety-related screens
│   ├── services/         # Business logic and API services
│   ├── theme/            # Theme definitions
│   ├── widgets/          # Reusable UI components
│   │   └── common/       # Highly reusable basic components
│   └── main.dart         # Application entry point
├── assets/               # Static assets (images, fonts, etc.)
├── test/                 # Test files
└── pubspec.yaml          # Package dependencies
```

## 2. File Organization and Naming Conventions

### Naming Conventions

- **Files**: Use `snake_case` for all file names
  - ✅ `user_profile_screen.dart`
  - ❌ `UserProfileScreen.dart` or `userProfileScreen.dart`

- **Classes**: Use `PascalCase` for all class names
  - ✅ `class UserProfileScreen extends StatelessWidget {...}`
  - ❌ `class userProfileScreen extends StatelessWidget {...}`

- **Variables/Functions**: Use `camelCase` for variables and functions
  - ✅ `final userProfile = UserProfile(...)`
  - ❌ `final UserProfile = UserProfile(...)`

### File Content Guidelines

- **One Class Per File**: Each file should generally contain only one primary class
  - Exception: Small helper classes or extensions closely related to the primary class

- **File Naming**: Files should be named after the primary class they contain
  - `user_profile_screen.dart` should contain `UserProfileScreen` class

- **Folder Organization**: Group related files in appropriate directories
  - All screens go in `screens/` or its subdirectories
  - All reusable widgets go in `widgets/` or its subdirectories
  - All models go in `models/`
  - All services go in `services/`

## 3. Import Path Standards

### Package Imports vs Relative Imports

**ALWAYS use package imports** rather than relative imports:

✅ **Correct**:
```dart
import 'package:culture_connect/models/user.dart';
import 'package:culture_connect/services/auth_service.dart';
```

❌ **Incorrect**:
```dart
import '../models/user.dart';
import '../../services/auth_service.dart';
```

### Import Order

Follow this order for imports:
1. Dart SDK imports
2. Flutter imports
3. Third-party package imports
4. Project imports (package:culture_connect/...)

Example:
```dart
// Dart SDK imports
import 'dart:async';
import 'dart:io';

// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Third-party package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;

// Project imports
import 'package:culture_connect/models/user.dart';
import 'package:culture_connect/services/auth_service.dart';
```

## 4. Guidelines for Creating New Components

### Where to Place New Components

- **Screens**: Place in `lib/screens/` or appropriate subdirectory
  - Example: `lib/screens/profile/edit_profile_screen.dart`

- **Reusable Widgets**: Place in `lib/widgets/` or appropriate subdirectory
  - Example: `lib/widgets/cards/experience_card.dart`

- **Common Widgets**: Place highly reusable basic components in `lib/widgets/common/`
  - Example: `lib/widgets/common/app_button.dart`

- **Models**: Place in `lib/models/`
  - Example: `lib/models/booking.dart`

- **Services**: Place in `lib/services/`
  - Example: `lib/services/booking_service.dart`

- **Providers**: Place in `lib/providers/`
  - Example: `lib/providers/auth_provider.dart`

### Component Creation Checklist

When creating a new component:

1. Verify the component doesn't already exist elsewhere in the codebase
2. Place it in the appropriate directory based on its function
3. Follow naming conventions
4. Use package imports for all project files
5. Document the component with appropriate comments
6. Ensure the component is properly exported if needed

## 5. Standards for Code Consolidation

When finding duplicate or similar components:

1. **Identify the Superior Implementation**: Determine which implementation is more complete, robust, and follows best practices

2. **Create a Consolidated Version**: 
   - Combine the best features from all implementations
   - Ensure backward compatibility if possible
   - Place in the appropriate directory
   - Use proper naming conventions

3. **Update References**:
   - Update all import statements to reference the consolidated component
   - Ensure all calling code works with the consolidated component

4. **Remove Duplicates**:
   - Only after verifying the consolidated component works correctly
   - Remove all duplicate implementations

### Example Consolidation Process

For duplicate button components:

1. Identify the most complete implementation
2. Create a consolidated version with all features
3. Update all references to use the consolidated component
4. Remove the duplicate implementations

## 6. Verification Checklist

Before committing changes, verify:

- [ ] All code is in the correct `culture_connect/lib/` directory
- [ ] No duplicate files or components exist
- [ ] All imports use package imports (`package:culture_connect/...`)
- [ ] File and class naming follows conventions
- [ ] Components are in the appropriate directories
- [ ] The project builds without errors
- [ ] Existing functionality is preserved

### Testing Changes

Run these commands to verify changes:

```bash
# Navigate to the project directory
cd culture_connect

# Get dependencies
flutter pub get

# Analyze the code
flutter analyze

# Run tests
flutter test
```

## 7. UI and Theme Standards

### Theme Usage Requirements

**ALWAYS use Theme.of(context) instead of direct color/text style references:**

✅ **Correct**:
```dart
// Use theme colors
color: Theme.of(context).colorScheme.primary,
backgroundColor: Theme.of(context).colorScheme.surface,

// Use theme text styles
style: Theme.of(context).textTheme.headlineMedium,
```

❌ **Incorrect**:
```dart
// Don't use direct color references
color: AppColors.primary,
backgroundColor: AppColors.surface,

// Don't use direct text style references
style: AppTextStyles.heading,
```

### Flutter Deprecation Fixes

**withOpacity to withAlpha Conversion Standards:**

Flutter has deprecated `withOpacity()` in favor of `withAlpha()`. Use these exact conversion values:

```dart
// Opacity to Alpha conversion table
0.1 → 26   (10% opacity)
0.2 → 51   (20% opacity)
0.3 → 77   (30% opacity)
0.4 → 102  (40% opacity)
0.5 → 128  (50% opacity)
0.6 → 153  (60% opacity)
0.7 → 179  (70% opacity)
0.8 → 204  (80% opacity)
0.9 → 230  (90% opacity)
```

✅ **Correct**:
```dart
color: Colors.black.withAlpha(128), // 50% opacity
```

❌ **Incorrect**:
```dart
color: Colors.black.withOpacity(0.5), // Deprecated
```

### flutter_screenutil Removal

**NEVER use flutter_screenutil** - it has been removed from the project:

❌ **Incorrect**:
```dart
import 'package:flutter_screenutil/flutter_screenutil.dart';
width: 100.w,
height: 50.h,
fontSize: 16.sp,
```

✅ **Correct**:
```dart
// Use MediaQuery or fixed values
width: MediaQuery.of(context).size.width * 0.8,
height: 50,
fontSize: 16,
```

### Const Modifiers

**ALWAYS add const modifiers where possible** for performance optimization:

✅ **Correct**:
```dart
const SizedBox(height: 16),
const Padding(
  padding: EdgeInsets.all(8.0),
  child: Text('Hello'),
),
```

❌ **Incorrect**:
```dart
SizedBox(height: 16),
Padding(
  padding: EdgeInsets.all(8.0),
  child: Text('Hello'),
),
```

## 8. Service Implementation Standards

### Async Operations and Mounted Checks

**ALWAYS add mounted checks for async operations in StatefulWidgets:**

✅ **Correct**:
```dart
Future<void> _loadData() async {
  try {
    final data = await apiService.fetchData();
    if (mounted) {
      setState(() {
        _data = data;
      });
    }
  } catch (e) {
    if (mounted) {
      // Handle error
    }
  }
}
```

❌ **Incorrect**:
```dart
Future<void> _loadData() async {
  final data = await apiService.fetchData();
  setState(() {
    _data = data; // May cause error if widget is disposed
  });
}
```

### Error Handling Standards

**ALWAYS implement comprehensive error handling:**

✅ **Correct**:
```dart
try {
  final result = await service.performOperation();
  return Right(result);
} catch (e) {
  logger.error('Operation failed: $e');
  return Left(ServiceException('Operation failed: ${e.toString()}'));
}
```

### Null Safety Best Practices

**ALWAYS handle null values explicitly:**

✅ **Correct**:
```dart
final user = currentUser;
if (user != null) {
  // Use user safely
  return user.name;
}
return 'Unknown User';
```

❌ **Incorrect**:
```dart
return currentUser!.name; // Dangerous null assertion
```

## 9. Code Quality Standards

### Import Organization

**ALWAYS organize imports in this exact order:**

```dart
// 1. Dart SDK imports
import 'dart:async';
import 'dart:convert';

// 2. Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 3. Third-party package imports (alphabetical)
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;

// 4. Project imports (alphabetical)
import 'package:culture_connect/models/user.dart';
import 'package:culture_connect/services/auth_service.dart';
```

### Unused Code Removal

**ALWAYS remove unused imports, variables, and methods:**

✅ **Clean Code**:
```dart
import 'package:flutter/material.dart';
import 'package:culture_connect/models/user.dart'; // Used

class UserWidget extends StatelessWidget {
  final User user; // Used

  const UserWidget({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return Text(user.name);
  }
}
```

❌ **Code with Unused Elements**:
```dart
import 'package:flutter/material.dart';
import 'package:culture_connect/models/user.dart';
import 'package:culture_connect/services/unused_service.dart'; // Unused

class UserWidget extends StatelessWidget {
  final User user;
  final String unusedField; // Unused

  const UserWidget({super.key, required this.user, this.unusedField = ''});

  void unusedMethod() {} // Unused

  @override
  Widget build(BuildContext context) {
    return Text(user.name);
  }
}
```

## 10. Testing and Quality Assurance

### Pre-Implementation Checklist

Before making any changes:

1. **Use codebase-retrieval** to understand existing code structure
2. **Run diagnostics** to identify current issues
3. **Plan changes** systematically
4. **Implement fixes** following these guardrails
5. **Run diagnostics again** to verify fixes
6. **Update documentation** with implementation notes

### Package Management

**ALWAYS use package managers** instead of manually editing package files:

✅ **Correct**:
```bash
flutter pub add package_name
flutter pub remove package_name
```

❌ **Incorrect**:
```yaml
# Don't manually edit pubspec.yaml dependencies
dependencies:
  package_name: ^1.0.0  # Manual addition
```

## 11. Common Mistakes to Avoid

### ⚠️ CRITICAL WARNINGS ⚠️

1. **NEVER create a separate `lib` folder outside of `culture_connect/lib/`**
   - This causes confusion and duplicate code

2. **NEVER use relative imports**
   - Always use package imports (`package:culture_connect/...`)

3. **NEVER use flutter_screenutil**
   - It has been completely removed from the project

4. **NEVER use AppColors or AppTextStyles directly**
   - Always use Theme.of(context) instead

5. **NEVER use withOpacity()**
   - Use withAlpha() with correct conversion values

6. **NEVER skip mounted checks in async operations**
   - Always check if (mounted) before setState()

7. **NEVER leave unused imports or variables**
   - Clean up all unused code

8. **NEVER manually edit package files**
   - Use package managers for dependency management

### Systematic Issue Resolution Process

When fixing issues, follow this exact process:

1. **Analyze**: Use codebase-retrieval to understand the code
2. **Diagnose**: Run diagnostics to see exact errors
3. **Fix**: Apply changes following these guardrails
4. **Verify**: Run diagnostics again to confirm fixes
5. **Document**: Update tracking with implementation notes

### Zero Technical Debt Methodology

Maintain zero technical debt by:

1. **Fixing issues immediately** when they arise
2. **Following coding standards** consistently
3. **Removing deprecated code** promptly
4. **Updating documentation** with every change
5. **Running quality checks** regularly

## 12. File Size and Modularity

### File Size Limits

**NEVER create files larger than 300 lines:**

- Break large files into smaller, focused modules
- Use composition over large monolithic classes
- Separate concerns into different files

### Modular Design Principles

✅ **Good Modular Design**:
```
lib/screens/booking/
├── booking_screen.dart           (< 300 lines)
├── widgets/
│   ├── booking_form.dart        (< 300 lines)
│   ├── booking_summary.dart     (< 300 lines)
│   └── payment_section.dart     (< 300 lines)
└── models/
    └── booking_data.dart        (< 300 lines)
```

## Conclusion

Following these guardrails will ensure consistency in the CultureConnect project and prevent the systematic issues we've encountered. These guidelines are based on real experience from our issue resolution phases and represent best practices for maintaining a clean, efficient, and error-free codebase.

**Remember**: The goal is to maintain zero technical debt through proactive adherence to these standards rather than reactive fixes.
