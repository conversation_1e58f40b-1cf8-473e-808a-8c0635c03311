// Flutter imports
import 'package:flutter_test/flutter_test.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart'
    hide InsuranceCoverageType, InsuranceClaimStatus;
import 'package:culture_connect/models/travel/insurance/insurance_coverage_type.dart';
import 'package:culture_connect/models/travel/insurance/insurance_claim_status.dart';
import 'package:culture_connect/services/travel/insurance/insurance_service.dart';

void main() {
  group('InsuranceService', () {
    late InsuranceService insuranceService;

    setUp(() {
      insuranceService = InsuranceService();
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        // Act & Assert - should not throw
        expect(() => insuranceService.initialize(), returnsNormally);
      });

      test('should handle initialization errors gracefully', () async {
        // This test would require dependency injection to properly mock failures
        // For now, we test that initialization doesn't throw
        expect(() => insuranceService.initialize(), returnsNormally);
      });
    });

    group('Provider Management', () {
      test('should return available insurance providers', () async {
        // Act
        final providers = await insuranceService.getInsuranceProviders();

        // Assert
        expect(providers, isA<List<InsuranceProvider>>());
        expect(providers, isNotEmpty);

        // Verify provider structure
        final firstProvider = providers.first;
        expect(firstProvider.id, isNotEmpty);
        expect(firstProvider.name, isNotEmpty);
        expect(firstProvider.rating, greaterThanOrEqualTo(0.0));
        expect(firstProvider.rating, lessThanOrEqualTo(5.0));
      });

      test('should return featured providers', () async {
        // Act
        final featuredProviders = await insuranceService.getFeaturedProviders();

        // Assert
        expect(featuredProviders, isA<List<InsuranceProvider>>());

        // All returned providers should be featured
        for (final provider in featuredProviders) {
          expect(provider.isFeatured, isTrue);
        }
      });

      test('should return partner providers', () async {
        // Act
        final partnerProviders = await insuranceService.getPartnerProviders();

        // Assert
        expect(partnerProviders, isA<List<InsuranceProvider>>());

        // All returned providers should be partners
        for (final provider in partnerProviders) {
          expect(provider.isPartner, isTrue);
        }
      });

      test('should get provider by ID', () async {
        // Arrange
        final allProviders = await insuranceService.getInsuranceProviders();
        final testProviderId = allProviders.first.id;

        // Act
        final provider = await insuranceService.getProviderById(testProviderId);

        // Assert
        expect(provider, isNotNull);
        expect(provider!.id, equals(testProviderId));
      });

      test('should return null for non-existent provider ID', () async {
        // Act
        final provider =
            await insuranceService.getProviderById('non-existent-id');

        // Assert
        expect(provider, isNull);
      });
    });

    group('Policy Management', () {
      test('should return available policies', () async {
        // Act
        final policies = await insuranceService.getAvailablePolicies();

        // Assert
        expect(policies, isA<List<InsurancePolicy>>());
        expect(policies, isNotEmpty);

        // Verify policy structure
        final firstPolicy = policies.first;
        expect(firstPolicy.id, isNotEmpty);
        expect(firstPolicy.name, isNotEmpty);
        expect(firstPolicy.price, greaterThan(0.0));
        expect(firstPolicy.coverages, isNotEmpty);
      });

      test('should return purchased policies', () async {
        // Act
        final purchasedPolicies = await insuranceService.getPurchasedPolicies();

        // Assert
        expect(purchasedPolicies, isA<List<InsurancePolicy>>());

        // All returned policies should have active status (purchased and active)
        for (final policy in purchasedPolicies) {
          expect(policy.status, equals(InsurancePolicyStatus.active));
        }
      });

      test('should return active policies', () async {
        // Act
        final activePolicies = await insuranceService.getActivePolicies();

        // Assert
        expect(activePolicies, isA<List<InsurancePolicy>>());

        // All returned policies should have active status
        for (final policy in activePolicies) {
          expect(policy.status, equals(InsurancePolicyStatus.active));
        }
      });

      test('should get policy by ID', () async {
        // Arrange
        final allPolicies = await insuranceService.getAvailablePolicies();
        final testPolicyId = allPolicies.first.id;

        // Act
        final policy = await insuranceService.getPolicyById(testPolicyId);

        // Assert
        expect(policy, isNotNull);
        expect(policy!.id, equals(testPolicyId));
      });

      test('should return null for non-existent policy ID', () async {
        // Act
        final policy = await insuranceService.getPolicyById('non-existent-id');

        // Assert
        expect(policy, isNull);
      });

      test('should get recommended policies', () async {
        // Arrange
        final destinationCountries = ['United States', 'Canada'];
        final startDate = DateTime.now().add(const Duration(days: 30));
        final endDate = startDate.add(const Duration(days: 7));
        const travelerCount = 2;
        const desiredCoverageTypes = [
          InsuranceCoverageType.medical,
          InsuranceCoverageType.cancellation,
        ];

        // Act
        final recommendedPolicies =
            await insuranceService.getRecommendedPolicies(
          destinationCountries: destinationCountries,
          startDate: startDate,
          endDate: endDate,
          travelerCount: travelerCount,
          desiredCoverageTypes: desiredCoverageTypes,
        );

        // Assert
        expect(recommendedPolicies, isA<List<InsurancePolicy>>());

        // Verify that recommended policies have the desired coverage types
        for (final policy in recommendedPolicies) {
          final policyCoverageTypes = policy.coverages
              .where((c) => c.isIncluded)
              .map((c) => c.type)
              .toSet();

          // Should have at least some of the desired coverage types
          expect(
            policyCoverageTypes.intersection(desiredCoverageTypes.toSet()),
            isNotEmpty,
          );
        }
      });
    });

    group('Claims Management', () {
      test('should return all claims', () async {
        // Act
        final claims = await insuranceService.getClaims();

        // Assert
        expect(claims, isA<List<InsuranceClaim>>());
      });

      test('should return active claims', () async {
        // Act
        final activeClaims = await insuranceService.getActiveClaims();

        // Assert
        expect(activeClaims, isA<List<InsuranceClaim>>());

        // All returned claims should not be closed
        for (final claim in activeClaims) {
          expect(claim.status, isNot(equals(InsuranceClaimStatus.closed)));
        }
      });

      test('should get claim by ID', () async {
        // Arrange
        final allClaims = await insuranceService.getClaims();
        if (allClaims.isNotEmpty) {
          final testClaimId = allClaims.first.id;

          // Act
          final claim = await insuranceService.getClaimById(testClaimId);

          // Assert
          expect(claim, isNotNull);
          expect(claim!.id, equals(testClaimId));
        }
      });

      test('should return null for non-existent claim ID', () async {
        // Act
        final claim = await insuranceService.getClaimById('non-existent-id');

        // Assert
        expect(claim, isNull);
      });

      test('should get claims by policy ID', () async {
        // Arrange
        final allPolicies = await insuranceService.getAvailablePolicies();
        if (allPolicies.isNotEmpty) {
          final testPolicyId = allPolicies.first.id;

          // Act
          final claims =
              await insuranceService.getClaimsByPolicyId(testPolicyId);

          // Assert
          expect(claims, isA<List<InsuranceClaim>>());

          // All returned claims should be for the specified policy
          for (final claim in claims) {
            expect(claim.policy.id, equals(testPolicyId));
          }
        }
      });
    });

    group('Policy Comparison', () {
      test('should compare policies successfully', () async {
        // Arrange
        final allPolicies = await insuranceService.getAvailablePolicies();
        if (allPolicies.length >= 2) {
          final policyIds = allPolicies.take(2).map((p) => p.id).toList();

          // Act
          final comparison = await insuranceService.comparePolicies(policyIds);

          // Assert
          expect(comparison, isA<Map<String, List<dynamic>>>());
          expect(comparison, isNotEmpty);

          // Should have comparison data for basic fields
          expect(comparison.containsKey('Provider'), isTrue);
          expect(comparison.containsKey('Price'), isTrue);
          expect(comparison.containsKey('Type'), isTrue);
        }
      });

      test('should handle empty policy list for comparison', () async {
        // Act & Assert
        expect(
          () => insuranceService.comparePolicies([]),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Error Handling', () {
      test('should handle service errors gracefully', () async {
        // Test that methods don't throw unexpected exceptions
        expect(() => insuranceService.getInsuranceProviders(), returnsNormally);
        expect(() => insuranceService.getAvailablePolicies(), returnsNormally);
        expect(() => insuranceService.getClaims(), returnsNormally);
      });
    });
  });
}
