import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/currency/exchange_rate_model.dart';
import 'package:culture_connect/models/currency/currency_preference_model.dart';
import 'package:culture_connect/services/currency/currency_data_service.dart';
import 'package:culture_connect/services/currency/exchange_rate_api_service.dart';
import 'package:culture_connect/services/currency/currency_conversion_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/location_service.dart';
import 'package:geolocator/geolocator.dart';

// Mock classes
class MockExchangeRateApiService extends Mock
    implements ExchangeRateApiService {}

class MockCurrencyDataService extends Mock implements CurrencyDataService {}

class MockLoggingService extends Mock implements LoggingService {
  @override
  void debug(String tag, String message,
      [dynamic data, StackTrace? stackTrace]) {}

  @override
  void error(String tag, String message,
      [dynamic error, StackTrace? stackTrace]) {}

  @override
  void info(String tag, String message,
      [dynamic data, StackTrace? stackTrace]) {}
}

class MockLocationService extends Mock implements LocationService {
  Future<Position?> getCurrentLocation() async => null;

  Future<String?> getCountryFromCoordinates(
          double latitude, double longitude) async =>
      null;
}

class MockSharedPreferences extends Mock implements SharedPreferences {}

void main() {
  late MockExchangeRateApiService mockExchangeRateApiService;
  late MockCurrencyDataService mockCurrencyDataService;
  late MockLoggingService mockLoggingService;
  late MockLocationService mockLocationService;
  late MockSharedPreferences mockSharedPreferences;
  late CurrencyConversionService currencyConversionService;

  setUp(() {
    mockExchangeRateApiService = MockExchangeRateApiService();
    mockCurrencyDataService = MockCurrencyDataService();
    mockLoggingService = MockLoggingService();
    mockLocationService = MockLocationService();
    mockSharedPreferences = MockSharedPreferences();

    currencyConversionService = CurrencyConversionService(
      exchangeRateApiService: mockExchangeRateApiService,
      currencyDataService: mockCurrencyDataService,
      loggingService: mockLoggingService,
      locationService: mockLocationService,
      preferences: mockSharedPreferences,
    );
  });

  group('CurrencyConversionService', () {
    test('should load default preferences when no preferences are stored', () {
      // Arrange
      when(mockSharedPreferences.getString('currency_preferences'))
          .thenReturn('');

      // Act & Assert
      expect(currencyConversionService.currencyPreferences,
          equals(CurrencyPreferenceModel.defaultPreferences));
    });

    test('should convert currency correctly', () async {
      // Arrange
      const fromCurrency = 'USD';
      const toCurrency = 'EUR';
      const amount = 100.0;
      const rate = 0.85;

      final exchangeRate = ExchangeRateModel(
        baseCurrency: fromCurrency,
        targetCurrency: toCurrency,
        rate: rate,
        timestamp: DateTime.now(),
        source: 'Test',
      );

      when(mockExchangeRateApiService.getExchangeRate(fromCurrency, toCurrency))
          .thenAnswer((_) async => exchangeRate);

      // Act
      final result = await currencyConversionService.convertCurrency(
        amount,
        fromCurrency,
        toCurrency,
      );

      // Assert
      expect(result, equals(amount * rate));
      verify(mockExchangeRateApiService.getExchangeRate(
              fromCurrency, toCurrency))
          .called(1);
    });

    test('should return original amount when currencies are the same',
        () async {
      // Arrange
      const currency = 'USD';
      const amount = 100.0;

      // Act
      final result = await currencyConversionService.convertCurrency(
        amount,
        currency,
        currency,
      );

      // Assert
      expect(result, equals(amount));
      // Verify no API calls were made
    });

    test('should format amount according to currency rules', () {
      // Arrange
      const currencyCode = 'USD';
      const amount = 1234.56;
      const formattedAmount = '\$1,234.56';

      when(mockCurrencyDataService.formatAmount(currencyCode, amount))
          .thenReturn(formattedAmount);

      // Act
      final result =
          currencyConversionService.formatAmount(currencyCode, amount);

      // Assert
      expect(result, equals(formattedAmount));
      verify(mockCurrencyDataService.formatAmount(currencyCode, amount))
          .called(1);
    });

    test('should add currency to favorite currencies', () async {
      // Arrange
      const currencyCode = 'JPY';

      when(mockSharedPreferences.getString('currency_preferences'))
          .thenReturn('');
      when(mockSharedPreferences.setString('currency_preferences', 'test'))
          .thenAnswer((_) async => true);

      // Act
      await currencyConversionService.addFavoriteCurrency(currencyCode);

      // Assert
      expect(
        currencyConversionService.currencyPreferences.favoriteCurrencies,
        contains(currencyCode),
      );
    });

    test('CurrencyConversionService should be created successfully', () {
      // Assert
      expect(currencyConversionService, isNotNull);
    });

    test('CurrencyConversionService should handle currency conversion',
        () async {
      // Act & Assert - Should not throw
      expect(() async {
        await currencyConversionService.convertCurrency(100.0, 'USD', 'EUR');
      }, returnsNormally);
    });

    test('CurrencyConversionService should format amount correctly', () {
      // Act
      final formatted = currencyConversionService.formatAmount('100.0', 100.0);

      // Assert
      expect(formatted, isA<String>());
      expect(formatted, isNotEmpty);
    });
  });
}
