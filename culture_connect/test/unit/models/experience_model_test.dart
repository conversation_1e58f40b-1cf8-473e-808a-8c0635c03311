import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

void main() {
  group('Experience Model Tests', () {
    test('Experience should be created correctly with required parameters', () {
      // Arrange & Act
      final experience = Experience(
        id: '1',
        title: 'Test Experience',
        description: 'This is a test experience',
        imageUrl: 'https://example.com/image.jpg',
        category: 'Cultural Tours',
        location: 'San Francisco, CA',
        coordinates: const LatLng(37.7749, -122.4194),
        price: 99.99,
        rating: 4.5,
        reviewCount: 100,
        guideId: 'guide1',
        guideName: '<PERSON>',
        guideImageUrl: 'https://example.com/guide.jpg',
        languages: const ['English', 'Spanish'],
        includedItems: const ['Guide', 'Transportation'],
        requirements: const ['Basic fitness level'],
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 5, 1),
      );

      // Assert
      expect(experience.id, '1');
      expect(experience.title, 'Test Experience');
      expect(experience.description, 'This is a test experience');
      expect(experience.imageUrl, 'https://example.com/image.jpg');
      expect(experience.category, 'Cultural Tours');
      expect(experience.location, 'San Francisco, CA');
      expect(experience.coordinates.latitude, 37.7749);
      expect(experience.coordinates.longitude, -122.4194);
      expect(experience.price, 99.99);
      expect(experience.rating, 4.5);
      expect(experience.reviewCount, 100);
      expect(experience.guideId, 'guide1');
      expect(experience.guideName, 'John Doe');
      expect(experience.guideImageUrl, 'https://example.com/guide.jpg');
      expect(experience.languages, const ['English', 'Spanish']);
      expect(experience.includedItems, const ['Guide', 'Transportation']);
      expect(experience.requirements, const ['Basic fitness level']);
      expect(experience.createdAt, DateTime(2023, 1, 1));
      expect(experience.updatedAt, DateTime(2023, 5, 1));
    });

    test('Experience copyWith should work correctly', () {
      // Arrange
      final experience = Experience(
        id: '1',
        title: 'Test Experience',
        description: 'This is a test experience',
        imageUrl: 'https://example.com/image.jpg',
        category: 'Cultural Tours',
        location: 'San Francisco, CA',
        coordinates: const LatLng(37.7749, -122.4194),
        price: 99.99,
        rating: 4.5,
        reviewCount: 100,
        guideId: 'guide1',
        guideName: 'John Doe',
        guideImageUrl: 'https://example.com/guide.jpg',
        languages: const ['English', 'Spanish'],
        includedItems: const ['Guide', 'Transportation'],
        requirements: const ['Basic fitness level'],
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 5, 1),
      );

      // Act
      final copy = experience.copyWith(
        title: 'Updated Experience',
        price: 129.99,
        rating: 4.8,
      );

      // Assert
      expect(copy.id, '1'); // Unchanged
      expect(copy.title, 'Updated Experience'); // Changed
      expect(copy.description, 'This is a test experience'); // Unchanged
      expect(copy.price, 129.99); // Changed
      expect(copy.rating, 4.8); // Changed
      expect(copy.reviewCount, 100); // Unchanged
    });
  });
}
