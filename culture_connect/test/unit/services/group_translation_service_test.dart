import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';

void main() {
  group('Group Translation Model Tests', () {
    test('LanguageModel should be created with required parameters', () {
      // Arrange & Act
      const language = LanguageModel(
        code: 'en',
        name: 'English',
        flag: '🇺🇸',
      );

      // Assert
      expect(language.code, 'en');
      expect(language.name, 'English');
      expect(language.flag, '🇺🇸');
      expect(language.isOfflineAvailable, false);
    });

    test('ParticipantLanguagePreference should be created correctly', () {
      // Arrange
      const language = LanguageModel(
        code: 'fr',
        name: 'French',
        flag: '🇫🇷',
      );

      // Act
      const preference = ParticipantLanguagePreference(
        userId: 'user-123',
        displayName: 'Test User',
        preferredLanguage: language,
      );

      // Assert
      expect(preference.userId, 'user-123');
      expect(preference.displayName, 'Test User');
      expect(preference.preferredLanguage.code, 'fr');
      expect(preference.autoTranslate, true);
      expect(preference.showOriginalText, false);
    });

    test('GroupTranslationSettings should be created correctly', () {
      // Arrange
      const language = LanguageModel(
        code: 'es',
        name: 'Spanish',
        flag: '🇪🇸',
      );

      const preference = ParticipantLanguagePreference(
        userId: 'user-456',
        displayName: 'Spanish User',
        preferredLanguage: language,
      );

      // Act
      const settings = GroupTranslationSettings(
        groupId: 'group-789',
        participantPreferences: {
          'user-456': preference,
        },
      );

      // Assert
      expect(settings.groupId, 'group-789');
      expect(settings.participantPreferences.length, 1);
      expect(settings.participantPreferences['user-456']?.userId, 'user-456');
      expect(settings.autoDetectLanguages, true);
      expect(settings.enableRealTimeTranslation, true);
    });

    test('GroupMessageTranslation should be created correctly', () {
      // Act
      final translation = GroupMessageTranslation(
        messageId: 'msg-123',
        originalText: 'Hello world',
        originalLanguageCode: 'en',
        translations: {
          'fr': 'Bonjour le monde',
          'es': 'Hola mundo',
        },
        translatedAt: DateTime(2023, 1, 1),
      );

      // Assert
      expect(translation.messageId, 'msg-123');
      expect(translation.originalText, 'Hello world');
      expect(translation.originalLanguageCode, 'en');
      expect(translation.translations['fr'], 'Bonjour le monde');
      expect(translation.translations['es'], 'Hola mundo');
      expect(translation.hasTranslationForLanguage('fr'), true);
      expect(translation.hasTranslationForLanguage('de'), false);
    });

    test('GroupMessageTranslation should add translations correctly', () {
      // Arrange
      final translation = GroupMessageTranslation(
        messageId: 'msg-456',
        originalText: 'Good morning',
        originalLanguageCode: 'en',
        translations: {},
        translatedAt: DateTime(2023, 1, 1),
      );

      // Act
      final updatedTranslation = translation.addTranslation('fr', 'Bonjour');

      // Assert
      expect(updatedTranslation.translations['fr'], 'Bonjour');
      expect(updatedTranslation.hasTranslationForLanguage('fr'), true);
      expect(updatedTranslation.getTranslationForLanguage('fr'), 'Bonjour');
    });
  });
}
