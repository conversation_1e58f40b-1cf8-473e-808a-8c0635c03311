import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/services/ar_lazy_loading_service.dart';

// Generate mocks for dependencies
@GenerateMocks([Directory, File])
import 'ar_lazy_loading_service_test.mocks.dart';

// Mock for SharedPreferences
class MockSharedPreferences extends Mock implements SharedPreferences {
  final Map<String, dynamic> values = {};

  @override
  bool getBool(String key) => values[key] ?? false;

  @override
  int? getInt(String key) => values[key];

  @override
  Future<bool> setInt(String key, int value) async {
    values[key] = value;
    return true;
  }

  @override
  Future<bool> setBool(String key, bool value) async {
    values[key] = value;
    return true;
  }
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late ARLazyLoadingService service;
  late MockSharedPreferences mockPreferences;
  late MockDirectory mockDirectory;
  late MockFile mockFile;

  // Setup method to initialize mocks and service before each test
  setUp(() {
    mockPreferences = MockSharedPreferences();
    mockDirectory = MockDirectory();
    mockFile = MockFile();

    // Mock path_provider
    const pathProviderChannel =
        MethodChannel('plugins.flutter.io/path_provider');
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(pathProviderChannel,
            (MethodCall methodCall) async {
      if (methodCall.method == 'getApplicationDocumentsDirectory') {
        return '/mock/docs/path';
      } else if (methodCall.method == 'getTemporaryDirectory') {
        return '/mock/temp/path';
      }
      return null;
    });

    // Mock asset loading
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMessageHandler('flutter/assets', (ByteData? message) async {
      // Return a small ByteData to simulate asset content
      final buffer = Uint8List(10).buffer;
      return ByteData.view(buffer);
    });

    // Setup directory mocks for AR models
    final arModelsDir = MockDirectory();
    when(arModelsDir.exists()).thenAnswer((_) async => true);
    when(arModelsDir.path).thenReturn('/mock/docs/path/ar_models');

    // Setup directory mocks for AR textures
    final arTexturesDir = MockDirectory();
    when(arTexturesDir.exists()).thenAnswer((_) async => true);
    when(arTexturesDir.path).thenReturn('/mock/docs/path/ar_textures');

    // Setup file mocks for AR models
    when(mockFile.exists()).thenAnswer((_) async => true);
    when(mockFile.path).thenReturn('/mock/docs/path/ar_models/landmark_1.glb');
    when(mockFile.writeAsBytes(any)).thenAnswer((_) async => mockFile);

    // Setup SharedPreferences mock for getInstance
    SharedPreferences.setMockInitialValues({});

    // Create service instance
    service = ARLazyLoadingService();
  });

  // Teardown method to clean up after each test
  tearDown(() {
    // Reset mocks
    reset(mockPreferences);
    reset(mockDirectory);
    reset(mockFile);

    // Reset method channels
    const pathProviderChannel =
        MethodChannel('plugins.flutter.io/path_provider');
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(pathProviderChannel, null);
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMessageHandler('flutter/assets', null);
  });

  group('ARLazyLoadingService Tests', () {
    test('initializeARFeatures should complete successfully', () async {
      // Act
      final result = await service.initializeARFeatures();

      // Assert
      expect(result, true);
      expect(service.arFeaturesLoaded, true);
    });

    test('arFeaturesLoadingFuture should complete when initialization is done',
        () async {
      // Arrange
      final future = service.arFeaturesLoadingFuture;

      // Act
      await service.initializeARFeatures();
      final result = await future;

      // Assert
      expect(result, true);
    });

    test('initializeARFeatures should handle errors gracefully', () async {
      // Arrange
      // Mock path_provider to throw an error
      const pathProviderChannel =
          MethodChannel('plugins.flutter.io/path_provider');
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(pathProviderChannel,
              (MethodCall methodCall) async {
        throw Exception('Mock error');
      });

      // Act & Assert
      try {
        final result = await service.initializeARFeatures();
        // Should return false when there are errors
        expect(result, false);
      } catch (e) {
        fail('initializeARFeatures should handle errors gracefully: $e');
      } finally {
        // Reset the mock to not affect other tests
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(pathProviderChannel, null);
      }
    });

    test('getARModelFilePath should return null for non-existent model',
        () async {
      // Arrange
      // Initialize AR features first
      await service.initializeARFeatures();

      // Setup mock file to not exist
      final nonExistentFile = MockFile();
      when(nonExistentFile.exists()).thenAnswer((_) async => false);
      when(mockDirectory.path).thenReturn('/mock/docs/path');

      // Act
      final result = await service.getARModelFilePath('non_existent_model.glb');

      // Assert
      expect(result, isNull);
    });

    test('getARTextureFilePath should return null for non-existent texture',
        () async {
      // Arrange
      // Initialize AR features first
      await service.initializeARFeatures();

      // Setup mock file to not exist
      final nonExistentFile = MockFile();
      when(nonExistentFile.exists()).thenAnswer((_) async => false);
      when(mockDirectory.path).thenReturn('/mock/docs/path');

      // Act
      final result =
          await service.getARTextureFilePath('non_existent_texture.jpg');

      // Assert
      expect(result, isNull);
    });

    test('getARModelFilePath should return path for existing model', () async {
      // Arrange
      // Initialize AR features first
      await service.initializeARFeatures();

      // Setup mock file to exist
      final existingFile = MockFile();
      when(existingFile.exists()).thenAnswer((_) async => true);
      when(existingFile.path)
          .thenReturn('/mock/docs/path/ar_models/landmark_1.glb');
      when(mockDirectory.path).thenReturn('/mock/docs/path');

      // Act
      final result = await service.getARModelFilePath('landmark_1.glb');

      // Assert
      expect(result, isNotNull);
      // Note: In a real test, we would expect the exact path, but since we're mocking
      // the file system, we can't guarantee the exact path will match
    });

    test('getARTextureFilePath should return path for existing texture',
        () async {
      // Arrange
      // Initialize AR features first
      await service.initializeARFeatures();

      // Setup mock file to exist
      final existingFile = MockFile();
      when(existingFile.exists()).thenAnswer((_) async => true);
      when(existingFile.path)
          .thenReturn('/mock/docs/path/ar_textures/texture_1.jpg');
      when(mockDirectory.path).thenReturn('/mock/docs/path');

      // Act
      final result = await service.getARTextureFilePath('texture_1.jpg');

      // Assert
      expect(result, isNotNull);
      // Note: In a real test, we would expect the exact path, but since we're mocking
      // the file system, we can't guarantee the exact path will match
    });
  });
}
