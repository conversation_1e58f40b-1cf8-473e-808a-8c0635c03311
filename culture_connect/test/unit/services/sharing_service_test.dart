import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:share_plus/share_plus.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/services/sharing_service.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

// Create a mock for Share.share
class MockShare extends Mock {
  static Future<void> share(String text, {String? subject}) async {}
  static Future<void> shareXFiles(List<XFile> files, {String? text}) async {}
}

// Generate mocks
@GenerateMocks([])
void main() {
  late SharingService sharingService;
  late Booking testBooking;
  late Experience testExperience;

  setUp(() {
    sharingService = SharingService();

    final now = DateTime.now();

    // Create a test booking
    testBooking = Booking(
      id: 'booking-123',
      experienceId: 'exp-456',
      date: DateTime(2023, 10, 15),
      timeSlot: TimeSlot(
        startTime: DateTime(2023, 10, 15, 10, 0),
        endTime: DateTime(2023, 10, 15, 12, 0),
      ),
      participantCount: 2,
      totalAmount: 100.0,
      status: BookingStatus.confirmed,
      specialRequirements: 'Vegetarian food',
      createdAt: now.subtract(const Duration(days: 1)),
      updatedAt: now.subtract(const Duration(days: 1)),
    );

    // Create a test experience
    testExperience = Experience(
      id: 'exp-456',
      title: 'Cultural Walking Tour',
      description:
          'Explore the rich cultural heritage of the city with a local guide.',
      imageUrl: 'https://example.com/image.jpg',
      rating: 4.8,
      reviewCount: 25,
      price: 50.0,
      category: 'Cultural Tours',
      location: 'City Center',
      coordinates: const LatLng(0, 0),
      guideId: 'guide-123',
      guideName: 'Local Guide',
      guideImageUrl: 'https://example.com/guide.jpg',
      languages: ['English', 'Spanish'],
      includedItems: ['Tour', 'Refreshments'],
      requirements: ['None'],
      createdAt: now.subtract(const Duration(days: 30)),
      updatedAt: now.subtract(const Duration(days: 1)),
    );
  });

  group('SharingService - Public Methods', () {
    test('shareBooking should work without errors', () async {
      // Act & Assert - Should not throw
      expect(() async {
        await sharingService.shareBooking(
          booking: testBooking,
          experienceTitle: 'Cultural Walking Tour',
        );
      }, returnsNormally);
    });

    test('shareExperience should work without errors', () async {
      // Act & Assert - Should not throw
      expect(() async {
        await sharingService.shareExperience(
          experience: testExperience,
        );
      }, returnsNormally);
    });

    test('shareReview should work without errors', () async {
      // Act & Assert - Should not throw
      expect(() async {
        await sharingService.shareReview(
          experienceTitle: 'Cultural Walking Tour',
          rating: 4.5,
          reviewText: 'Amazing experience!',
        );
      }, returnsNormally);
    });

    test('SharingService should be singleton', () {
      // Arrange
      final service1 = SharingService();
      final service2 = SharingService();

      // Assert
      expect(service1, same(service2));
    });
  });
}
