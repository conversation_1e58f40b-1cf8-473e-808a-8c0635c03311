import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/models/booking.dart';

void main() {
  // Simple test without complex mocking
  group('Review Service Model Tests', () {
    test('Booking model should be created correctly', () {
      // Arrange
      final tomorrow = DateTime.fromMillisecondsSinceEpoch(
          DateTime.now().millisecondsSinceEpoch + 86400000);

      // Act
      final booking = Booking(
        id: 'booking-123',
        experienceId: 'exp-456',
        date: tomorrow,
        timeSlot: TimeSlot(
          startTime:
              DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 10, 0),
          endTime: DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 12, 0),
        ),
        participantCount: 2,
        totalAmount: 100.0,
        status: BookingStatus.confirmed,
        specialRequirements: 'Vegetarian food',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Assert
      expect(booking.id, 'booking-123');
      expect(booking.experienceId, 'exp-456');
      expect(booking.participantCount, 2);
      expect(booking.totalAmount, 100.0);
      expect(booking.status, BookingStatus.confirmed);
    });

    test('BookingStatus enum should have correct values', () {
      // Assert
      expect(BookingStatus.pending, isNotNull);
      expect(BookingStatus.confirmed, isNotNull);
      expect(BookingStatus.completed, isNotNull);
      expect(BookingStatus.cancelled, isNotNull);
    });

    test('TimeSlot should be created correctly', () {
      // Arrange
      final now = DateTime.now();
      final startTime = DateTime(now.year, now.month, now.day, 10, 0);
      final endTime = DateTime(now.year, now.month, now.day, 12, 0);

      // Act
      final timeSlot = TimeSlot(
        startTime: startTime,
        endTime: endTime,
      );

      // Assert
      expect(timeSlot.startTime, startTime);
      expect(timeSlot.endTime, endTime);
    });
  });
}
