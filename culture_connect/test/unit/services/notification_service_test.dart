import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/services/notification_service.dart';

void main() {
  late NotificationService notificationService;

  setUp(() {
    notificationService = NotificationService();
  });

  group('NotificationService Tests', () {
    test('NotificationService should be created successfully', () {
      expect(notificationService, isNotNull);
    });

    test('NotificationService should be singleton', () {
      // Arrange
      final service1 = NotificationService();
      final service2 = NotificationService();

      // Assert
      expect(service1, same(service2));
    });

    test('initialize should complete without error', () async {
      // Act & Assert
      expect(() async {
        await notificationService.initialize();
      }, returnsNormally);
    });

    test('requestPermissions should complete without error', () async {
      // Act & Assert
      expect(() async {
        await notificationService.requestPermissions();
      }, returnsNormally);
    });
  });
}
