import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/services/ar_recording_service.dart';
import 'package:image_picker/image_picker.dart';

// Generate mocks for dependencies
@GenerateMocks([File, Directory, ImagePicker, XFile])
import 'ar_recording_service_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late ARRecordingService service;
  late MockFile mockFile;
  late MockDirectory mockDirectory;
  late MockImagePicker mockImagePicker;
  late MockXFile mockXFile;
  bool onRecordingStateChangedCalled = false;

  setUp(() {
    mockFile = MockFile();
    mockDirectory = MockDirectory();
    mockImagePicker = MockImagePicker();
    mockXFile = MockXFile();
    onRecordingStateChangedCalled = false;

    // Mock path_provider
    const pathProviderChannel =
        MethodChannel('plugins.flutter.io/path_provider');
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(pathProviderChannel,
            (MethodCall methodCall) async {
      if (methodCall.method == 'getApplicationDocumentsDirectory') {
        return '/mock/docs/path';
      } else if (methodCall.method == 'getTemporaryDirectory') {
        return '/mock/temp/path';
      }
      return null;
    });

    // Setup directory mocks
    when(mockDirectory.exists()).thenAnswer((_) async => true);
    when(mockDirectory.path).thenReturn('/mock/docs/path/recordings');
    when(mockDirectory.create(recursive: true))
        .thenAnswer((_) async => mockDirectory);

    // Setup file mocks
    when(mockFile.exists()).thenAnswer((_) async => true);
    when(mockFile.path).thenReturn('/mock/docs/path/recordings/recording.mp4');
    when(mockFile.delete()).thenAnswer((_) async => mockFile);
    when(mockFile.copy(any)).thenAnswer((_) async => mockFile);

    // Setup image picker mocks
    when(mockXFile.path).thenReturn('/mock/temp/path/image.jpg');
    when(mockImagePicker.pickImage(source: ImageSource.camera))
        .thenAnswer((_) async => mockXFile);
    when(mockImagePicker.pickVideo(source: ImageSource.camera))
        .thenAnswer((_) async => mockXFile);

    // Create service instance
    service = ARRecordingService();

    // Inject mocks - Note: This assumes we've added a setter for testing
    // In a real implementation, we would need to add this to the ARRecordingService class
  });

  tearDown(() {
    // Reset mocks
    reset(mockFile);
    reset(mockDirectory);
    reset(mockImagePicker);
    reset(mockXFile);

    // Reset method channels
    const pathProviderChannel =
        MethodChannel('plugins.flutter.io/path_provider');
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(pathProviderChannel, null);

    // Dispose service
    service.dispose();
  });

  group('ARRecordingService Tests', () {
    test('initialize should setup recording service', () async {
      // Act
      await service.initialize(
        onRecordingStateChanged: () {
          onRecordingStateChangedCalled = true;
        },
        onRecordingTimerUpdated: (seconds) {
          // Timer updated callback
        },
      );

      // Assert
      expect(onRecordingStateChangedCalled,
          false); // Not called during initialization
    });

    test('startRecording should start recording', () async {
      // Arrange
      await service.initialize(
        onRecordingStateChanged: () {
          onRecordingStateChangedCalled = true;
        },
        onRecordingTimerUpdated: (seconds) {
          // Timer updated callback
        },
      );
      onRecordingStateChangedCalled = false; // Reset flag

      // Act
      final result = await service.startRecording();

      // Assert
      expect(result, true);
      expect(service.isRecording, true);
      expect(onRecordingStateChangedCalled, true);
    });

    test('pauseRecording should pause recording', () async {
      // Arrange
      await service.initialize(
        onRecordingStateChanged: () {
          onRecordingStateChangedCalled = true;
        },
        onRecordingTimerUpdated: (seconds) {
          // Timer updated callback
        },
      );
      await service.startRecording();
      onRecordingStateChangedCalled = false; // Reset flag

      // Act
      service.pauseRecording();

      // Assert
      expect(service.isPaused, true);
      expect(onRecordingStateChangedCalled, true);
    });

    test('resumeRecording should resume recording', () async {
      // Arrange
      await service.initialize(
        onRecordingStateChanged: () {
          onRecordingStateChangedCalled = true;
        },
        onRecordingTimerUpdated: (seconds) {
          // Timer updated callback
        },
      );
      await service.startRecording();
      service.pauseRecording();
      onRecordingStateChangedCalled = false; // Reset flag

      // Act
      service.resumeRecording();

      // Assert
      expect(service.isPaused, false);
      expect(onRecordingStateChangedCalled, true);
    });

    test('stopRecording should stop recording', () async {
      // Arrange
      await service.initialize(
        onRecordingStateChanged: () {
          onRecordingStateChangedCalled = true;
        },
        onRecordingTimerUpdated: (seconds) {
          // Timer updated callback
        },
      );
      await service.startRecording();
      onRecordingStateChangedCalled = false; // Reset flag

      // Act
      final result = await service.stopRecording();

      // Assert
      expect(result, true);
      expect(service.isRecording, false);
      expect(onRecordingStateChangedCalled, true);
    });

    test('takeScreenshot should capture screenshot', () async {
      // Arrange
      await service.initialize(
        onRecordingStateChanged: () {},
        onRecordingTimerUpdated: (seconds) {},
      );

      // Act
      final result = await service.takeScreenshot();

      // Assert
      expect(result, isA<File?>());
    });

    test('recordVideo should record video', () async {
      // Arrange
      await service.initialize(
        onRecordingStateChanged: () {},
        onRecordingTimerUpdated: (seconds) {},
      );

      // Act
      final result = await service.recordVideo();

      // Assert
      expect(result, isA<File?>());
    });

    test('recordingDurationInSeconds should return current duration', () async {
      // Arrange
      await service.initialize(
        onRecordingStateChanged: () {},
        onRecordingTimerUpdated: (seconds) {
          // Timer updated callback
        },
      );
      await service.startRecording();

      // Act
      final duration = service.recordingDurationInSeconds;

      // Assert
      expect(duration, isA<int>());
      expect(duration, 0); // In a real test, this would increase over time
    });

    test('recordingDurationFormatted should return formatted duration',
        () async {
      // Arrange
      await service.initialize(
        onRecordingStateChanged: () {},
        onRecordingTimerUpdated: (seconds) {},
      );
      await service.startRecording();

      // Act
      final formattedDuration = service.recordingDurationFormatted;

      // Assert
      expect(formattedDuration, isA<String>());
      expect(formattedDuration,
          '00:00'); // In a real test, this would change over time
    });

    test('shareRecording should share recording', () async {
      // Arrange
      await service.initialize(
        onRecordingStateChanged: () {},
        onRecordingTimerUpdated: (seconds) {},
      );
      await service.startRecording();
      await service.stopRecording();

      // Act
      final result = await service.shareRecording(
        title: 'Test Recording',
        description: 'This is a test recording',
        tags: ['test', 'recording'],
      );

      // Assert
      expect(result, true);
    });

    test('saveRecording should save recording', () async {
      // Arrange
      await service.initialize(
        onRecordingStateChanged: () {},
        onRecordingTimerUpdated: (seconds) {},
      );
      await service.startRecording();
      await service.stopRecording();

      // Act
      final result = await service.saveRecording(
        title: 'Test Recording',
        description: 'This is a test recording',
        tags: ['test', 'recording'],
      );

      // Assert
      expect(result, true);
    });

    test('dispose should clean up resources', () async {
      // Arrange
      await service.initialize(
        onRecordingStateChanged: () {},
        onRecordingTimerUpdated: (seconds) {},
      );
      await service.startRecording();

      // Act
      service.dispose();

      // Assert
      expect(service.isRecording, false);
    });
  });
}
