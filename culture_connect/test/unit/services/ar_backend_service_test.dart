import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/services/ar_backend_service.dart';
import 'package:culture_connect/models/landmark.dart';
import 'package:culture_connect/models/ar_model.dart';

void main() {
  late ARBackendService service;

  setUp(() {
    // Initialize Flutter binding for tests
    TestWidgetsFlutterBinding.ensureInitialized();

    // Create service instance
    service = ARBackendService();
  });

  group('ARBackendService Tests', () {
    test('ARBackendService should initialize without errors', () async {
      // Act & Assert - Should not throw
      expect(() async {
        await service.initialize();
      }, returnsNormally);
    });

    test('ARBackendService should be singleton', () {
      // Arrange
      final service1 = ARBackendService();
      final service2 = ARBackendService();

      // Assert
      expect(service1, same(service2));
    });

    test('getLandmarks should handle errors gracefully', () async {
      // Act & Assert - Should not throw and return a list
      final landmarks = await service.getLandmarks(
        latitude: 48.8584,
        longitude: 2.2945,
        radius: 1000,
      );

      expect(landmarks, isA<List<Landmark>>());
    });

    test('getARModel should handle errors gracefully', () async {
      // Act & Assert - Should not throw
      final model = await service.getARModel('test-model-id');

      expect(model, anyOf(isA<ARModel>(), isNull));
    });
  });
}
