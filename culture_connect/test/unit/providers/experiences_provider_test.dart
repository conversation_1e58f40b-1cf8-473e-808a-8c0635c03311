import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

void main() {
  group('Experience Model Tests', () {
    test('Experience model should create correctly with required parameters',
        () {
      // Arrange & Act
      final experience = Experience(
        id: '1',
        title: 'Cultural Tour',
        description: 'Explore local culture',
        imageUrl: 'https://example.com/image1.jpg',
        rating: 4.5,
        reviewCount: 100,
        price: 99.99,
        category: 'Cultural Tours',
        location: 'San Francisco, CA',
        coordinates: const LatLng(37.7749, -122.4194),
        guideId: 'guide1',
        guideName: '<PERSON>',
        guideImageUrl: 'https://example.com/guide1.jpg',
        languages: const ['English', 'Spanish'],
        includedItems: const ['Transportation', 'Guide'],
        requirements: const ['Basic fitness level'],
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
      );

      // Assert
      expect(experience.id, '1');
      expect(experience.title, 'Cultural Tour');
      expect(experience.description, 'Explore local culture');
      expect(experience.rating, 4.5);
      expect(experience.reviewCount, 100);
      expect(experience.price, 99.99);
      expect(experience.category, 'Cultural Tours');
      expect(experience.location, 'San Francisco, CA');
      expect(experience.coordinates.latitude, 37.7749);
      expect(experience.coordinates.longitude, -122.4194);
      expect(experience.guideId, 'guide1');
      expect(experience.guideName, 'John Doe');
      expect(experience.languages, const ['English', 'Spanish']);
      expect(experience.includedItems, const ['Transportation', 'Guide']);
      expect(experience.requirements, const ['Basic fitness level']);
    });

    test('Experience model should handle optional parameters correctly', () {
      // Arrange & Act
      final experience = Experience(
        id: '2',
        title: 'Food Tour',
        description: 'Taste local cuisine',
        imageUrl: 'https://example.com/image2.jpg',
        rating: 4.8,
        reviewCount: 150,
        price: 79.99,
        category: 'Food & Drink',
        location: 'San Francisco, CA',
        coordinates: const LatLng(37.7750, -122.4195),
        guideId: 'guide2',
        guideName: 'Jane Smith',
        guideImageUrl: 'https://example.com/guide2.jpg',
        languages: const ['English'],
        includedItems: const ['Food samples', 'Guide'],
        requirements: const ['No dietary restrictions'],
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
        durationHours: 3.0,
        isAccessible: true,
        maxParticipants: 15,
        currentParticipants: 8,
        isFeatured: true,
      );

      // Assert
      expect(experience.durationHours, 3.0);
      expect(experience.isAccessible, true);
      expect(experience.maxParticipants, 15);
      expect(experience.currentParticipants, 8);
      expect(experience.isFeatured, true);
    });

    test('Experience model should handle copyWith correctly', () {
      // Arrange
      final originalExperience = Experience(
        id: '3',
        title: 'Adventure Tour',
        description: 'Outdoor activities',
        imageUrl: 'https://example.com/image3.jpg',
        rating: 4.2,
        reviewCount: 80,
        price: 129.99,
        category: 'Adventure',
        location: 'San Francisco, CA',
        coordinates: const LatLng(37.7751, -122.4196),
        guideId: 'guide3',
        guideName: 'Bob Wilson',
        guideImageUrl: 'https://example.com/guide3.jpg',
        languages: const ['English'],
        includedItems: const ['Equipment', 'Guide'],
        requirements: const ['Good fitness level'],
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
      );

      // Act
      final updatedExperience = originalExperience.copyWith(
        rating: 4.5,
        reviewCount: 100,
        isFeatured: true,
      );

      // Assert
      expect(updatedExperience.id, '3');
      expect(updatedExperience.title, 'Adventure Tour');
      expect(updatedExperience.rating, 4.5); // Updated
      expect(updatedExperience.reviewCount, 100); // Updated
      expect(updatedExperience.isFeatured, true); // Updated
      expect(updatedExperience.price, 129.99); // Unchanged
    });
  });
}
