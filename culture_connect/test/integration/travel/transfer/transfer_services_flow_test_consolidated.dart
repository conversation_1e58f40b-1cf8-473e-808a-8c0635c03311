import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/models/travel/transfer/transfer_vehicle.dart';
import 'package:culture_connect/models/travel/transfer/transfer_driver.dart';
import 'package:culture_connect/models/travel/transfer/transfer_service.dart';
import 'package:culture_connect/models/travel/transfer/transfer_booking.dart';
import 'package:culture_connect/models/travel/transfer/transfer_location.dart';
import 'package:culture_connect/models/geo_location.dart';

void main() {
  group('Transfer Models Integration Tests', () {
    test('Transfer models should work together correctly', () {
      // Arrange - Create test vehicle
      const testVehicle = TransferVehicle(
        type: TransferVehicleType.sedan,
        make: 'Toyota',
        model: 'Camry',
        year: 2022,
        color: 'Black',
        licensePlate: 'ABC123',
        passengerCapacity: 4,
        luggageCapacity: 2,
        features: ['Air Conditioning', 'WiFi', 'USB'],
        imageUrl: 'https://example.com/vehicle.jpg',
        additionalImages: ['https://example.com/vehicle2.jpg'],
        hasAirConditioning: true,
        hasWifi: true,
        hasUsb: true,
        hasChildSeat: false,
        hasWheelchairAccess: false,
      );

      // Arrange - Create test driver
      const testDriver = TransferDriver(
        id: 'driver1',
        name: 'John Doe',
        photoUrl: 'https://example.com/driver.jpg',
        rating: 4.8,
        reviewCount: 120,
        yearsOfExperience: 5,
        isVerified: true,
        languages: ['English', 'Spanish'],
      );

      // Arrange - Create test transfer service
      final testTransfer = TransferService(
        id: 'transfer1',
        name: 'Airport to Hotel Transfer',
        description: 'Comfortable transfer from airport to your hotel',
        provider: 'ExampleTransfers',
        location: 'New York',
        coordinates: const GeoLocation(latitude: 40.7128, longitude: -74.0060),
        imageUrl: 'https://example.com/transfer.jpg',
        additionalImages: const ['https://example.com/transfer2.jpg'],
        price: 50.0,
        currency: 'USD',
        rating: 4.5,
        reviewCount: 100,
        vehicleType: TransferVehicleType.sedan,
        vehicle: testVehicle,
        driver: testDriver,
        passengerCapacity: 4,
        luggageCapacity: 2,
        isPrivate: true,
        includesMeetAndGreet: true,
        includesFlightTracking: true,
        includesWaitingTime: true,
        freeWaitingTimeMinutes: 60,
        isAvailable24Hours: true,
        minimumNoticeHours: 2,
        cancellationPolicy: 'Free cancellation up to 24 hours before pickup',
        freeCancellationHours: 24,
        amenities: const ['Bottled water', 'Newspapers'],
        isFeatured: true,
        isOnSale: false,
        isAvailable: true,
        tags: const ['Airport', 'Transfer'],
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
      );

      // Arrange - Create test locations
      const pickupLocation = TransferLocation(
        id: 'location1',
        type: TransferLocationType.airport,
        name: 'JFK Airport',
        address: 'JFK Airport, Queens, NY',
        city: 'New York',
        country: 'USA',
        coordinates: GeoLocation(latitude: 40.6413, longitude: -73.7781),
      );

      const dropoffLocation = TransferLocation(
        id: 'location2',
        type: TransferLocationType.hotel,
        name: 'Hilton Hotel',
        address: '1335 Avenue of the Americas, New York',
        city: 'New York',
        country: 'USA',
        coordinates: GeoLocation(latitude: 40.7621, longitude: -73.9771),
      );

      // Arrange - Create test booking
      final testBooking = TransferBooking(
        id: 'booking1',
        userId: 'user1',
        transferId: 'transfer1',
        transferService: testTransfer,
        pickupLocation: pickupLocation,
        dropoffLocation: dropoffLocation,
        pickupDateTime: DateTime(2023, 6, 1, 14, 0),
        passengerCount: 2,
        luggageCount: 2,
        contactName: 'Jane Smith',
        contactPhone: '+1234567890',
        contactEmail: '<EMAIL>',
        status: TransferBookingStatus.confirmed,
        totalPrice: 50.0,
        currency: 'USD',
        confirmationCode: 'ABC123',
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
      );

      // Assert - Verify vehicle properties
      expect(testVehicle.type, TransferVehicleType.sedan);
      expect(testVehicle.make, 'Toyota');
      expect(testVehicle.model, 'Camry');
      expect(testVehicle.passengerCapacity, 4);
      expect(testVehicle.luggageCapacity, 2);
      expect(testVehicle.hasAirConditioning, true);
      expect(testVehicle.fullName, 'Toyota Camry');

      // Assert - Verify driver properties
      expect(testDriver.id, 'driver1');
      expect(testDriver.name, 'John Doe');
      expect(testDriver.rating, 4.8);
      expect(testDriver.isVerified, true);
      expect(testDriver.languages, ['English', 'Spanish']);

      // Assert - Verify transfer service properties
      expect(testTransfer.id, 'transfer1');
      expect(testTransfer.name, 'Airport to Hotel Transfer');
      expect(testTransfer.vehicleType, TransferVehicleType.sedan);
      expect(testTransfer.vehicle, testVehicle);
      expect(testTransfer.driver, testDriver);
      expect(testTransfer.isPrivate, true);
      expect(testTransfer.includesMeetAndGreet, true);
      expect(testTransfer.freeWaitingTimeMinutes, 60);

      // Assert - Verify location properties
      expect(pickupLocation.type, TransferLocationType.airport);
      expect(pickupLocation.name, 'JFK Airport');
      expect(dropoffLocation.type, TransferLocationType.hotel);
      expect(dropoffLocation.name, 'Hilton Hotel');

      // Assert - Verify booking properties
      expect(testBooking.id, 'booking1');
      expect(testBooking.transferService, testTransfer);
      expect(testBooking.pickupLocation, pickupLocation);
      expect(testBooking.dropoffLocation, dropoffLocation);
      expect(testBooking.status, TransferBookingStatus.confirmed);
      expect(testBooking.totalPrice, 50.0);
      expect(testBooking.confirmationCode, 'ABC123');
    });
  });
}
