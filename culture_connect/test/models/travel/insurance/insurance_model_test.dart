// Flutter imports
import 'package:flutter_test/flutter_test.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart'
    hide InsuranceCoverageType, InsuranceClaimStatus;
import 'package:culture_connect/models/travel/insurance/insurance_coverage_type.dart';

void main() {
  group('InsuranceProvider', () {
    test('should create InsuranceProvider with required fields', () {
      // Arrange & Act
      const provider = InsuranceProvider(
        id: 'provider-1',
        name: 'Test Insurance',
        description: 'Test insurance provider',
        logoUrl: 'https://example.com/logo.png',
        websiteUrl: 'https://example.com',
        phoneNumber: '******-123-4567',
        email: '<EMAIL>',
        rating: 4.5,
        reviewCount: 100,
        isFeatured: true,
        isPartner: false,
        countries: ['US', 'CA'],
      );

      // Assert
      expect(provider.id, equals('provider-1'));
      expect(provider.name, equals('Test Insurance'));
      expect(provider.description, equals('Test insurance provider'));
      expect(provider.logoUrl, equals('https://example.com/logo.png'));
      expect(provider.websiteUrl, equals('https://example.com'));
      expect(provider.phoneNumber, equals('******-123-4567'));
      expect(provider.email, equals('<EMAIL>'));
      expect(provider.rating, equals(4.5));
      expect(provider.reviewCount, equals(100));
      expect(provider.isFeatured, isTrue);
      expect(provider.isPartner, isFalse);
      expect(provider.countries, equals(['US', 'CA']));
      expect(provider.details, isEmpty);
    });

    test('should create InsuranceProvider with optional details', () {
      // Arrange & Act
      const provider = InsuranceProvider(
        id: 'provider-1',
        name: 'Test Insurance',
        description: 'Test insurance provider',
        logoUrl: 'https://example.com/logo.png',
        websiteUrl: 'https://example.com',
        phoneNumber: '******-123-4567',
        email: '<EMAIL>',
        rating: 4.5,
        reviewCount: 100,
        isFeatured: true,
        isPartner: false,
        countries: ['US', 'CA'],
        details: {'specialty': 'travel', 'founded': '2020'},
      );

      // Assert
      expect(
          provider.details, equals({'specialty': 'travel', 'founded': '2020'}));
    });

    test('should have correct rating value', () {
      // Arrange
      const provider = InsuranceProvider(
        id: 'provider-1',
        name: 'Test Insurance',
        description: 'Test insurance provider',
        logoUrl: 'https://example.com/logo.png',
        websiteUrl: 'https://example.com',
        phoneNumber: '******-123-4567',
        email: '<EMAIL>',
        rating: 4.7,
        reviewCount: 100,
        isFeatured: true,
        isPartner: false,
        countries: ['US', 'CA'],
      );

      // Act & Assert
      expect(provider.rating, equals(4.7));
      expect(provider.rating.toString(), equals('4.7'));
    });

    test('should create copy with modified fields', () {
      // Arrange
      const originalProvider = InsuranceProvider(
        id: 'provider-1',
        name: 'Test Insurance',
        description: 'Test insurance provider',
        logoUrl: 'https://example.com/logo.png',
        websiteUrl: 'https://example.com',
        phoneNumber: '******-123-4567',
        email: '<EMAIL>',
        rating: 4.5,
        reviewCount: 100,
        isFeatured: true,
        isPartner: false,
        countries: ['US', 'CA'],
      );

      // Act
      final modifiedProvider = originalProvider.copyWith(
        name: 'Modified Insurance',
        rating: 4.8,
        isFeatured: false,
      );

      // Assert
      expect(modifiedProvider.id, equals('provider-1')); // unchanged
      expect(modifiedProvider.name, equals('Modified Insurance')); // changed
      expect(modifiedProvider.rating, equals(4.8)); // changed
      expect(modifiedProvider.isFeatured, isFalse); // changed
      expect(modifiedProvider.reviewCount, equals(100)); // unchanged
    });

    test('should serialize to and from JSON correctly', () {
      // Arrange
      const provider = InsuranceProvider(
        id: 'provider-1',
        name: 'Test Insurance',
        description: 'Test insurance provider',
        logoUrl: 'https://example.com/logo.png',
        websiteUrl: 'https://example.com',
        phoneNumber: '******-123-4567',
        email: '<EMAIL>',
        rating: 4.5,
        reviewCount: 100,
        isFeatured: true,
        isPartner: false,
        countries: ['US', 'CA'],
        details: {'specialty': 'travel'},
      );

      // Act
      final json = provider.toJson();
      final deserializedProvider = InsuranceProvider.fromJson(json);

      // Assert
      expect(deserializedProvider.id, equals(provider.id));
      expect(deserializedProvider.name, equals(provider.name));
      expect(deserializedProvider.description, equals(provider.description));
      expect(deserializedProvider.logoUrl, equals(provider.logoUrl));
      expect(deserializedProvider.websiteUrl, equals(provider.websiteUrl));
      expect(deserializedProvider.phoneNumber, equals(provider.phoneNumber));
      expect(deserializedProvider.email, equals(provider.email));
      expect(deserializedProvider.rating, equals(provider.rating));
      expect(deserializedProvider.reviewCount, equals(provider.reviewCount));
      expect(deserializedProvider.isFeatured, equals(provider.isFeatured));
      expect(deserializedProvider.isPartner, equals(provider.isPartner));
      expect(deserializedProvider.countries, equals(provider.countries));
      expect(deserializedProvider.details, equals(provider.details));
    });

    test('should handle equality correctly', () {
      // Arrange
      const provider1 = InsuranceProvider(
        id: 'provider-1',
        name: 'Test Insurance',
        description: 'Test insurance provider',
        logoUrl: 'https://example.com/logo.png',
        websiteUrl: 'https://example.com',
        phoneNumber: '******-123-4567',
        email: '<EMAIL>',
        rating: 4.5,
        reviewCount: 100,
        isFeatured: true,
        isPartner: false,
        countries: ['US', 'CA'],
      );

      const provider2 = InsuranceProvider(
        id: 'provider-1',
        name: 'Test Insurance',
        description: 'Test insurance provider',
        logoUrl: 'https://example.com/logo.png',
        websiteUrl: 'https://example.com',
        phoneNumber: '******-123-4567',
        email: '<EMAIL>',
        rating: 4.5,
        reviewCount: 100,
        isFeatured: true,
        isPartner: false,
        countries: ['US', 'CA'],
      );

      const provider3 = InsuranceProvider(
        id: 'provider-2',
        name: 'Different Insurance',
        description: 'Different insurance provider',
        logoUrl: 'https://example.com/logo2.png',
        websiteUrl: 'https://example2.com',
        phoneNumber: '******-123-4568',
        email: '<EMAIL>',
        rating: 4.0,
        reviewCount: 50,
        isFeatured: false,
        isPartner: true,
        countries: ['UK', 'FR'],
      );

      // Act & Assert
      expect(provider1, equals(provider2));
      expect(provider1.hashCode, equals(provider2.hashCode));
      expect(provider1, isNot(equals(provider3)));
      expect(provider1.hashCode, isNot(equals(provider3.hashCode)));
    });

    test('should validate rating range', () {
      // Test valid ratings
      expect(
          () => const InsuranceProvider(
                id: 'provider-1',
                name: 'Test Insurance',
                description: 'Test insurance provider',
                logoUrl: 'https://example.com/logo.png',
                websiteUrl: 'https://example.com',
                phoneNumber: '******-123-4567',
                email: '<EMAIL>',
                rating: 0.0,
                reviewCount: 100,
                isFeatured: true,
                isPartner: false,
                countries: ['US'],
              ),
          returnsNormally);

      expect(
          () => const InsuranceProvider(
                id: 'provider-1',
                name: 'Test Insurance',
                description: 'Test insurance provider',
                logoUrl: 'https://example.com/logo.png',
                websiteUrl: 'https://example.com',
                phoneNumber: '******-123-4567',
                email: '<EMAIL>',
                rating: 5.0,
                reviewCount: 100,
                isFeatured: true,
                isPartner: false,
                countries: ['US'],
              ),
          returnsNormally);
    });
  });

  group('InsuranceCoverage', () {
    test('should create InsuranceCoverage with required fields', () {
      // Arrange & Act
      const coverage = InsuranceCoverage(
        id: 'coverage-1',
        type: InsuranceCoverageType.medical,
        amount: 50000.0,
        currency: 'USD',
        isIncluded: true,
      );

      // Assert
      expect(coverage.id, equals('coverage-1'));
      expect(coverage.type, equals(InsuranceCoverageType.medical));
      expect(coverage.amount, equals(50000.0));
      expect(coverage.currency, equals('USD'));
      expect(coverage.isIncluded, isTrue);
      expect(coverage.deductible, isNull);
      expect(coverage.maximumBenefit, isNull);
      expect(coverage.details, isEmpty);
    });

    test('should create InsuranceCoverage with optional fields', () {
      // Arrange & Act
      const coverage = InsuranceCoverage(
        id: 'coverage-1',
        type: InsuranceCoverageType.medical,
        amount: 50000.0,
        currency: 'USD',
        deductible: 500.0,
        maximumBenefit: 100000.0,
        isIncluded: true,
        details: {'network': 'global', 'preApproval': 'required'},
      );

      // Assert
      expect(coverage.deductible, equals(500.0));
      expect(coverage.maximumBenefit, equals(100000.0));
      expect(coverage.details,
          equals({'network': 'global', 'preApproval': 'required'}));
    });

    test('should format amounts correctly', () {
      // Arrange
      const coverage = InsuranceCoverage(
        id: 'coverage-1',
        type: InsuranceCoverageType.medical,
        amount: 50000.0,
        currency: 'USD',
        deductible: 500.0,
        maximumBenefit: 100000.0,
        isIncluded: true,
      );

      // Act & Assert
      expect(coverage.formattedAmount, equals('USD50000.00'));
      expect(coverage.formattedDeductible, equals('USD500.00'));
      expect(coverage.formattedMaximumBenefit, equals('USD100000.00'));
    });

    test('should handle null deductible and maximum benefit formatting', () {
      // Arrange
      const coverage = InsuranceCoverage(
        id: 'coverage-1',
        type: InsuranceCoverageType.medical,
        amount: 50000.0,
        currency: 'USD',
        isIncluded: true,
      );

      // Act & Assert
      expect(coverage.formattedDeductible, isNull);
      expect(coverage.formattedMaximumBenefit, isNull);
    });

    test('should create copy with modified fields', () {
      // Arrange
      const originalCoverage = InsuranceCoverage(
        id: 'coverage-1',
        type: InsuranceCoverageType.medical,
        amount: 50000.0,
        currency: 'USD',
        isIncluded: true,
      );

      // Act
      final modifiedCoverage = originalCoverage.copyWith(
        amount: 75000.0,
        deductible: 1000.0,
        isIncluded: false,
      );

      // Assert
      expect(modifiedCoverage.id, equals('coverage-1')); // unchanged
      expect(modifiedCoverage.type,
          equals(InsuranceCoverageType.medical)); // unchanged
      expect(modifiedCoverage.amount, equals(75000.0)); // changed
      expect(modifiedCoverage.deductible, equals(1000.0)); // changed
      expect(modifiedCoverage.isIncluded, isFalse); // changed
      expect(modifiedCoverage.currency, equals('USD')); // unchanged
    });

    test('should serialize to and from JSON correctly', () {
      // Arrange
      const coverage = InsuranceCoverage(
        id: 'coverage-1',
        type: InsuranceCoverageType.medical,
        amount: 50000.0,
        currency: 'USD',
        deductible: 500.0,
        maximumBenefit: 100000.0,
        isIncluded: true,
        details: {'network': 'global'},
      );

      // Act
      final json = coverage.toJson();
      final deserializedCoverage = InsuranceCoverage.fromJson(json);

      // Assert
      expect(deserializedCoverage.id, equals(coverage.id));
      expect(deserializedCoverage.type, equals(coverage.type));
      expect(deserializedCoverage.amount, equals(coverage.amount));
      expect(deserializedCoverage.currency, equals(coverage.currency));
      expect(deserializedCoverage.deductible, equals(coverage.deductible));
      expect(
          deserializedCoverage.maximumBenefit, equals(coverage.maximumBenefit));
      expect(deserializedCoverage.isIncluded, equals(coverage.isIncluded));
      expect(deserializedCoverage.details, equals(coverage.details));
    });

    test('should handle equality correctly', () {
      // Arrange
      const coverage1 = InsuranceCoverage(
        id: 'coverage-1',
        type: InsuranceCoverageType.medical,
        amount: 50000.0,
        currency: 'USD',
        isIncluded: true,
      );

      const coverage2 = InsuranceCoverage(
        id: 'coverage-1',
        type: InsuranceCoverageType.medical,
        amount: 50000.0,
        currency: 'USD',
        isIncluded: true,
      );

      const coverage3 = InsuranceCoverage(
        id: 'coverage-2',
        type: InsuranceCoverageType.cancellation,
        amount: 25000.0,
        currency: 'EUR',
        isIncluded: false,
      );

      // Act & Assert
      expect(coverage1, equals(coverage2));
      expect(coverage1.hashCode, equals(coverage2.hashCode));
      expect(coverage1, isNot(equals(coverage3)));
      expect(coverage1.hashCode, isNot(equals(coverage3.hashCode)));
    });
  });
}
