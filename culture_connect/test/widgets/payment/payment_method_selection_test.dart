import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/services/payment_service.dart';
import 'package:culture_connect/widgets/payment/payment_method_selection.dart';

class MockPaymentService extends Mock implements PaymentService {
  @override
  Future<List<PaymentMethodModel>> getSavedPaymentMethods() async {
    return [
      const PaymentMethodModel(
        id: '1',
        type: PaymentMethodType.creditCard,
        name: 'Visa ending in 4242',
        isDefault: true,
        details: {'last4': '4242'},
      ),
      const PaymentMethodModel(
        id: '2',
        type: PaymentMethodType.paypal,
        name: 'PayPal',
        isDefault: false,
        details: {},
      ),
    ];
  }
}

void main() {
  late MockPaymentService mockPaymentService;

  setUp(() {
    mockPaymentService = MockPaymentService();
    // Note: In a real implementation, we would use dependency injection
    // or provider overrides to inject the mock service
  });

  group('PaymentMethodSelection Widget Tests', () {
    testWidgets('renders payment methods', (WidgetTester tester) async {
      // Define a callback for payment method selection
      PaymentMethodModel? selectedMethod;
      void onPaymentMethodSelected(PaymentMethodModel method) {
        selectedMethod = method;
      }

      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PaymentMethodSelection(
                onPaymentMethodSelected: onPaymentMethodSelected,
              ),
            ),
          ),
        ),
      );

      // Initially shows loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for the payment methods to load
      await tester.pump(const Duration(seconds: 1));

      // Verify that payment methods are displayed
      expect(find.text('Visa ending in 4242'), findsOneWidget);
      expect(find.text('PayPal'), findsOneWidget);

      // Verify that the default method is selected
      expect(selectedMethod, isNotNull);
      expect(selectedMethod!.id, '1');
      expect(selectedMethod!.isDefault, isTrue);

      // Tap on the second payment method
      await tester.tap(find.text('PayPal'));
      await tester.pump();

      // Verify that the selected method has changed
      expect(selectedMethod!.id, '2');
      expect(selectedMethod!.name, 'PayPal');
    });

    testWidgets('shows empty state when no payment methods',
        (WidgetTester tester) async {
      // Override the mock to return an empty list
      when(mockPaymentService.getSavedPaymentMethods())
          .thenAnswer((_) async => []);

      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PaymentMethodSelection(
                onPaymentMethodSelected: (_) {},
              ),
            ),
          ),
        ),
      );

      // Wait for the payment methods to load
      await tester.pump(const Duration(seconds: 1));

      // Verify that empty state is displayed
      expect(find.text('No payment methods found'), findsOneWidget);
      expect(find.text('Add a payment method to continue'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('shows add button when showAddButton is true',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PaymentMethodSelection(
                onPaymentMethodSelected: (_) {},
                showAddButton: true,
              ),
            ),
          ),
        ),
      );

      // Wait for the payment methods to load
      await tester.pump(const Duration(seconds: 1));

      // Verify that add button is displayed
      expect(find.text('Add New'), findsOneWidget);
    });

    testWidgets('hides add button when showAddButton is false',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PaymentMethodSelection(
                onPaymentMethodSelected: (_) {},
                showAddButton: false,
              ),
            ),
          ),
        ),
      );

      // Wait for the payment methods to load
      await tester.pump(const Duration(seconds: 1));

      // Verify that add button is not displayed
      expect(find.text('Add New'), findsNothing);
    });
  });
}
