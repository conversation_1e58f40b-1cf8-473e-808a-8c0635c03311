import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/widgets/travel/timeline/ar_timeline_view.dart';

void main() {
  // Sample timeline data
  final sampleTimeline = Timeline(
    id: 'timeline-1',
    userId: 'user-1',
    title: 'My Paris Trip',
    description: 'A wonderful trip to Paris',
    startDate: DateTime(2023, 6, 1),
    endDate: DateTime(2023, 6, 7),
    events: [
      TimelineEvent(
        id: 'event-1',
        title: 'Eiffel Tower Visit',
        description: 'Visit the iconic Eiffel Tower',
        eventDate: DateTime(2023, 6, 2),
        eventTime: const TimeOfDay(hour: 10, minute: 0),
        location: 'Eiffel Tower, Paris',
        coordinates: {'lat': 48.8584, 'lng': 2.2945},
        eventType: 'sightseeing',
        hasARContent: true,
        arContentId: 'ar-content-1',
      ),
      TimelineEvent(
        id: 'event-2',
        title: 'Louvre Museum',
        description: 'Explore the famous Louvre Museum',
        eventDate: DateTime(2023, 6, 3),
        eventTime: const TimeOfDay(hour: 13, minute: 0),
        location: 'Louvre Museum, Paris',
        coordinates: {'lat': 48.8606, 'lng': 2.3376},
        eventType: 'museum',
        hasARContent: false,
      ),
    ],
    theme: TimelineTheme.standard,
  );

  testWidgets(
      'ARTimelineView displays timeline header with correct information',
      (WidgetTester tester) async {
    // Arrange

    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineView(
            timeline: sampleTimeline,
            showARContentOnly: false,
            onEventTap: (_) {},
            onARContentTap: (_) {},
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Assert
    expect(find.text('My Paris Trip'), findsOneWidget);
    expect(find.text('A wonderful trip to Paris'), findsOneWidget);
    expect(find.text('Jun 1, 2023 - Jun 7, 2023'), findsOneWidget);
    expect(find.text('1 AR Experiences'), findsOneWidget);
  });

  testWidgets('ARTimelineView displays date selector with AR indicators',
      (WidgetTester tester) async {
    // Arrange
    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineView(
            timeline: sampleTimeline,
            showARContentOnly: false,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Assert
    // Find the date selector
    expect(find.text('Jun'), findsWidgets); // Month abbreviation
    expect(find.text('1'), findsWidgets); // Day number
    expect(find.text('2'), findsWidgets); // Day number

    // Find AR indicator for the date with AR content
    final arIndicatorFinder = find.byIcon(Icons.view_in_ar);
    expect(arIndicatorFinder, findsWidgets);
  });

  testWidgets('ARTimelineView displays events with correct information',
      (WidgetTester tester) async {
    // Arrange
    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineView(
            timeline: sampleTimeline,
            showARContentOnly: false,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Assert
    expect(find.text('Eiffel Tower Visit'), findsOneWidget);
    expect(find.text('Louvre Museum'), findsOneWidget);
    expect(find.text('10:00'), findsOneWidget); // Event time
    expect(find.text('13:00'), findsOneWidget); // Event time
    expect(find.text('Eiffel Tower, Paris'), findsOneWidget); // Location
    expect(find.text('Louvre Museum, Paris'), findsOneWidget); // Location
  });

  testWidgets(
      'ARTimelineView filters events with AR content when showARContentOnly is true',
      (WidgetTester tester) async {
    // Arrange
    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineView(
            timeline: sampleTimeline,
            showARContentOnly: true, // Show only AR content
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Assert
    expect(find.text('Eiffel Tower Visit'),
        findsOneWidget); // Event with AR content
    expect(find.text('Louvre Museum'),
        findsNothing); // Event without AR content should be filtered out
  });

  testWidgets('ARTimelineView calls onEventTap when an event is tapped',
      (WidgetTester tester) async {
    // Arrange
    TimelineEvent? tappedEvent;

    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineView(
            timeline: sampleTimeline,
            showARContentOnly: false,
            onEventTap: (event) => tappedEvent = event,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Find and tap the event card
    final eventCardFinder = find.text('Eiffel Tower Visit');
    expect(eventCardFinder, findsOneWidget);
    await tester.tap(eventCardFinder);
    await tester.pumpAndSettle();

    // Assert
    expect(tappedEvent, isNotNull);
    expect(tappedEvent?.title, equals('Eiffel Tower Visit'));
  });

  testWidgets(
      'ARTimelineView calls onARContentTap when AR content badge is tapped',
      (WidgetTester tester) async {
    // Arrange
    TimelineEvent? tappedAREvent;

    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineView(
            timeline: sampleTimeline,
            showARContentOnly: false,
            onARContentTap: (event) => tappedAREvent = event,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Find and tap the AR content badge
    final arBadgeFinder = find.byIcon(Icons.view_in_ar);
    expect(arBadgeFinder, findsWidgets);
    await tester.tap(arBadgeFinder.first);
    await tester.pumpAndSettle();

    // Assert
    expect(tappedAREvent, isNotNull);
    expect(tappedAREvent?.title, equals('Eiffel Tower Visit'));
    expect(tappedAREvent?.hasARContent, isTrue);
  });

  testWidgets('ARTimelineView displays empty state when timeline has no events',
      (WidgetTester tester) async {
    // Arrange
    final emptyTimeline = Timeline(
      id: 'timeline-empty',
      userId: 'user-1',
      title: 'Empty Timeline',
      startDate: DateTime(2023, 6, 1),
      endDate: DateTime(2023, 6, 7),
      events: [], // No events
      theme: TimelineTheme.standard,
    );

    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineView(
            timeline: emptyTimeline,
            showARContentOnly: false,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Assert
    expect(find.text('No events in this timeline'), findsOneWidget);
    expect(find.text('Add events to your timeline to get started'),
        findsOneWidget);
  });

  testWidgets(
      'ARTimelineView displays AR-specific empty state when no AR events and filter is on',
      (WidgetTester tester) async {
    // Arrange
    final noARTimeline = Timeline(
      id: 'timeline-no-ar',
      userId: 'user-1',
      title: 'No AR Timeline',
      startDate: DateTime(2023, 6, 1),
      endDate: DateTime(2023, 6, 7),
      events: [
        TimelineEvent(
          id: 'event-1',
          title: 'Regular Event',
          eventDate: DateTime(2023, 6, 2),
          eventType: 'regular',
          hasARContent: false,
        ),
      ],
      theme: TimelineTheme.standard,
    );

    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineView(
            timeline: noARTimeline,
            showARContentOnly: true, // Show only AR content
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Assert
    expect(find.text('No AR content in this timeline'), findsOneWidget);
    expect(find.text('Add AR content to your events to see them here'),
        findsOneWidget);
  });
}
