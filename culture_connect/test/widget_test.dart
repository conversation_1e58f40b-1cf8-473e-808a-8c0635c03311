// CultureConnect Widget Tests
//
// This file contains basic widget tests for the CultureConnect app.
// It tests the main app initialization and core functionality.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/main.dart';

void main() {
  group('CultureConnect App Tests', () {
    testWidgets('App initializes correctly', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        const ProviderScope(
          child: CultureConnectApp(),
        ),
      );

      // Wait for the app to settle
      await tester.pumpAndSettle();

      // Verify that the app loads without errors
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('App shows splash screen initially',
        (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        const ProviderScope(
          child: CultureConnectApp(),
        ),
      );

      // Verify that splash screen is shown initially
      expect(find.byType(MaterialApp), findsOneWidget);

      // The app should not crash during initialization
      expect(tester.takeException(), isNull);
    });

    testWidgets('App handles theme correctly', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        const ProviderScope(
          child: CultureConnectApp(),
        ),
      );

      // Wait for the app to settle
      await tester.pumpAndSettle();

      // Verify that the app has a theme
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.theme, isNotNull);
    });
  });
}
