import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:network_image_mock/network_image_mock.dart';
import 'package:culture_connect/widgets/experience_card.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

void main() {
  // Sample experience for testing
  final sampleExperience = Experience(
    id: '1',
    title: 'Cultural Tour',
    description: 'Explore local culture',
    imageUrl: 'https://example.com/image1.jpg',
    category: 'Cultural Tours',
    location: 'San Francisco, CA',
    coordinates: const LatLng(37.7749, -122.4194),
    price: 99.99,
    rating: 4.5,
    reviewCount: 100,
    guideId: 'guide1',
    guideName: '<PERSON>',
    guideImageUrl: 'https://example.com/guide1.jpg',
    languages: const ['English', 'Spanish'],
    includedItems: const ['Transportation', 'Guide'],
    requirements: const ['Basic fitness level'],
    createdAt: DateTime(2023, 1, 1),
    updatedAt: DateTime(2023, 1, 1),
  );

  testWidgets('ExperienceCard should render correctly in vertical orientation',
      (WidgetTester tester) async {
    // Mock network images
    await mockNetworkImagesFor(() async {
      // Build our widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExperienceCard(
              title: sampleExperience.title,
              imageUrl: sampleExperience.imageUrl,
              rating: sampleExperience.rating,
              reviewCount: sampleExperience.reviewCount,
              price: sampleExperience.price.toString(),
              category: sampleExperience.category,
              location: sampleExperience.location,
              isHorizontal: false,
              onTap: () {},
            ),
          ),
        ),
      );

      // Wait for widget to build
      await tester.pumpAndSettle();

      // Verify that the card renders correctly
      expect(find.text('Cultural Tour'), findsOneWidget);
      expect(find.text('Cultural Tours'), findsOneWidget);
      expect(find.text('San Francisco, CA'), findsOneWidget);
      expect(find.text('4.5'), findsOneWidget);
      expect(find.text('(100)'), findsOneWidget);
      expect(find.text('\$99.99'), findsOneWidget);
    });
  });

  testWidgets(
      'ExperienceCard should render correctly in horizontal orientation',
      (WidgetTester tester) async {
    // Mock network images
    await mockNetworkImagesFor(() async {
      // Build our widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExperienceCard(
              title: sampleExperience.title,
              imageUrl: sampleExperience.imageUrl,
              rating: sampleExperience.rating,
              reviewCount: sampleExperience.reviewCount,
              price: sampleExperience.price.toString(),
              category: sampleExperience.category,
              location: sampleExperience.location,
              isHorizontal: true,
              onTap: () {},
            ),
          ),
        ),
      );

      // Wait for widget to build
      await tester.pumpAndSettle();

      // Verify that the card renders correctly
      expect(find.text('Cultural Tour'), findsOneWidget);
      expect(find.text('Cultural Tours'), findsOneWidget);
      expect(find.text('San Francisco, CA'), findsOneWidget);
      expect(find.text('4.5'), findsOneWidget);
      expect(find.text('(100)'), findsOneWidget);
      expect(find.text('\$99.99'), findsOneWidget);
    });
  });

  testWidgets('ExperienceCard should call onTap when tapped',
      (WidgetTester tester) async {
    // Track if onTap was called
    bool onTapCalled = false;

    // Mock network images
    await mockNetworkImagesFor(() async {
      // Build our widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExperienceCard(
              title: sampleExperience.title,
              imageUrl: sampleExperience.imageUrl,
              rating: sampleExperience.rating,
              reviewCount: sampleExperience.reviewCount,
              price: sampleExperience.price.toString(),
              category: sampleExperience.category,
              location: sampleExperience.location,
              isHorizontal: false,
              onTap: () {
                onTapCalled = true;
              },
            ),
          ),
        ),
      );

      // Wait for widget to build
      await tester.pumpAndSettle();

      // Tap the card
      await tester.tap(find.byType(ExperienceCard));

      // Verify that onTap was called
      expect(onTapCalled, isTrue);
    });
  });

  testWidgets('ExperienceCard should handle missing image',
      (WidgetTester tester) async {
    // Mock network images
    await mockNetworkImagesFor(() async {
      // Build our widget with empty image URL
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExperienceCard(
              title: sampleExperience.title,
              imageUrl: '',
              rating: sampleExperience.rating,
              reviewCount: sampleExperience.reviewCount,
              price: sampleExperience.price.toString(),
              category: sampleExperience.category,
              location: sampleExperience.location,
              isHorizontal: false,
              onTap: () {},
            ),
          ),
        ),
      );

      // Wait for widget to build
      await tester.pumpAndSettle();

      // Verify that the card still renders correctly
      expect(find.text('Cultural Tour'), findsOneWidget);
      expect(find.text('Cultural Tours'), findsOneWidget);
      expect(find.text('San Francisco, CA'), findsOneWidget);
      expect(find.text('4.5'), findsOneWidget);
      expect(find.text('(100)'), findsOneWidget);
      expect(find.text('\$99.99'), findsOneWidget);
    });
  });

  testWidgets('ExperienceCard should handle zero rating and review count',
      (WidgetTester tester) async {
    // Mock network images
    await mockNetworkImagesFor(() async {
      // Build our widget with zero rating and review count
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExperienceCard(
              title: sampleExperience.title,
              imageUrl: sampleExperience.imageUrl,
              rating: 0,
              reviewCount: 0,
              price: sampleExperience.price.toString(),
              category: sampleExperience.category,
              location: sampleExperience.location,
              isHorizontal: false,
              onTap: () {},
            ),
          ),
        ),
      );

      // Wait for widget to build
      await tester.pumpAndSettle();

      // Verify that the card still renders correctly
      expect(find.text('Cultural Tour'), findsOneWidget);
      expect(find.text('Cultural Tours'), findsOneWidget);
      expect(find.text('San Francisco, CA'), findsOneWidget);
      expect(find.text('0.0'), findsOneWidget);
      expect(find.text('(0)'), findsOneWidget);
      expect(find.text('\$99.99'), findsOneWidget);
    });
  });

  testWidgets('ExperienceCard should handle long title and location',
      (WidgetTester tester) async {
    // Mock network images
    await mockNetworkImagesFor(() async {
      // Build our widget with long title and location
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExperienceCard(
              title:
                  'This is a very long title that should be truncated because it exceeds the available space',
              imageUrl: sampleExperience.imageUrl,
              rating: sampleExperience.rating,
              reviewCount: sampleExperience.reviewCount,
              price: sampleExperience.price.toString(),
              category: sampleExperience.category,
              location:
                  'This is a very long location that should be truncated because it exceeds the available space',
              isHorizontal: false,
              onTap: () {},
            ),
          ),
        ),
      );

      // Wait for widget to build
      await tester.pumpAndSettle();

      // Verify that the card still renders correctly
      expect(find.textContaining('This is a very long title'), findsOneWidget);
      expect(find.text('Cultural Tours'), findsOneWidget);
      expect(
          find.textContaining('This is a very long location'), findsOneWidget);
      expect(find.text('4.5'), findsOneWidget);
      expect(find.text('(100)'), findsOneWidget);
      expect(find.text('\$99.99'), findsOneWidget);
    });
  });

  testWidgets('ExperienceCard should handle different price formats',
      (WidgetTester tester) async {
    // Mock network images
    await mockNetworkImagesFor(() async {
      // Build our widget with different price format
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExperienceCard(
              title: sampleExperience.title,
              imageUrl: sampleExperience.imageUrl,
              rating: sampleExperience.rating,
              reviewCount: sampleExperience.reviewCount,
              price: 'Free',
              category: sampleExperience.category,
              location: sampleExperience.location,
              isHorizontal: false,
              onTap: () {},
            ),
          ),
        ),
      );

      // Wait for widget to build
      await tester.pumpAndSettle();

      // Verify that the card still renders correctly
      expect(find.text('Cultural Tour'), findsOneWidget);
      expect(find.text('Cultural Tours'), findsOneWidget);
      expect(find.text('San Francisco, CA'), findsOneWidget);
      expect(find.text('4.5'), findsOneWidget);
      expect(find.text('(100)'), findsOneWidget);
      expect(find.text('Free'), findsOneWidget);
    });
  });
}
