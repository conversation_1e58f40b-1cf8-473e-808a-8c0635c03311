import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/screens/splash_screen.dart';

void main() {
  testWidgets('SplashScreen should render without errors',
      (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      const ProviderScope(
        child: MaterialApp(
          home: SplashScreen(),
        ),
      ),
    );

    // Wait for widget to build
    await tester.pumpAndSettle();

    // Verify that the screen renders without errors
    expect(find.byType(SplashScreen), findsOneWidget);
  });
}
