import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/screens/ar/ar_explore_screen.dart';

void main() {
  testWidgets('ARExploreScreen should render correctly',
      (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      const ProviderScope(
        child: MaterialApp(
          home: ARExploreScreen(),
        ),
      ),
    );

    // Wait for widget to build
    await tester.pumpAndSettle();

    // Verify that the screen renders without errors
    expect(find.byType(ARExploreScreen), findsOneWidget);
  });
}
